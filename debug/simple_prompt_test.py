#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的提示词功能测试
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_prompt_service():
    """测试提示词服务"""
    try:
        from services.prompt_service import PromptService
        
        print("正在测试提示词服务...")
        
        # 创建提示词服务实例
        prompt_service = PromptService()
        
        # 测试章节分析提示词
        section_prompt = prompt_service.get_section_analysis_prompt(
            chapter_outline="1. 概述\n1.1 项目概况",
            criteria_text="审查细则1：项目概况应包括项目名称",
            review_guide="审查指南：请按照国家标准评审"
        )
        
        print(f"✓ 章节分析提示词生成成功，长度: {len(section_prompt)} 字符")
        
        # 测试批量综合分析提示词
        batch_prompt = prompt_service.get_comprehensive_batch_prompt(
            outline="编制大纲内容"
        )
        
        print(f"✓ 批量分析提示词生成成功，长度: {len(batch_prompt)} 字符")
        
        # 测试汇总评审提示词
        summary_prompt = prompt_service.get_summary_review_prompt(
            review_results=["测试结果1", "测试结果2"]
        )
        
        print(f"✓ 汇总评审提示词生成成功，长度: {len(summary_prompt)} 字符")
        
        print("🎉 提示词服务测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 提示词服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_service():
    """测试ModelService集成"""
    try:
        from services.model_service import ModelService
        
        print("正在测试ModelService集成...")
        
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        # 创建ModelService实例
        model_service = ModelService()
        
        # 测试系统提示词生成
        sys_prompt = model_service._get_system_prompt(
            chapter_outline="1. 概述\n1.1 项目概况",
            criteria_text="审查细则1：项目概况应包括项目名称",
            review_guide="审查指南：请按照国家标准评审"
        )
        
        print(f"✓ 系统提示词生成成功，长度: {len(sys_prompt)} 字符")
        
        # 检查关键内容
        if "当前章节编制大纲要求" in sys_prompt and "审查指南" in sys_prompt:
            print("✓ 系统提示词包含必要内容")
        else:
            print("❌ 系统提示词缺少关键内容")
            return False
        
        print("🎉 ModelService集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ ModelService集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始简单提示词功能测试...")
    
    results = []
    
    # 测试提示词服务
    results.append(test_prompt_service())
    
    # 测试ModelService集成
    results.append(test_model_service())
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
