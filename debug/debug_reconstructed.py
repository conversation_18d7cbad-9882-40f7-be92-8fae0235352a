from services.document_parser import DocumentParser
import PyPDF2

def debug_reconstructed_text(pdf_path):
    """调试重构后的文本"""
    print(f"调试重构文本: {pdf_path}")
    
    parser = DocumentParser()
    
    # 提取原始文本
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        all_text = ""
        for page in reader.pages:
            try:
                text = page.extract_text()
                if text.strip():
                    all_text += text + "\n"
            except Exception as e:
                print(f"页面文本提取失败: {e}")
                continue
    
    # 应用文本修复
    fixed_text = parser._fix_text_formatting(all_text)
    lines = fixed_text.split('\n')
    
    print(f"重构后共 {len(lines)} 行")
    print("\n重构后的前100行:")
    for i, line in enumerate(lines[:100]):
        if line.strip():
            print(f"{i+1:3d}: {line}")
    
    # 查找章节标题
    print("\n查找章节标题:")
    for i, line in enumerate(lines):
        is_title, chapter_title = parser.is_chapter_title(line)
        if is_title:
            print(f"行 {i+1}: {chapter_title} (原文: {line})")

# 调试问题文件
debug_reconstructed_text("uploads/test_可行性研究报告.pdf")
