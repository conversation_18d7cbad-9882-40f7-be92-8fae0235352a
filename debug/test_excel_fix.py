from services.document_parser import DocumentParser

print("=== 测试Excel解析修复 ===")

# 测试修复后的Excel解析
parser = DocumentParser()
criteria = parser.parse_review_criteria("templates/中央预算投资项目审核表.xlsx")

print(f"\n✅ 解析结果:")
print(f"总审查细则数: {len(criteria)}")

print(f"\n📋 前10个审查细则:")
for i, criterion in enumerate(criteria[:10]):
    print(f"{i+1:2d}. ID: {criterion['id']:<8} | 原始ID: {criterion.get('original_id', 'N/A'):<5} | 内容: {criterion['content'][:40]}...")

print(f"\n📋 后10个审查细则:")
for i, criterion in enumerate(criteria[-10:], len(criteria)-9):
    print(f"{i:2d}. ID: {criterion['id']:<8} | 原始ID: {criterion.get('original_id', 'N/A'):<5} | 内容: {criterion['content'][:40]}...")

# 检查是否有重复的ID
ids = [c['id'] for c in criteria]
unique_ids = set(ids)
print(f"\n🔍 ID唯一性检查:")
print(f"总ID数: {len(ids)}")
print(f"唯一ID数: {len(unique_ids)}")
if len(ids) == len(unique_ids):
    print("✅ 所有ID都是唯一的")
else:
    print("❌ 发现重复ID")
    from collections import Counter
    id_counts = Counter(ids)
    duplicates = {id_val: count for id_val, count in id_counts.items() if count > 1}
    for id_val, count in duplicates.items():
        print(f"  重复ID: {id_val} (出现{count}次)")

# 检查内容质量
print(f"\n📊 内容质量检查:")
valid_content_count = 0
for criterion in criteria:
    content = criterion['content']
    if content and content not in ['nan', 'NaN', '未指定', '']:
        valid_content_count += 1

print(f"有效内容数: {valid_content_count}/{len(criteria)}")
print(f"有效率: {valid_content_count/len(criteria)*100:.1f}%")

print(f"\n🎯 预期结果:")
print(f"- 应该有21个审查细则")
print(f"- 所有ID应该是唯一的")
print(f"- 所有审查细则都应该有有效内容")
print(f"- 实际结果: {len(criteria)}个审查细则，{len(unique_ids)}个唯一ID，{valid_content_count}个有效内容")

if len(criteria) >= 20 and len(ids) == len(unique_ids) and valid_content_count >= 20:
    print("\n🎉 Excel解析修复成功！")
else:
    print("\n⚠️  还需要进一步调整")
