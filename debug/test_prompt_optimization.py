#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
提示词优化功能测试
测试新的提示词管理服务和模板功能
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.prompt_service import PromptService
from services.model_service import ModelService


def test_prompt_service():
    """测试提示词服务基本功能"""
    print("\n=== 测试提示词服务基本功能 ===")
    
    try:
        prompt_service = PromptService()
        
        # 测试获取模板信息
        template_info = prompt_service.get_template_info()
        print(f"模板信息: {json.dumps(template_info, indent=2, ensure_ascii=False)}")
        
        # 测试章节分析提示词
        section_prompt = prompt_service.get_section_analysis_prompt(
            chapter_outline="1. 概述\n1.1 项目概况\n1.2 编制依据",
            criteria_text="审查细则1：项目概况应包括项目名称、建设地点等基本信息",
            review_guide="审查指南：请按照国家相关标准进行评审"
        )
        print(f"\n章节分析提示词长度: {len(section_prompt)} 字符")
        print(f"章节分析提示词预览: {section_prompt[:200]}...")
        
        # 测试批量综合分析提示词
        batch_prompt = prompt_service.get_comprehensive_batch_prompt(
            outline="可研报告编制大纲内容..."
        )
        print(f"\n批量分析提示词长度: {len(batch_prompt)} 字符")
        print(f"批量分析提示词预览: {batch_prompt[:200]}...")
        
        # 测试汇总评审提示词
        summary_prompt = prompt_service.get_summary_review_prompt(
            review_results=["测试评审结果1", "测试评审结果2"]
        )
        print(f"\n汇总评审提示词长度: {len(summary_prompt)} 字符")
        print(f"汇总评审提示词预览: {summary_prompt[:200]}...")
        
        print("✓ 提示词服务基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 提示词服务测试失败: {e}")
        return False


def test_model_service_integration():
    """测试ModelService与提示词服务的集成"""
    print("\n=== 测试ModelService与提示词服务集成 ===")
    
    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        model_service = ModelService()
        
        # 测试_get_system_prompt方法
        sys_prompt = model_service._get_system_prompt(
            chapter_outline="1. 概述\n1.1 项目概况\n1.2 编制依据",
            criteria_text="审查细则1：项目概况应包括项目名称、建设地点等基本信息",
            review_guide="审查指南：请按照国家相关标准进行评审"
        )
        
        print(f"系统提示词长度: {len(sys_prompt)} 字符")
        print(f"系统提示词预览: {sys_prompt[:300]}...")
        
        # 检查是否包含关键内容
        if "当前章节编制大纲要求" in sys_prompt and "审查指南" in sys_prompt:
            print("✓ 系统提示词包含章节大纲和审查指南")
        else:
            print("✗ 系统提示词缺少关键内容")
            return False
        
        print("✓ ModelService集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ ModelService集成测试失败: {e}")
        return False


def test_template_variables():
    """测试模板变量替换功能"""
    print("\n=== 测试模板变量替换功能 ===")
    
    try:
        prompt_service = PromptService()
        
        # 测试空值处理
        section_prompt = prompt_service.get_section_analysis_prompt(
            chapter_outline=None,
            criteria_text="",
            review_guide="   "  # 空白字符串
        )
        
        # 检查空值是否被正确处理
        if "未提供" in section_prompt:
            print("✓ 空值处理正确")
        else:
            print("✗ 空值处理失败")
            return False
        
        # 测试系统变量
        template_info = prompt_service.get_template_info()
        system_vars = template_info["system_variables"]
        
        if "current_time" in system_vars and "model_name" in system_vars:
            print(f"✓ 系统变量正确: {system_vars}")
        else:
            print("✗ 系统变量缺失")
            return False
        
        print("✓ 模板变量替换测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模板变量替换测试失败: {e}")
        return False


def test_template_cache():
    """测试模板缓存功能"""
    print("\n=== 测试模板缓存功能 ===")
    
    try:
        prompt_service = PromptService()
        
        # 第一次加载
        prompt1 = prompt_service.get_section_analysis_prompt()
        
        # 第二次加载（应该使用缓存）
        prompt2 = prompt_service.get_section_analysis_prompt()
        
        if prompt1 == prompt2:
            print("✓ 模板缓存工作正常")
        else:
            print("✗ 模板缓存失败")
            return False
        
        # 测试缓存清理
        prompt_service.clear_cache()
        template_info = prompt_service.get_template_info()
        
        if len(template_info["cached_templates"]) == 0:
            print("✓ 缓存清理成功")
        else:
            print("✗ 缓存清理失败")
            return False
        
        print("✓ 模板缓存测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模板缓存测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试不存在的模板文件
        os.environ["PROMPT_SECTION_ANALYSIS"] = "non_existent_template.txt"
        
        prompt_service = PromptService()
        
        try:
            prompt_service.get_section_analysis_prompt()
            print("✗ 应该抛出文件不存在错误")
            return False
        except FileNotFoundError:
            print("✓ 正确处理文件不存在错误")
        
        # 恢复正确的配置
        os.environ["PROMPT_SECTION_ANALYSIS"] = "templates/prompts/section_analysis_prompt.txt"
        
        print("✓ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始提示词优化功能测试...")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_prompt_service())
    test_results.append(test_model_service_integration())
    test_results.append(test_template_variables())
    test_results.append(test_template_cache())
    test_results.append(test_error_handling())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！提示词优化功能正常工作")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
