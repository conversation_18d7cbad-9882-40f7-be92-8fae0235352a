#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化2功能
包括：
1. 上传报告时，报告名称可以由文件名称自动提取
2. 报告管理界面，报告列表中显示报告的专题名称
3. 评审管理界面，可选择多个报告进行批量评审
4. 评审管理界面，报告专题的下拉过滤框在前
"""

import os
import sys
import json
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_auto_fill_report_name():
    """测试自动填充报告名称功能"""
    print("=" * 50)
    print("测试1: 自动填充报告名称功能")
    print("=" * 50)
    
    # 这个功能是前端JavaScript实现的，我们可以模拟测试
    test_filenames = [
        "某某项目可行性研究报告.pdf",
        "2024年度投资项目评估.pdf",
        "基础设施建设可研报告_v2.pdf",
        "test_report.pdf"
    ]
    
    for filename in test_filenames:
        # 模拟JavaScript的文件名处理逻辑
        name_without_ext = filename.replace('.pdf', '')
        print(f"文件名: {filename}")
        print(f"自动提取的报告名称: {name_without_ext}")
        print("-" * 30)
    
    print("✅ 自动填充报告名称功能测试完成")
    return True

def test_report_topic_display():
    """测试报告列表显示专题名称功能"""
    print("=" * 50)
    print("测试2: 报告列表显示专题名称功能")
    print("=" * 50)
    
    try:
        # 测试获取报告列表API
        response = requests.get("http://localhost:8001/api/reports")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                reports = result.get('data', [])
                print(f"获取到 {len(reports)} 个报告")
                
                # 获取专题列表
                topic_response = requests.get("http://localhost:8001/api/topics")
                if topic_response.status_code == 200:
                    topic_result = topic_response.json()
                    if topic_result.get('success'):
                        topics = topic_result.get('data', [])
                        topic_dict = {topic['id']: topic['name'] for topic in topics}
                        
                        for report in reports[:3]:  # 只显示前3个报告
                            topic_name = topic_dict.get(report.get('topic_id', ''), '未分类')
                            print(f"报告: {report.get('name', 'N/A')}")
                            print(f"专题: {topic_name}")
                            print(f"创建时间: {report.get('created_at', 'N/A')}")
                            print("-" * 30)
                        
                        print("✅ 报告列表显示专题名称功能正常")
                        return True
                    else:
                        print("❌ 获取专题列表失败")
                        return False
                else:
                    print("❌ 专题API请求失败")
                    return False
            else:
                print("❌ 报告API返回失败")
                return False
        else:
            print("❌ 报告API请求失败")
            return False
    except Exception as e:
        print(f"❌ 测试报告专题显示功能失败: {e}")
        return False

def test_filter_order():
    """测试评审管理界面过滤器顺序"""
    print("=" * 50)
    print("测试3: 评审管理界面过滤器顺序")
    print("=" * 50)
    
    # 这个是前端HTML结构的测试，我们检查HTML文件
    html_file = project_root / "templates" / "review_management.html"
    
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查专题过滤器是否在报告过滤器之前
        topic_filter_pos = content.find('id="topicFilter"')
        report_filter_pos = content.find('id="reportFilter"')
        
        if topic_filter_pos < report_filter_pos and topic_filter_pos != -1:
            print("✅ 专题过滤框在报告过滤框之前，顺序正确")
            return True
        else:
            print("❌ 过滤框顺序不正确")
            return False
    else:
        print("❌ 找不到评审管理页面文件")
        return False

def test_batch_review_ui():
    """测试批量评审UI结构"""
    print("=" * 50)
    print("测试4: 批量评审UI结构")
    print("=" * 50)
    
    html_file = project_root / "templates" / "review_management.html"
    
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查批量评审相关的UI元素
        ui_elements = [
            'id="batchReportsList"',
            'onclick="selectAllReports()"',
            'onclick="clearAllReports()"',
            'onclick="startBatchReview()"',
            'id="selectedCount"'
        ]
        
        missing_elements = []
        for element in ui_elements:
            if element not in content:
                missing_elements.append(element)
        
        if not missing_elements:
            print("✅ 批量评审UI元素完整")
            
            # 检查JavaScript函数
            js_functions = [
                'function populateBatchReportsList()',
                'function toggleReportSelection(',
                'function selectAllReports()',
                'function clearAllReports()',
                'function startBatchReview()'
            ]
            
            missing_functions = []
            for func in js_functions:
                if func not in content:
                    missing_functions.append(func)
            
            if not missing_functions:
                print("✅ 批量评审JavaScript函数完整")
                return True
            else:
                print(f"❌ 缺少JavaScript函数: {missing_functions}")
                return False
        else:
            print(f"❌ 缺少UI元素: {missing_elements}")
            return False
    else:
        print("❌ 找不到评审管理页面文件")
        return False

def test_auto_fill_js_function():
    """测试自动填充JavaScript函数"""
    print("=" * 50)
    print("测试5: 自动填充JavaScript函数")
    print("=" * 50)
    
    html_file = project_root / "templates" / "report_management.html"
    
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查自动填充相关的代码
        if 'onchange="autoFillReportName()"' in content:
            print("✅ 文件输入框绑定了自动填充事件")
        else:
            print("❌ 文件输入框未绑定自动填充事件")
            return False
        
        if 'function autoFillReportName()' in content:
            print("✅ 自动填充函数存在")
        else:
            print("❌ 自动填充函数不存在")
            return False
        
        if 'nameWithoutExt = fileName.replace' in content:
            print("✅ 文件名处理逻辑正确")
        else:
            print("❌ 文件名处理逻辑不正确")
            return False
        
        print("✅ 自动填充JavaScript函数测试通过")
        return True
    else:
        print("❌ 找不到报告管理页面文件")
        return False

def main():
    """主测试函数"""
    print("开始测试优化2功能...")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("自动填充报告名称", test_auto_fill_report_name()))
    test_results.append(("报告专题显示", test_report_topic_display()))
    test_results.append(("过滤器顺序", test_filter_order()))
    test_results.append(("批量评审UI", test_batch_review_ui()))
    test_results.append(("自动填充JS函数", test_auto_fill_js_function()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {total} 项测试，通过: {passed} 项，失败: {total - passed} 项")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有优化功能测试通过！")
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
