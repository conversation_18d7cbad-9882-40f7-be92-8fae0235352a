import pandas as pd
from services.document_parser import DocumentParser

# 检查Excel文件内容
excel_path = "templates/中央预算投资项目审核表.xlsx"

print("=== 检查Excel文件内容 ===")

try:
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    print(f"Excel文件读取成功")
    print(f"总行数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    
    print("\n前10行数据:")
    print(df.head(10))
    
    print("\n=== 使用DocumentParser解析 ===")
    parser = DocumentParser()
    criteria = parser.parse_review_criteria(excel_path)
    
    print(f"解析到的审查细则数量: {len(criteria)}")
    
    for i, criterion in enumerate(criteria):
        print(f"{i+1}. ID: '{criterion.get('id', 'N/A')}', 内容: '{criterion.get('content', 'N/A')[:50]}...'")
        if i >= 20:  # 只显示前20个
            print("   ...(更多细则)")
            break
    
    # 检查是否有空ID
    empty_ids = [c for c in criteria if not c.get('id', '').strip()]
    if empty_ids:
        print(f"\n⚠️  发现 {len(empty_ids)} 个空ID的审查细则")
        for i, criterion in enumerate(empty_ids[:5]):
            print(f"  {i+1}. 内容: '{criterion.get('content', 'N/A')[:50]}...'")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
