#!/usr/bin/env python3
"""
测试文件上传和评审功能
"""

import requests
import os
import json

def test_upload_and_analyze():
    """测试文件上传和分析功能"""
    
    # 服务器地址
    base_url = "http://localhost:8000"
    
    # 测试文件路径
    pdf_path = "uploads/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"错误：测试文件不存在 {pdf_path}")
        return False
    
    print("=" * 60)
    print("测试文件上传和评审功能")
    print("=" * 60)
    
    try:
        # 测试首页访问
        print("1. 测试首页访问...")
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✓ 首页访问成功")
        else:
            print(f"✗ 首页访问失败，状态码: {response.status_code}")
            return False
        
        # 测试文件上传和分析
        print("\n2. 测试文件上传和分析...")
        
        with open(pdf_path, 'rb') as f:
            files = {'pdf_file': (os.path.basename(pdf_path), f, 'application/pdf')}
            
            print(f"上传文件: {os.path.basename(pdf_path)}")
            print("正在分析，请稍候...")
            
            response = requests.post(f"{base_url}/analyze", files=files, timeout=300)
            
            if response.status_code == 200:
                print("✓ 文件上传和分析成功")
                
                # 解析响应
                result = response.json()
                
                # 验证响应结构
                print("\n3. 验证响应结构...")
                required_keys = ["criteria_analysis", "sections", "summary", "statistics"]
                for key in required_keys:
                    if key in result:
                        print(f"✓ {key}: 存在")
                    else:
                        print(f"✗ {key}: 缺失")
                        return False
                
                # 验证审查细则数据
                criteria_analysis = result.get("criteria_analysis", [])
                print(f"\n4. 审查细则分析结果:")
                print(f"   总数: {len(criteria_analysis)}")
                
                if criteria_analysis:
                    # 检查第一个审查细则
                    first_criterion = criteria_analysis[0]
                    print(f"   第一个细则ID: {first_criterion.get('criterion_id', 'unknown')}")
                    
                    # 检查新增字段
                    new_fields = ["comprehensive_analysis", "overall_assessment", "key_findings", "recommendations"]
                    for field in new_fields:
                        if field in first_criterion:
                            print(f"   ✓ {field}: 存在")
                        else:
                            print(f"   ✗ {field}: 缺失")
                
                # 保存结果用于检查
                output_file = "test_upload_result.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"\n5. 结果已保存到: {output_file}")
                
                print("\n" + "=" * 60)
                print("✓ 所有测试通过！")
                print("=" * 60)
                
                return True
                
            else:
                print(f"✗ 文件上传失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_upload_and_analyze()
    if success:
        print("\n🎉 测试完成！现在可以在浏览器中访问 http://localhost:8000 查看完整功能。")
    else:
        print("\n❌ 测试失败，请检查服务是否正常运行。")
