#!/usr/bin/env python3
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dotenv import load_dotenv
from openai import OpenAI
from services.document_parser import DocumentParser

# 加载环境变量
load_dotenv()

print("测试API连接...")

api_key = os.getenv("OPENAI_API_KEY")
api_base = os.getenv("OPENAI_API_BASE")
model_name = os.getenv("MODEL_NAME")

print(f"API Key: {api_key[:10]}..." if api_key else "API Key: None")
print(f"API Base: {api_base}")
print(f"Model Name: {model_name}")

try:
    print("创建OpenAI客户端...")
    client = OpenAI(
        api_key=api_key,
        base_url=api_base
    )
    print("✓ OpenAI客户端创建成功")
    
    print("测试API调用...")
    response = client.chat.completions.create(
        model=model_name,
        messages=[
            {"role": "system", "content": "你是一个测试助手。"},
            {"role": "user", "content": "请回复'测试成功'"}
        ],
        timeout=10  # 设置超时
    )
    
    print("✓ API调用成功")
    print(f"响应: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"✗ API测试失败: {e}")
    import traceback
    traceback.print_exc()
