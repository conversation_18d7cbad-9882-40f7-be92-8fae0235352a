#!/usr/bin/env python3
"""
测试专题汇总功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.topic_service import TopicService
from services.report_service import ReportService
from services.review_service import ReviewService
from services.topic_summary_service import TopicSummaryService
from services.model_service import ModelService

def create_test_data():
    """创建测试数据"""
    print("=== 创建测试数据 ===")
    
    topic_service = TopicService()
    report_service = ReportService()
    review_service = ReviewService()
    
    # 创建专题（带日期）
    print("1. 创建测试专题...")
    try:
        topic = topic_service.create_topic(
            name="测试专题汇总",
            description="用于测试专题汇总功能的专题",
            outline_file="templates/可行性研究报告编制大纲.docx",
            guide_file="templates/可行性研究报告审查指南.docx",
            criteria_file="templates/中央预算投资项目审核表.xlsx",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )
        print(f"创建专题成功: {topic['name']} (ID: {topic['id']})")
        topic_id = topic['id']
    except ValueError as e:
        # 如果专题已存在，获取现有专题
        topics = topic_service.get_topics()
        topic = next((t for t in topics if t['name'] == "测试专题汇总"), None)
        if topic:
            print(f"使用现有专题: {topic['name']} (ID: {topic['id']})")
            topic_id = topic['id']
        else:
            raise e
    
    # 创建测试文件
    test_file_path = "debug/test_report.pdf"
    with open(test_file_path, 'w') as f:
        f.write("这是一个测试PDF文件")
    
    # 创建测试报告
    print("2. 创建测试报告...")
    report1 = report_service.create_report(
        name="测试报告1",
        file_path=test_file_path,
        topic_id=topic_id,
        description="第一个测试报告"
    )
    print(f"创建报告成功: {report1['name']} (ID: {report1['id']})")
    
    report2 = report_service.create_report(
        name="测试报告2",
        file_path=test_file_path,
        topic_id=topic_id,
        description="第二个测试报告"
    )
    print(f"创建报告成功: {report2['name']} (ID: {report2['id']})")
    
    report3 = report_service.create_report(
        name="测试报告3（未评审）",
        file_path=test_file_path,
        topic_id=topic_id,
        description="第三个测试报告，用于测试未评审提示"
    )
    print(f"创建报告成功: {report3['name']} (ID: {report3['id']})")
    
    # 创建模拟评审结果
    print("3. 创建模拟评审结果...")
    
    mock_result1 = {
        "sections": ["1 项目概况", "2 项目建设的必要性"],
        "review_results": [
            {
                "criterion_id": "1.1",
                "criterion_text": "项目名称应明确",
                "result": "符合",
                "analysis": "项目名称明确清晰"
            },
            {
                "criterion_id": "1.2", 
                "criterion_text": "项目建设内容应详细",
                "result": "基本符合",
                "analysis": "建设内容较为详细，但部分细节需要补充"
            },
            {
                "criterion_id": "2.1",
                "criterion_text": "必要性分析应充分",
                "result": "不符合",
                "analysis": "必要性分析不够充分，缺乏数据支撑"
            }
        ],
        "summary": "报告1总体评审意见：项目基本符合要求，但必要性分析需要加强",
        "statistics": {
            "total_criteria": 3,
            "compliance_rate": 66.7,
            "result_distribution": {"符合": 1, "基本符合": 1, "不符合": 1}
        }
    }
    
    mock_result2 = {
        "sections": ["1 项目概况", "2 项目建设的必要性"],
        "review_results": [
            {
                "criterion_id": "1.1",
                "criterion_text": "项目名称应明确",
                "result": "符合",
                "analysis": "项目名称非常明确"
            },
            {
                "criterion_id": "1.2",
                "criterion_text": "项目建设内容应详细", 
                "result": "符合",
                "analysis": "建设内容详细完整"
            },
            {
                "criterion_id": "2.1",
                "criterion_text": "必要性分析应充分",
                "result": "基本符合",
                "analysis": "必要性分析基本充分，有一定数据支撑"
            }
        ],
        "summary": "报告2总体评审意见：项目符合要求，质量较高",
        "statistics": {
            "total_criteria": 3,
            "compliance_rate": 83.3,
            "result_distribution": {"符合": 2, "基本符合": 1, "不符合": 0}
        }
    }
    
    review1 = review_service.create_review(
        report_id=report1['id'],
        topic_id=topic_id,
        result=mock_result1
    )
    print(f"创建评审记录成功: 报告1 (ID: {review1['id']})")
    
    review2 = review_service.create_review(
        report_id=report2['id'],
        topic_id=topic_id,
        result=mock_result2
    )
    print(f"创建评审记录成功: 报告2 (ID: {review2['id']})")
    
    return topic_id, [report1['id'], report2['id'], report3['id']], [review1['id'], review2['id']]

def test_topic_summary_service():
    """测试专题汇总服务"""
    print("\n=== 测试专题汇总服务 ===")
    
    # 创建测试数据
    topic_id, report_ids, review_ids = create_test_data()
    
    # 初始化汇总服务
    model_service = ModelService()
    summary_service = TopicSummaryService(model_service)
    
    # 测试获取专题状态
    print("1. 获取专题状态...")
    status = summary_service.get_topic_status(topic_id)
    print(f"专题状态:")
    print(f"  - 总报告数: {status['total_reports']}")
    print(f"  - 已评审: {status['reviewed_count']}")
    print(f"  - 未评审: {status['unreviewed_count']}")
    print(f"  - 完成率: {status['completion_rate']:.1f}%")
    
    if status['unreviewed_reports']:
        print(f"  - 未评审报告: {[r['name'] for r in status['unreviewed_reports']]}")
    
    # 测试生成汇总报告（应该提示有未评审报告）
    print("\n2. 尝试生成汇总报告（有未评审报告）...")
    summary_result = summary_service.generate_topic_summary(topic_id)
    
    if 'warning' in summary_result:
        print(f"✅ 正确提示未评审报告: {summary_result['message']}")
        print(f"   未评审报告数量: {len(summary_result['unreviewed_reports'])}")
        
        # 强制生成汇总报告
        print("\n3. 强制生成汇总报告...")
        summary_result = summary_service.generate_topic_summary(topic_id, force_regenerate=True)
        
        if 'summary_result' in summary_result:
            print("✅ 汇总报告生成成功")
            result = summary_result['summary_result']
            stats = result['overall_statistics']
            
            print(f"   汇总统计:")
            print(f"   - 总报告数: {stats['total_reports']}")
            print(f"   - 已评审报告数: {stats['reviewed_reports']}")
            print(f"   - 未评审报告数: {stats['unreviewed_reports']}")
            print(f"   - 平均合规率: {stats['average_compliance_rate']}%")
            print(f"   - 完成率: {stats['completion_rate']}%")
            
            print(f"\n   汇总分析内容:")
            print(f"   {result['summary'][:200]}...")
            
            print(f"\n   审查细则分析数量: {len(result['criteria_analysis'])}")
            for criterion_id, criterion_data in list(result['criteria_analysis'].items())[:2]:
                print(f"   - {criterion_id}: {criterion_data['criterion_text']}")
                print(f"     评审结果数量: {len(criterion_data['results'])}")
        else:
            print("❌ 汇总报告生成失败")
    else:
        print("❌ 未正确提示未评审报告")
    
    # 测试获取最新汇总报告
    print("\n4. 获取最新汇总报告...")
    latest_summary = summary_service.get_latest_summary(topic_id)
    if latest_summary:
        print(f"✅ 获取最新汇总报告成功")
        print(f"   生成时间: {latest_summary['created_at']}")
        print(f"   汇总ID: {latest_summary['id']}")
    else:
        print("❌ 未找到汇总报告")
    
    # 测试重新生成汇总报告
    print("\n5. 测试重新生成汇总报告...")
    new_summary = summary_service.generate_topic_summary(topic_id, force_regenerate=True)
    if 'summary_result' in new_summary:
        print("✅ 重新生成汇总报告成功")
        print(f"   新汇总ID: {new_summary['id']}")
    else:
        print("❌ 重新生成汇总报告失败")
    
    # 测试获取所有汇总报告
    print("\n6. 获取所有汇总报告...")
    all_summaries = summary_service.get_all_summaries(topic_id)
    print(f"✅ 该专题共有 {len(all_summaries)} 个汇总报告")
    
    return topic_id, summary_service

def main():
    """主测试函数"""
    print("开始测试专题汇总功能...")
    
    try:
        topic_id, summary_service = test_topic_summary_service()
        
        print("\n=== 测试完成 ===")
        print("✅ 专题状态获取功能正常")
        print("✅ 未评审报告提示功能正常")
        print("✅ 汇总报告生成功能正常")
        print("✅ 重新生成汇总报告功能正常")
        print("✅ 汇总报告查询功能正常")
        
        print(f"\n测试专题ID: {topic_id}")
        print("可以在前端页面中选择该专题查看汇总效果")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
