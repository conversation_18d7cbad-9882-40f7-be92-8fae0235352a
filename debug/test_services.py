#!/usr/bin/env python3
import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

print("开始测试服务...")

print("1. 测试文档解析器导入...")
try:
    from services.document_parser import DocumentParser
    print("✓ DocumentParser 导入成功")

    parser = DocumentParser()
    print("✓ DocumentParser 初始化成功")
except Exception as e:
    print(f"✗ DocumentParser 失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n2. 测试模型服务导入...")
try:
    from services.model_service import ModelService
    print("✓ ModelService 导入成功")

    model_service = ModelService()
    print("✓ ModelService 初始化成功")
except Exception as e:
    print(f"✗ ModelService 失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n3. 测试报告分析器导入...")
try:
    from services.report_analyzer import ReportAnalyzer
    print("✓ ReportAnalyzer 导入成功")

    print("开始初始化 ReportAnalyzer...")
    analyzer = ReportAnalyzer(model_service, parser)
    print("✓ ReportAnalyzer 初始化成功")
except Exception as e:
    print(f"✗ ReportAnalyzer 失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n✓ 所有服务测试通过")
