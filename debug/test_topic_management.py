#!/usr/bin/env python3
"""
测试报告专题管理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.topic_service import TopicService
from services.report_service import ReportService
from services.review_service import ReviewService

def test_topic_service():
    """测试专题管理服务"""
    print("=== 测试专题管理服务 ===")

    topic_service = TopicService()

    # 测试创建专题
    print("1. 创建专题...")

    # 先检查是否已存在，如果存在就使用现有的
    existing_topics = topic_service.get_topics()
    topic1 = None
    topic2 = None

    for topic in existing_topics:
        if topic['name'] == "农村电网巩固提升工程":
            topic1 = topic
            print(f"使用现有专题: {topic1['name']} (ID: {topic1['id']})")
        elif topic['name'] == "测试专题":
            topic2 = topic
            print(f"使用现有专题: {topic2['name']} (ID: {topic2['id']})")

    if not topic1:
        topic1 = topic_service.create_topic(
            name="农村电网巩固提升工程",
            description="农村电网巩固提升工程中央预算内投资项目专题",
            outline_file="templates/可行性研究报告编制大纲.docx",
            guide_file="templates/可行性研究报告审查指南.docx",
            criteria_file="templates/中央预算投资项目审核表.xlsx"
        )
        print(f"创建专题成功: {topic1['name']} (ID: {topic1['id']})")

    if not topic2:
        topic2 = topic_service.create_topic(
            name="测试专题",
            description="用于测试的专题",
            outline_file="templates/test_编制大纲.docx",
            guide_file="templates/可行性研究报告审查指南.docx",
            criteria_file="templates/test_审核表.xlsx"
        )
        print(f"创建专题成功: {topic2['name']} (ID: {topic2['id']})")

    # 测试获取所有专题
    print("\n2. 获取所有专题...")
    topics = topic_service.get_topics()
    print(f"共有 {len(topics)} 个专题:")
    for topic in topics:
        print(f"  - {topic['name']}: {topic['description']}")

    # 测试获取单个专题
    print("\n3. 获取单个专题...")
    topic = topic_service.get_topic(topic1['id'])
    if topic:
        print(f"获取专题成功: {topic['name']}")
        print(f"  描述: {topic['description']}")
        print(f"  大纲文件: {topic['outline_file']}")
        print(f"  指南文件: {topic['guide_file']}")
        print(f"  细则文件: {topic['criteria_file']}")

    # 测试更新专题
    print("\n4. 更新专题...")
    updated_topic = topic_service.update_topic(
        topic2['id'],
        description="更新后的测试专题描述"
    )
    if updated_topic:
        print(f"更新专题成功: {updated_topic['description']}")

    # 测试获取专题文件配置
    print("\n5. 获取专题文件配置...")
    files = topic_service.get_topic_files(topic1['id'])
    print(f"专题文件配置: {files}")

    return topic1['id'], topic2['id']

def test_report_service():
    """测试报告管理服务"""
    print("\n=== 测试报告管理服务 ===")

    report_service = ReportService()

    # 创建测试文件
    test_file_path = "debug/test_report.pdf"
    with open(test_file_path, 'w') as f:
        f.write("这是一个测试PDF文件")

    # 测试创建报告
    print("1. 创建报告...")
    report1 = report_service.create_report(
        name="测试可研报告1",
        file_path=test_file_path,
        topic_id="default-topic-001",
        description="这是第一个测试报告"
    )
    print(f"创建报告成功: {report1['name']} (ID: {report1['id']})")

    report2 = report_service.create_report(
        name="测试可研报告2",
        file_path=test_file_path,
        description="这是第二个测试报告"
    )
    print(f"创建报告成功: {report2['name']} (ID: {report2['id']})")

    # 测试获取所有报告
    print("\n2. 获取所有报告...")
    reports = report_service.get_reports()
    print(f"共有 {len(reports)} 个报告:")
    for report in reports:
        print(f"  - {report['name']}: {report['description']}")

    # 测试按专题过滤报告
    print("\n3. 按专题过滤报告...")
    filtered_reports = report_service.get_reports(topic_id="default-topic-001")
    print(f"专题 'default-topic-001' 下有 {len(filtered_reports)} 个报告")

    # 测试获取单个报告
    print("\n4. 获取单个报告...")
    report = report_service.get_report(report1['id'])
    if report:
        print(f"获取报告成功: {report['name']}")
        print(f"  文件路径: {report['file_path']}")
        print(f"  文件大小: {report['file_size']} 字节")

    # 测试更新报告
    print("\n5. 更新报告...")
    updated_report = report_service.update_report(
        report2['id'],
        topic_id="default-topic-001",
        description="更新后的报告描述"
    )
    if updated_report:
        print(f"更新报告成功: {updated_report['description']}")

    return report1['id'], report2['id']

def test_review_service():
    """测试评审管理服务"""
    print("\n=== 测试评审管理服务 ===")

    review_service = ReviewService()

    # 模拟评审结果
    mock_result = {
        "sections": ["1 项目概况", "2 项目建设的必要性"],
        "review_results": [
            {
                "criterion_id": "1.1",
                "criterion_text": "项目名称应明确",
                "result": "符合",
                "analysis": "项目名称明确清晰"
            }
        ],
        "summary": "总体评审意见：项目基本符合要求",
        "statistics": {
            "total_criteria": 1,
            "compliance_rate": 100.0,
            "result_distribution": {"符合": 1}
        }
    }

    # 测试创建评审记录
    print("1. 创建评审记录...")
    review1 = review_service.create_review(
        report_id="test-report-001",
        topic_id="default-topic-001",
        result=mock_result
    )
    print(f"创建评审记录成功: ID {review1['id']}")

    review2 = review_service.create_review(
        report_id="test-report-002",
        topic_id="default-topic-001",
        result=mock_result
    )
    print(f"创建评审记录成功: ID {review2['id']}")

    # 测试获取所有评审记录
    print("\n2. 获取所有评审记录...")
    reviews = review_service.get_reviews()
    print(f"共有 {len(reviews)} 个评审记录:")
    for review in reviews:
        print(f"  - ID: {review['id']}, 报告ID: {review['report_id']}, 状态: {review['status']}")

    # 测试按报告过滤评审记录
    print("\n3. 按报告过滤评审记录...")
    filtered_reviews = review_service.get_reviews(report_id="test-report-001")
    print(f"报告 'test-report-001' 有 {len(filtered_reviews)} 个评审记录")

    # 测试获取单个评审记录
    print("\n4. 获取单个评审记录...")
    review = review_service.get_review(review1['id'])
    if review:
        print(f"获取评审记录成功: ID {review['id']}")
        print(f"  状态: {review['status']}")
        print(f"  合规率: {review['result']['statistics']['compliance_rate']}%")

    # 测试获取报告的最新评审记录
    print("\n5. 获取报告的最新评审记录...")
    latest_review = review_service.get_latest_review_for_report("test-report-001")
    if latest_review:
        print(f"最新评审记录: ID {latest_review['id']}")

    return review1['id'], review2['id']

def main():
    """主测试函数"""
    print("开始测试报告专题管理功能...")

    try:
        # 测试专题管理
        topic1_id, topic2_id = test_topic_service()

        # 测试报告管理
        report1_id, report2_id = test_report_service()

        # 测试评审管理
        review1_id, review2_id = test_review_service()

        print("\n=== 所有测试完成 ===")
        print("✅ 专题管理服务测试通过")
        print("✅ 报告管理服务测试通过")
        print("✅ 评审管理服务测试通过")

        print(f"\n创建的测试数据:")
        print(f"  专题ID: {topic1_id}, {topic2_id}")
        print(f"  报告ID: {report1_id}, {report2_id}")
        print(f"  评审ID: {review1_id}, {review2_id}")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
