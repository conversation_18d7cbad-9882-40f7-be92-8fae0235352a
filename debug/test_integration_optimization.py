#!/usr/bin/env python3
"""
集成测试：验证数据结构优化后的完整功能
"""

import sys
import os
import json
import requests
import time

def test_api_integration():
    """测试API集成功能"""
    print("=" * 60)
    print("🧪 测试API集成功能（数据结构优化）")
    print("=" * 60)
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    try:
        # 1. 检查服务是否运行
        print("1. 检查服务状态...")
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            if response.status_code == 200:
                print("✓ 服务正在运行")
            else:
                print(f"❌ 服务状态异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到服务: {e}")
            print("请确保服务正在运行: python main.py")
            return False
        
        # 2. 准备测试文件
        print("\n2. 准备测试文件...")
        test_files = [
            "uploads/test.pdf",
            "templates/test_可研报告.pdf"
        ]
        
        test_file = None
        for file_path in test_files:
            if os.path.exists(file_path):
                test_file = file_path
                break
        
        if not test_file:
            print("❌ 未找到测试PDF文件")
            print("请确保以下文件之一存在:")
            for file_path in test_files:
                print(f"  - {file_path}")
            return False
        
        print(f"✓ 使用测试文件: {test_file}")
        
        # 3. 发送分析请求
        print("\n3. 发送分析请求...")
        with open(test_file, 'rb') as f:
            files = {'pdf_file': (os.path.basename(test_file), f, 'application/pdf')}
            
            print("   正在分析，请稍候...")
            start_time = time.time()
            response = requests.post(f"{base_url}/analyze", files=files, timeout=300)
            end_time = time.time()
            
            print(f"   请求耗时: {end_time - start_time:.2f}秒")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        print("✓ API请求成功")
        
        # 4. 解析响应数据
        print("\n4. 解析响应数据...")
        try:
            result = response.json()
            print("✓ JSON解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return False
        
        # 5. 验证新的数据结构
        print("\n5. 验证新的数据结构...")
        
        # 检查顶级字段
        required_fields = ['sections', 'review_results', 'summary', 'statistics', 'timing_stats']
        for field in required_fields:
            if field in result:
                print(f"  ✓ 顶级字段 '{field}': 存在")
            else:
                print(f"  ❌ 顶级字段 '{field}': 缺失")
        
        # 检查向后兼容字段
        compatibility_fields = ['criteria_analysis', 'sections_detail']
        for field in compatibility_fields:
            if field in result:
                print(f"  ✓ 兼容字段 '{field}': 存在")
            else:
                print(f"  ⚠️  兼容字段 '{field}': 缺失")
        
        # 6. 验证sections字段结构
        print("\n6. 验证sections字段结构...")
        sections = result.get('sections', [])
        print(f"  章节数量: {len(sections)}")
        
        if sections:
            required_section_fields = ['section_name', 'content_length', 'has_content', 'summary']
            for i, section in enumerate(sections[:3]):  # 只检查前3个
                missing_fields = [field for field in required_section_fields if field not in section]
                if missing_fields:
                    print(f"  ❌ sections[{i}] 缺少字段: {missing_fields}")
                else:
                    print(f"  ✓ sections[{i}]: {section['section_name']} - 结构完整")
                    print(f"    摘要: {section['summary'][:50]}...")
        
        # 7. 验证review_results字段结构
        print("\n7. 验证review_results字段结构...")
        review_results = result.get('review_results', [])
        print(f"  审查细则数量: {len(review_results)}")
        
        if review_results:
            required_criterion_fields = ['criterion_id', 'criterion_content', 'sections']
            for i, criterion in enumerate(review_results[:3]):  # 只检查前3个
                missing_fields = [field for field in required_criterion_fields if field not in criterion]
                if missing_fields:
                    print(f"  ❌ review_results[{i}] 缺少字段: {missing_fields}")
                else:
                    print(f"  ✓ review_results[{i}]: {criterion['criterion_id']} - 结构完整")
                    
                    # 检查sections子字段
                    criterion_sections = criterion.get('sections', [])
                    print(f"    章节评审数: {len(criterion_sections)}")
                    
                    if criterion_sections:
                        required_section_fields = ['section_name', 'result', 'explanation']
                        for j, section in enumerate(criterion_sections[:2]):  # 只检查前2个
                            missing_fields = [field for field in required_section_fields if field not in section]
                            if missing_fields:
                                print(f"    ❌ sections[{j}] 缺少字段: {missing_fields}")
                            else:
                                print(f"    ✓ sections[{j}]: {section['section_name']} - {section['result']}")
        
        # 8. 验证统计信息
        print("\n8. 验证统计信息...")
        statistics = result.get('statistics', {})
        timing_stats = result.get('timing_stats', {})
        
        if statistics:
            print(f"  总审查细则: {statistics.get('total_criteria', 'N/A')}")
            print(f"  总章节数: {statistics.get('total_sections', 'N/A')}")
            print(f"  合规率: {statistics.get('compliance_rate', 'N/A')}%")
        
        if timing_stats:
            print(f"  执行时间: {timing_stats.get('execution_time', 'N/A')}秒")
            print(f"  API调用次数: {timing_stats.get('api_calls', 'N/A')}")
            print(f"  总Token数: {timing_stats.get('total_tokens', 'N/A')}")
        
        # 9. 保存测试结果
        print("\n9. 保存测试结果...")
        output_file = "integration_test_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"✓ 测试结果已保存到: {output_file}")
        
        # 10. 对比新旧数据结构
        print("\n10. 对比新旧数据结构...")
        
        # 新格式数据量
        new_sections_count = len(result.get('sections', []))
        new_review_results_count = len(result.get('review_results', []))
        
        # 旧格式数据量（兼容性检查）
        old_criteria_count = len(result.get('criteria_analysis', []))
        old_sections_count = len(result.get('sections_detail', []))
        
        print(f"  新格式 - sections: {new_sections_count}, review_results: {new_review_results_count}")
        print(f"  旧格式 - criteria_analysis: {old_criteria_count}, sections_detail: {old_sections_count}")
        
        # 数据一致性检查
        if new_review_results_count == old_criteria_count:
            print("  ✓ 审查细则数量一致")
        else:
            print("  ⚠️  审查细则数量不一致")
        
        if new_sections_count == old_sections_count:
            print("  ✓ 章节数量一致")
        else:
            print("  ⚠️  章节数量不一致")
        
        print("\n" + "=" * 60)
        print("✅ API集成测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api_integration()
    sys.exit(0 if success else 1)
