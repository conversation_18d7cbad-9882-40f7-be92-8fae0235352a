#!/usr/bin/env python3
"""
测试专题汇总功能（简化版，不依赖模型服务）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.topic_service import TopicService
from services.report_service import ReportService
from services.review_service import ReviewService

class MockModelService:
    """模拟模型服务"""
    def call_model(self, prompt, system_prompt=""):
        return "这是一个模拟的汇总分析结果。在实际使用中，这里会调用大模型生成详细的专题汇总分析报告。"

def test_basic_services():
    """测试基础服务功能"""
    print("=== 测试基础服务功能 ===")
    
    topic_service = TopicService()
    report_service = ReportService()
    review_service = ReviewService()
    
    # 测试专题服务（带日期字段）
    print("1. 测试专题服务...")
    try:
        topic = topic_service.create_topic(
            name="测试专题带日期",
            description="测试专题日期功能",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )
        print(f"✅ 创建带日期的专题成功: {topic['name']}")
        print(f"   开始日期: {topic['start_date']}")
        print(f"   结束日期: {topic['end_date']}")
        topic_id = topic['id']
    except ValueError:
        # 专题已存在，获取现有的
        topics = topic_service.get_topics()
        topic = next((t for t in topics if t['name'] == "测试专题带日期"), None)
        if topic:
            print(f"✅ 使用现有专题: {topic['name']}")
            topic_id = topic['id']
        else:
            raise
    
    # 测试报告服务
    print("\n2. 测试报告服务...")
    test_file_path = "debug/test_report.pdf"
    with open(test_file_path, 'w') as f:
        f.write("测试PDF内容")
    
    report1 = report_service.create_report(
        name="测试报告A",
        file_path=test_file_path,
        topic_id=topic_id,
        description="第一个测试报告"
    )
    print(f"✅ 创建报告成功: {report1['name']}")
    
    report2 = report_service.create_report(
        name="测试报告B",
        file_path=test_file_path,
        topic_id=topic_id,
        description="第二个测试报告"
    )
    print(f"✅ 创建报告成功: {report2['name']}")
    
    # 测试评审服务
    print("\n3. 测试评审服务...")
    mock_result = {
        "review_results": [
            {
                "criterion_id": "1.1",
                "criterion_text": "项目名称应明确",
                "result": "符合",
                "analysis": "项目名称明确清晰"
            }
        ],
        "summary": "总体评审意见：项目基本符合要求",
        "statistics": {
            "total_criteria": 1,
            "compliance_rate": 100.0,
            "result_distribution": {"符合": 1}
        }
    }
    
    review1 = review_service.create_review(
        report_id=report1['id'],
        topic_id=topic_id,
        result=mock_result
    )
    print(f"✅ 创建评审记录成功: {review1['id']}")
    
    return topic_id, [report1['id'], report2['id']], [review1['id']]

def test_topic_status():
    """测试专题状态功能"""
    print("\n=== 测试专题状态功能 ===")
    
    # 导入汇总服务
    from services.topic_summary_service import TopicSummaryService
    
    # 使用模拟的模型服务
    mock_model_service = MockModelService()
    summary_service = TopicSummaryService(mock_model_service)
    
    # 获取测试数据
    topic_id, report_ids, review_ids = test_basic_services()
    
    # 测试获取专题状态
    print("1. 获取专题状态...")
    status = summary_service.get_topic_status(topic_id)
    
    if 'error' in status:
        print(f"❌ 获取专题状态失败: {status['error']}")
        return
    
    print(f"✅ 专题状态获取成功:")
    print(f"   专题名称: {status['topic']['name']}")
    print(f"   总报告数: {status['total_reports']}")
    print(f"   已评审: {status['reviewed_count']}")
    print(f"   未评审: {status['unreviewed_count']}")
    print(f"   完成率: {status['completion_rate']:.1f}%")
    
    # 测试生成汇总报告
    print("\n2. 测试生成汇总报告...")
    summary_result = summary_service.generate_topic_summary(topic_id, force_regenerate=True)
    
    if 'error' in summary_result:
        print(f"❌ 生成汇总报告失败: {summary_result['error']}")
        return
    
    if 'warning' in summary_result:
        print(f"⚠️  有未评审报告: {summary_result['message']}")
        # 强制生成
        summary_result = summary_service.generate_topic_summary(topic_id, force_regenerate=True)
    
    if 'summary_result' in summary_result:
        print("✅ 汇总报告生成成功")
        result = summary_result['summary_result']
        stats = result['overall_statistics']
        
        print(f"   统计信息:")
        print(f"   - 总报告数: {stats['total_reports']}")
        print(f"   - 已评审报告数: {stats['reviewed_reports']}")
        print(f"   - 平均合规率: {stats['average_compliance_rate']}%")
        
        print(f"   汇总分析: {result['summary'][:100]}...")
        print(f"   审查细则分析数量: {len(result['criteria_analysis'])}")
    else:
        print("❌ 汇总报告生成失败")
    
    # 测试获取最新汇总
    print("\n3. 测试获取最新汇总...")
    latest = summary_service.get_latest_summary(topic_id)
    if latest:
        print(f"✅ 获取最新汇总成功: {latest['id']}")
        print(f"   生成时间: {latest['created_at']}")
    else:
        print("❌ 未找到汇总报告")
    
    return topic_id

def test_api_data_structure():
    """测试API数据结构"""
    print("\n=== 测试API数据结构 ===")
    
    topic_service = TopicService()
    
    # 测试专题数据结构
    topics = topic_service.get_topics()
    if topics:
        topic = topics[0]
        print("✅ 专题数据结构:")
        required_fields = ['id', 'name', 'description', 'start_date', 'end_date', 'created_at', 'updated_at']
        for field in required_fields:
            if field in topic:
                print(f"   ✓ {field}: {topic.get(field, 'N/A')}")
            else:
                print(f"   ✗ 缺少字段: {field}")
    
    # 测试报告数据结构
    report_service = ReportService()
    reports = report_service.get_reports()
    if reports:
        report = reports[0]
        print("\n✅ 报告数据结构:")
        required_fields = ['id', 'name', 'file_path', 'topic_id', 'description', 'created_at']
        for field in required_fields:
            if field in report:
                print(f"   ✓ {field}: {report.get(field, 'N/A')}")
            else:
                print(f"   ✗ 缺少字段: {field}")
    
    # 测试评审数据结构
    review_service = ReviewService()
    reviews = review_service.get_reviews()
    if reviews:
        review = reviews[0]
        print("\n✅ 评审数据结构:")
        required_fields = ['id', 'report_id', 'topic_id', 'result', 'created_at', 'status']
        for field in required_fields:
            if field in review:
                print(f"   ✓ {field}: {type(review.get(field, 'N/A'))}")
            else:
                print(f"   ✗ 缺少字段: {field}")

def main():
    """主测试函数"""
    print("开始测试专题汇总优化功能...")
    
    try:
        # 测试基础服务
        test_api_data_structure()
        
        # 测试专题状态和汇总功能
        topic_id = test_topic_status()
        
        print("\n=== 测试总结 ===")
        print("✅ 专题日期字段功能正常")
        print("✅ 专题状态获取功能正常")
        print("✅ 汇总报告生成功能正常")
        print("✅ 数据结构完整")
        
        print(f"\n测试专题ID: {topic_id}")
        print("现在可以启动服务器测试前端功能:")
        print("python main.py --port 8001")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
