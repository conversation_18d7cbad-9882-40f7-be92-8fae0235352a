#!/usr/bin/env python3
"""
测试优化后的调试信息机制
"""

import os
import sys
import time
import threading
import requests
import json
from typing import List, Dict

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_debug_callback():
    """测试调试回调功能"""
    print("=== 测试调试回调功能 ===")

    # 设置测试模式
    os.environ["TEST_MODE"] = "true"

    from services.model_service import ModelService
    from services.document_parser import DocumentParser
    from services.report_analyzer import ReportAnalyzer

    # 收集调试信息
    debug_messages = []

    def debug_callback(message: str, level: str = "info"):
        debug_messages.append({
            "timestamp": time.time(),
            "message": message,
            "level": level
        })
        print(f"[{level.upper()}] {message}")

    try:
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)

        # 使用测试PDF文件
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")

        if not os.path.exists(test_pdf):
            print(f"测试PDF文件不存在: {test_pdf}")
            return False

        print("开始分析，收集调试信息...")
        result = analyzer.analyze(test_pdf, debug_callback=debug_callback)

        print(f"\n收集到 {len(debug_messages)} 条调试信息:")

        # 按级别统计
        level_stats = {}
        for msg in debug_messages:
            level = msg["level"]
            level_stats[level] = level_stats.get(level, 0) + 1

        print(f"调试信息统计: {level_stats}")

        # 检查关键调试信息
        key_messages = [
            "模板文件加载完成",
            "开始分析章节",
            "准备调用大模型分析章节",
            "大模型API调用成功",
            "解析成功，获得"
        ]

        found_messages = []
        for key_msg in key_messages:
            for msg in debug_messages:
                if key_msg in msg["message"]:
                    found_messages.append(key_msg)
                    break

        print(f"找到关键调试信息: {len(found_messages)}/{len(key_messages)}")
        for msg in found_messages:
            print(f"  ✓ {msg}")

        missing_messages = set(key_messages) - set(found_messages)
        if missing_messages:
            print("缺少的关键调试信息:")
            for msg in missing_messages:
                print(f"  ✗ {msg}")

        return len(found_messages) >= len(key_messages) * 0.8  # 至少80%的关键信息

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sse_endpoint():
    """测试SSE端点功能"""
    print("\n=== 测试SSE端点功能 ===")

    try:
        # 测试SSE端点是否可访问
        url = "http://localhost:8002/debug-stream"

        # 使用流式请求测试SSE
        response = requests.get(url, stream=True, timeout=5)

        if response.status_code == 200:
            print("✓ SSE端点可访问")

            # 检查响应头
            content_type = response.headers.get('content-type', '')
            if 'text/event-stream' in content_type:
                print("✓ 正确的Content-Type")
            else:
                print(f"✗ 错误的Content-Type: {content_type}")
                return False

            # 读取几行数据
            lines_read = 0
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data:'):
                    lines_read += 1
                    try:
                        data = json.loads(line[5:])  # 去掉 'data:' 前缀
                        print(f"  收到数据: {data}")
                    except json.JSONDecodeError:
                        print(f"  收到非JSON数据: {line}")

                if lines_read >= 3:  # 读取3条数据后停止
                    break

            print(f"✓ 成功读取 {lines_read} 条SSE数据")
            return True
        else:
            print(f"✗ SSE端点访问失败: {response.status_code}")
            return False

    except requests.exceptions.Timeout:
        print("✗ SSE端点访问超时")
        return False
    except Exception as e:
        print(f"✗ SSE端点测试失败: {e}")
        return False

def test_frontend_debug_features():
    """测试前端调试功能"""
    print("\n=== 测试前端调试功能 ===")

    try:
        url = "http://localhost:8002/"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            html_content = response.text

            # 检查调试相关的JavaScript功能
            debug_features = [
                "initDebugStream",
                "addDebugLogFromServer",
                "toggleDebugPanel",
                "clearDebugLog",
                "EventSource",
                "debug-stream"
            ]

            found_features = []
            for feature in debug_features:
                if feature in html_content:
                    found_features.append(feature)

            print(f"找到调试功能: {len(found_features)}/{len(debug_features)}")
            for feature in found_features:
                print(f"  ✓ {feature}")

            missing_features = set(debug_features) - set(found_features)
            if missing_features:
                print("缺少的调试功能:")
                for feature in missing_features:
                    print(f"  ✗ {feature}")

            return len(found_features) >= len(debug_features) * 0.8
        else:
            print(f"✗ 前端页面访问失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"✗ 前端调试功能测试失败: {e}")
        return False

def test_real_time_debug():
    """测试实时调试信息推送"""
    print("\n=== 测试实时调试信息推送 ===")

    debug_messages_received = []
    sse_thread = None

    def sse_listener():
        """SSE监听线程"""
        try:
            url = "http://localhost:8002/debug-stream"
            response = requests.get(url, stream=True, timeout=30)

            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data:'):
                    try:
                        data = json.loads(line[5:])
                        if data.get('type') != 'heartbeat':
                            debug_messages_received.append(data)
                            print(f"  收到调试信息: {data.get('message', '')}")
                    except json.JSONDecodeError:
                        pass
        except Exception as e:
            print(f"SSE监听异常: {e}")

    try:
        # 启动SSE监听线程
        sse_thread = threading.Thread(target=sse_listener, daemon=True)
        sse_thread.start()

        # 等待连接建立
        time.sleep(2)

        # 触发分析请求
        print("发送分析请求...")
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")

        if os.path.exists(test_pdf):
            with open(test_pdf, 'rb') as f:
                files = {'pdf_file': ('test_可行性研究报告.pdf', f, 'application/pdf')}
                response = requests.post('http://localhost:8002/analyze', files=files, timeout=60)

            print(f"分析请求响应: {response.status_code}")

            # 等待调试信息
            time.sleep(5)

            print(f"收到 {len(debug_messages_received)} 条实时调试信息")

            if debug_messages_received:
                print("调试信息示例:")
                for i, msg in enumerate(debug_messages_received[:5]):
                    print(f"  {i+1}. [{msg.get('level', 'info')}] {msg.get('message', '')}")

            return len(debug_messages_received) > 0
        else:
            print("测试文件不存在，跳过实时测试")
            return True

    except Exception as e:
        print(f"实时调试测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的调试信息机制...\n")

    tests = [
        ("调试回调功能", test_debug_callback),
        ("SSE端点功能", test_sse_endpoint),
        ("前端调试功能", test_frontend_debug_features),
        ("实时调试推送", test_real_time_debug)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))

    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{len(results)} 个测试通过")

    if passed == len(results):
        print("🎉 所有调试功能测试通过！")
    else:
        print("⚠️  部分调试功能测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
