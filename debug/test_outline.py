import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.document_parser import DocumentParser


def test_outline(outline_path):
    """测试大纲读取和章节解析"""
    print(f"\n{'='*60}")
    print(f"文件路径: {outline_path}")
    print(f"{'='*60}")

    parser = DocumentParser()

    try:
        # 解析大纲文档
        sections = parser.parse_outline(outline_path)
        print(f"解析成功，提取到 {len(sections)} 个章节")

        # 从文件名中提取项目名称
        project_name = os.path.splitext(os.path.basename(outline_path))[0]
        print(f"项目名称: {project_name}")

        # 验证返回值类型
        assert isinstance(sections, dict), f"期望返回字典类型，实际返回: {type(sections)}"
        print("✓ 返回值类型验证通过")

        # 验证章节标题格式
        expected_chapters = set(f"{num} {title}" for num, title in parser.standard_outline.items())
        actual_chapters = set(sections.keys())

        print(f"\n期望的章节数量: {len(expected_chapters)}")
        print(f"实际解析的章节数量: {len(actual_chapters)}")

        # 检查是否包含标准章节
        missing_chapters = expected_chapters - actual_chapters
        extra_chapters = actual_chapters - expected_chapters

        if missing_chapters:
            print(f"⚠️  缺失的标准章节: {missing_chapters}")
        else:
            print("✓ 包含所有标准章节")

        if extra_chapters:
            print(f"ℹ️  额外的章节: {extra_chapters}")

        # 详细显示每个章节的内容（新的字符串格式）
        print(f"\n{'='*40} 章节详情 {'='*40}")
        for section_title, content in sections.items():
            print(f"\n章节: {section_title}")

            # 现在内容都是字符串格式
            content_length = len(content) if content else 0
            print(f"字符数: {content_length}")

            if content and content.strip():
                print(f"内容预览: {content[:200]}{'...' if len(content) > 200 else ''}")
            else:
                print("内容为空")

        # 统计有内容的章节数量（字符串格式）
        non_empty_sections = 0
        for content in sections.values():
            if content and str(content).strip():
                non_empty_sections += 1

        print(f"\n{'='*40} 统计信息 {'='*40}")
        print(f"总章节数: {len(sections)}")
        print(f"有内容的章节数: {non_empty_sections}")
        print(f"空章节数: {len(sections) - non_empty_sections}")

        # 验证parser.outline是否被正确设置
        if hasattr(parser, 'outline') and parser.outline is not None:
            print("✓ parser.outline 已正确设置")
            assert parser.outline == sections, "parser.outline 与返回值不一致"
            print("✓ parser.outline 与返回值一致")
        else:
            print("⚠️  parser.outline 未设置或为None")

        # 测试get_chapter_outline方法
        print(f"\n{'='*40} 测试章节大纲获取 {'='*40}")
        test_section = "1 概述"
        if test_section in sections:
            chapter_outline = parser.get_chapter_outline(sections, test_section)
            print(f"获取章节 '{test_section}' 的大纲:")
            print(f"大纲长度: {len(chapter_outline)} 字符")
            print(f"大纲预览: {chapter_outline[:300]}{'...' if len(chapter_outline) > 300 else ''}")
        else:
            print(f"测试章节 '{test_section}' 不存在")

        return sections

    except Exception as e:
        print(f"大纲解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数，用于执行所有测试用例"""
    print("开始执行大纲解析测试...")

    # 测试文件列表
    test_files = [
        "templates/可行性研究报告编制大纲.docx"
    ]

    # 查找可用的测试文件
    available_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            available_file = file_path
            break

    if available_file:
        print(f"使用测试文件: {available_file}")

        # 运行综合测试
        test_outline(available_file)

        print(f"\n{'='*60}")
        print("测试完成")
        print(f"{'='*60}")
    else:
        print("错误: 未找到可用的测试文件")


if __name__ == "__main__":
    main()
