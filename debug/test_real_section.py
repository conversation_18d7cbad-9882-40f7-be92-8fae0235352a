#!/usr/bin/env python3
"""
真实章节分析测试 - 使用实际的审查指南、编制大纲和PDF文件
但限制审查细则数量以加快测试速度
"""
import sys
import os
import json
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append('.')

# 加载环境变量
load_dotenv()

def test_real_section_analysis():
    """测试真实的章节分析功能"""
    print("="*60)
    print("真实章节分析测试")
    print("="*60)
    
    try:
        # 1. 导入模块
        print("1. 导入模块...")
        from services.document_parser import DocumentParser
        from services.model_service import ModelService
        from services.report_analyzer import ReportAnalyzer
        print("✓ 模块导入成功")
        
        # 2. 初始化服务
        print("2. 初始化服务...")
        document_parser = DocumentParser()
        model_service = ModelService()
        report_analyzer = ReportAnalyzer(model_service, document_parser)
        print("✓ 服务初始化成功")
        
        # 3. 加载模板文件
        print("3. 加载模板文件...")
        report_analyzer._load_templates()
        print(f"✓ 大纲: {len(report_analyzer.outline)} 章节")
        print(f"✓ 审查细则: {len(report_analyzer.criteria)} 项")
        print(f"✓ 审查指南: {len(report_analyzer.review_guide)} 字符")
        
        # 4. 检查PDF文件
        pdf_path = "templates/test_可行性研究报告.pdf"
        print(f"4. 检查PDF文件: {pdf_path}")
        if not os.path.exists(pdf_path):
            print("❌ PDF文件不存在")
            return False
        print("✓ PDF文件存在")
        
        # 5. 解析PDF
        print("5. 解析PDF文件...")
        project_name, pdf_sections = document_parser.parse_pdf(pdf_path)
        if not project_name:
            project_name = os.path.splitext(os.path.basename(pdf_path))[0]
        print(f"✓ 项目名称: {project_name}")
        print(f"✓ 提取章节数: {len(pdf_sections)}")
        
        # 6. 选择测试章节
        target_section = "1 概述"
        if target_section not in report_analyzer.outline:
            available_sections = list(report_analyzer.outline.keys())
            target_section = available_sections[0]
            print(f"改用第一个可用章节: {target_section}")
        
        print(f"6. 测试章节: {target_section}")
        
        # 7. 提取章节内容
        print("7. 提取章节内容...")
        section_content = report_analyzer._extract_section_content_from_pdf(
            target_section, pdf_sections
        )
        print(f"✓ 章节内容长度: {len(section_content)} 字符")
        
        # 8. 获取章节大纲
        chapter_outline = report_analyzer.outline[target_section]
        print(f"✓ 大纲要求长度: {len(chapter_outline)} 字符")
        
        # 9. 限制审查细则数量（只使用前5个）
        print("9. 准备审查细则（限制为前5个以加快测试）...")
        limited_criteria = report_analyzer.criteria[:5]
        print(f"✓ 使用审查细则数量: {len(limited_criteria)}")
        
        # 10. 执行章节分析
        print("10. 执行章节分析...")
        print("注意：这可能需要一些时间，因为需要调用AI模型...")
        
        batch_result = model_service.analyze_section_batch(
            project_name=project_name,
            section_title=target_section,
            section_content=section_content,
            all_criteria=limited_criteria,
            chapter_outline=chapter_outline,
            review_guide=report_analyzer.review_guide
        )
        
        # 11. 验证结果
        print("11. 验证分析结果...")
        if "criteria_results" not in batch_result:
            print("❌ 分析结果中缺少 criteria_results")
            return False
        
        criteria_results = batch_result["criteria_results"]
        print(f"✓ 收到 {len(criteria_results)} 个审查细则的评审结果")
        
        # 12. 验证结果格式
        print("12. 验证结果格式...")
        required_fields = ["criterion_id", "criterion_content", "result", "explanation"]
        valid_results = ["符合", "基本符合", "不符合", "不适用"]
        
        for i, criterion_result in enumerate(criteria_results):
            if not isinstance(criterion_result, dict):
                print(f"❌ 第{i+1}个结果不是字典格式")
                return False
            
            for field in required_fields:
                if field not in criterion_result:
                    print(f"❌ 第{i+1}个结果缺少字段: {field}")
                    return False
            
            result_value = criterion_result.get("result", "")
            if result_value not in valid_results:
                print(f"❌ 第{i+1}个结果的result值无效: {result_value}")
                return False
        
        print("✓ 结果格式验证通过")
        
        # 13. 统计结果
        print("13. 统计分析结果...")
        result_stats = {}
        for criterion_result in criteria_results:
            result_type = criterion_result.get("result", "未知")
            result_stats[result_type] = result_stats.get(result_type, 0) + 1
        
        print(f"✓ 结果统计: {result_stats}")
        
        # 14. 显示详细结果
        print("14. 详细分析结果:")
        for i, criterion_result in enumerate(criteria_results, 1):
            print(f"\n{i}. 审查细则ID: {criterion_result.get('criterion_id', 'N/A')}")
            print(f"   内容: {criterion_result.get('criterion_content', 'N/A')[:100]}...")
            print(f"   结果: {criterion_result.get('result', 'N/A')}")
            print(f"   说明: {criterion_result.get('explanation', 'N/A')[:200]}...")
        
        # 15. 测试JSON序列化
        print("\n15. 测试JSON序列化...")
        json_str = json.dumps(batch_result, ensure_ascii=False, indent=2)
        print(f"✓ JSON序列化成功，长度: {len(json_str)} 字符")
        
        # 16. 保存结果
        output_file = "real_section_analysis_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(json_str)
        print(f"✓ 测试结果已保存到: {output_file}")
        
        print(f"\n{'='*60}")
        print("✅ 真实章节分析测试完成！")
        print("所有功能正常，JSON格式正确")
        print(f"{'='*60}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_real_section_analysis()
    if success:
        print("\n🎉 测试成功！")
        print("✓ 编制大纲解析正常")
        print("✓ 审查指南加载正常") 
        print("✓ PDF文件解析正常")
        print("✓ 章节分析功能正常")
        print("✓ JSON格式化正常")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    main()
