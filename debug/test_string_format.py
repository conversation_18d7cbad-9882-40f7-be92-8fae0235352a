#!/usr/bin/env python3
"""
测试字符串格式的大纲解析
"""

# 模拟测试数据
test_outline_string = {
    "1 概述": """1.1 项目概况
概述项目名称、建设目标和任务、建设地点、建设内容和规模、建设工期、投资规模和资金来源、建设模式、主要技术经济指标、绩效目标等。
1.2项目单位概况
简述项目单位基本情况。拟新组建项目法人的，简述项目法人组建方案。对于政府资本金注入项目，简述项目法人基本信息、投资人(或者股东)构成及政府出资人代表等情况。
1.3 编制依据
概述国家和地方有关支持性规划、主要标准规范、专题研究成果以及其他依据。""",
    
    "2 项目建设背景和必要性": """2.1 项目建设背景
项目所在县域的经济社会、自然环境等简况，电力系统简况。
2.2 项目建设必要性
从保障民生用电、促进乡村振兴、推动县域经济发展等方面论述项目建设的必要性。""",
    
    "3 项目需求分析与预期产出": "",
    
    "4 项目选址与要素保障": """4.1 项目选址或选线
变电站选址。论述地方规划、地形地貌、压覆矿产、工程地质、土石方工程、环境、水文、历史文物、进出线条件、站用电源、交通运输、土地用途、周边设施影响等多种因素。""",
    
    "5 项目建设方案": """投资估算编制说明应包括估算编制依据和编制范围，工程量确定的主要依据和计算原则，执行的定额、指标以及主要设备、材料价格执行文件。
35千伏及以上电压等级项目按单个项目、35千伏以下电压等级项目按县域进行财务评价。""",
    
    "6 项目运营方案": """6.4 绩效管理方案
研究提出项目全生命周期关键绩效指标和绩效管理机制。""",
    
    "7 项目投融资与财务方案": "7.1 投资估算",
    
    "8 项目影响效果分析": """10.2问题与建议
针对项目需要重点关注和进一步研究解决的问题，提出相关建议。""",
    
    "9 项目风险管控方案": """9.3 风险应急预案
对于项目可能发生的风险，研究制定重大风险应急预案，明确应急处置及应急演练要求等。""",
    
    "10 研究结论及建议": "10.1 主要研究结论",
    
    "11 附表": ""
}

def test_string_format():
    """测试字符串格式的大纲数据"""
    print("测试字符串格式的大纲解析结果")
    print("="*60)
    
    total_sections = len(test_outline_string)
    non_empty_sections = 0
    
    for section_title, content in test_outline_string.items():
        content_length = len(content) if content else 0
        is_empty = not (content and content.strip())
        
        if not is_empty:
            non_empty_sections += 1
        
        print(f"\n章节: {section_title}")
        print(f"内容长度: {content_length} 字符")
        print(f"状态: {'空' if is_empty else '有内容'}")
        
        if content and content.strip():
            # 显示内容预览
            preview = content[:150] + "..." if len(content) > 150 else content
            print(f"内容预览:\n{preview}")
        else:
            print("内容: 无")
    
    print(f"\n{'='*40} 统计信息 {'='*40}")
    print(f"总章节数: {total_sections}")
    print(f"有内容的章节数: {non_empty_sections}")
    print(f"空章节数: {total_sections - non_empty_sections}")
    
    # 验证内容充足性（30字符以上）
    print(f"\n{'='*40} 内容充足性验证 {'='*40}")
    for section_title, content in test_outline_string.items():
        content_length = len(content) if content else 0
        if content_length < 30:
            print(f"⚠️  章节 '{section_title}' 内容不足（{content_length} < 30字符）")
        else:
            print(f"✓ 章节 '{section_title}' 内容充足（{content_length} 字符）")

def test_get_chapter_outline():
    """测试获取章节大纲的方法"""
    print(f"\n{'='*40} 测试章节大纲获取 {'='*40}")
    
    # 模拟get_chapter_outline方法的逻辑
    def get_chapter_outline(outline, section_title):
        if not outline:
            return "未提供大纲信息"
        
        # 直接匹配章节标题
        if section_title in outline:
            return outline[section_title]
        
        return "未找到对应章节的大纲要求"
    
    test_section = "1 概述"
    if test_section in test_outline_string:
        chapter_outline = get_chapter_outline(test_outline_string, test_section)
        print(f"获取章节 '{test_section}' 的大纲:")
        print(f"大纲长度: {len(chapter_outline)} 字符")
        print(f"大纲预览: {chapter_outline[:300]}{'...' if len(chapter_outline) > 300 else ''}")
    else:
        print(f"测试章节 '{test_section}' 不存在")

if __name__ == "__main__":
    test_string_format()
    test_get_chapter_outline()
    print(f"\n{'='*60}")
    print("测试完成 - 字符串格式验证成功！")
    print(f"{'='*60}")
