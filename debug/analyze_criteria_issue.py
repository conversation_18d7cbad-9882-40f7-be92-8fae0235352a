import json

# 分析评审细则问题
with open('debug_response.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("=== 评审细则问题分析 ===")

criteria_analysis = data.get('criteria_analysis', [])
print(f"总评审细则数: {len(criteria_analysis)}")

for i, criterion in enumerate(criteria_analysis):
    criterion_id = criterion.get('criterion_id', 'N/A')
    section_results = criterion.get('section_results', [])
    
    print(f"\n{i+1}. 细则ID: '{criterion_id}'")
    print(f"   章节数: {len(section_results)}")
    
    # 检查重复章节
    sections = [s.get('section', '') for s in section_results]
    unique_sections = set(sections)
    
    if len(sections) != len(unique_sections):
        print(f"   ⚠️  发现重复章节!")
        print(f"   总章节数: {len(sections)}, 唯一章节数: {len(unique_sections)}")
        
        # 统计每个章节的重复次数
        from collections import Counter
        section_counts = Counter(sections)
        duplicates = {section: count for section, count in section_counts.items() if count > 1}
        
        print(f"   重复章节统计:")
        for section, count in duplicates.items():
            print(f"     - {section}: {count} 次")
    else:
        print(f"   ✅ 无重复章节")

# 检查原始章节数据
print(f"\n=== 原始章节数据分析 ===")
sections_data = data.get('sections', [])
print(f"总章节数: {len(sections_data)}")

for section in sections_data:
    section_name = section.get('section', 'N/A')
    analysis = section.get('analysis', [])
    print(f"章节: {section_name}, 评审细则数: {len(analysis)}")
    
    # 检查是否有重复的criterion_id
    criterion_ids = [a.get('criterion_id', '') for a in analysis]
    unique_ids = set(criterion_ids)
    
    if len(criterion_ids) != len(unique_ids):
        print(f"   ⚠️  该章节有重复的审查细则ID!")
        from collections import Counter
        id_counts = Counter(criterion_ids)
        duplicates = {cid: count for cid, count in id_counts.items() if count > 1}
        for cid, count in duplicates.items():
            print(f"     - ID '{cid}': {count} 次")

print(f"\n=== 建议修复方案 ===")
print("1. 检查Excel文件解析，确保所有21个审查细则都被正确读取")
print("2. 修复数据重组算法，避免重复添加章节评审结果")
print("3. 确保criterion_id不为空")
print("4. 添加去重逻辑")
