#!/usr/bin/env python3
"""
测试单个章节的解析和分析功能
使用.env中实际的审查指南、编制大纲，测试PDF文件某个章节的解析结果
"""
import sys
import os
import json
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

# 加载环境变量
load_dotenv()

class SectionAnalysisTest:
    def __init__(self):
        self.document_parser = DocumentParser()
        self.model_service = ModelService()
        self.report_analyzer = ReportAnalyzer(self.model_service, self.document_parser)
        
    def test_single_section_analysis(self, pdf_path: str, target_section: str = "1 概述"):
        """测试单个章节的分析功能"""
        print(f"{'='*60}")
        print(f"测试单个章节分析功能")
        print(f"{'='*60}")
        print(f"PDF文件: {pdf_path}")
        print(f"目标章节: {target_section}")
        
        try:
            # 1. 加载模板文件
            print(f"\n1. 加载模板文件...")
            self.report_analyzer._load_templates()
            print(f"✓ 大纲: {len(self.report_analyzer.outline)} 章节")
            print(f"✓ 审查细则: {len(self.report_analyzer.criteria)} 项")
            print(f"✓ 审查指南: {len(self.report_analyzer.review_guide)} 字符")
            
            # 2. 解析PDF文件
            print(f"\n2. 解析PDF文件...")
            project_name, pdf_sections = self.document_parser.parse_pdf(pdf_path)
            if not project_name:
                project_name = os.path.splitext(os.path.basename(pdf_path))[0]
            print(f"✓ 项目名称: {project_name}")
            print(f"✓ 提取章节数: {len(pdf_sections)}")
            
            # 显示PDF中提取的章节
            print(f"\nPDF中提取的章节:")
            for i, (title, content) in enumerate(pdf_sections.items(), 1):
                print(f"  {i}. {title} ({len(content)} 字符)")
            
            # 3. 检查目标章节是否存在于大纲中
            print(f"\n3. 检查目标章节...")
            if target_section not in self.report_analyzer.outline:
                print(f"⚠️  目标章节 '{target_section}' 不在大纲中")
                print(f"可用章节: {list(self.report_analyzer.outline.keys())}")
                # 使用第一个可用章节
                target_section = list(self.report_analyzer.outline.keys())[0]
                print(f"改用章节: {target_section}")
            
            # 4. 提取章节内容
            print(f"\n4. 提取章节内容...")
            section_content = self.report_analyzer._extract_section_content_from_pdf(
                target_section, pdf_sections
            )
            print(f"✓ 章节内容长度: {len(section_content)} 字符")
            print(f"内容预览: {section_content[:200]}...")
            
            # 5. 获取章节大纲要求
            print(f"\n5. 获取章节大纲要求...")
            chapter_outline = self.report_analyzer.outline[target_section]
            print(f"✓ 大纲要求长度: {len(chapter_outline)} 字符")
            print(f"大纲预览: {chapter_outline[:200]}...")
            
            # 6. 执行章节分析
            print(f"\n6. 执行章节分析...")
            batch_result = self.model_service.analyze_section_batch(
                project_name=project_name,
                section_title=target_section,
                section_content=section_content,
                all_criteria=self.report_analyzer.criteria,
                chapter_outline=chapter_outline,
                review_guide=self.report_analyzer.review_guide
            )
            
            # 7. 验证返回结果
            print(f"\n7. 验证返回结果...")
            self._validate_analysis_result(batch_result)
            
            # 8. 显示分析结果
            print(f"\n8. 分析结果详情...")
            self._display_analysis_result(batch_result, target_section)
            
            return batch_result
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _validate_analysis_result(self, result):
        """验证分析结果的格式和内容"""
        print("验证分析结果格式...")
        
        # 检查基本结构
        assert isinstance(result, dict), f"结果应为字典，实际为: {type(result)}"
        assert "criteria_results" in result, "结果中缺少 'criteria_results' 字段"
        
        criteria_results = result["criteria_results"]
        assert isinstance(criteria_results, list), f"criteria_results应为列表，实际为: {type(criteria_results)}"
        
        print(f"✓ 基本结构验证通过")
        print(f"✓ 审查细则数量: {len(criteria_results)}")
        
        # 检查每个审查细则结果
        required_fields = ["criterion_id", "criterion_content", "result", "explanation"]
        valid_results = ["符合", "基本符合", "不符合", "不适用"]
        
        for i, criterion_result in enumerate(criteria_results):
            assert isinstance(criterion_result, dict), f"第{i+1}个结果应为字典"
            
            for field in required_fields:
                assert field in criterion_result, f"第{i+1}个结果缺少字段: {field}"
            
            result_value = criterion_result.get("result", "")
            assert result_value in valid_results, f"第{i+1}个结果的result值无效: {result_value}"
        
        print(f"✓ 所有审查细则结果格式验证通过")
    
    def _display_analysis_result(self, result, section_title):
        """显示分析结果的详细信息"""
        criteria_results = result.get("criteria_results", [])
        
        print(f"章节 '{section_title}' 的分析结果:")
        print(f"{'='*50}")
        
        # 统计结果分布
        result_stats = {}
        for criterion_result in criteria_results:
            result_type = criterion_result.get("result", "未知")
            result_stats[result_type] = result_stats.get(result_type, 0) + 1
        
        print(f"结果统计:")
        for result_type, count in result_stats.items():
            print(f"  {result_type}: {count} 项")
        
        print(f"\n详细结果 (显示前5项):")
        for i, criterion_result in enumerate(criteria_results[:5]):
            print(f"\n{i+1}. 审查细则ID: {criterion_result.get('criterion_id', 'N/A')}")
            print(f"   内容: {criterion_result.get('criterion_content', 'N/A')[:100]}...")
            print(f"   结果: {criterion_result.get('result', 'N/A')}")
            print(f"   说明: {criterion_result.get('explanation', 'N/A')[:150]}...")
        
        if len(criteria_results) > 5:
            print(f"\n... 还有 {len(criteria_results) - 5} 个审查细则结果")
    
    def test_json_format(self, result):
        """测试结果是否可以正确序列化为JSON"""
        print(f"\n测试JSON格式化...")
        try:
            json_str = json.dumps(result, ensure_ascii=False, indent=2)
            print(f"✓ JSON序列化成功，长度: {len(json_str)} 字符")
            
            # 验证可以反序列化
            parsed_result = json.loads(json_str)
            assert parsed_result == result, "JSON反序列化后数据不一致"
            print(f"✓ JSON反序列化验证通过")
            
            return json_str
        except Exception as e:
            print(f"❌ JSON格式化失败: {e}")
            return None

def main():
    """主测试函数"""
    print("开始执行章节分析测试...")
    
    # 测试文件路径
    pdf_path = "templates/test_可行性研究报告.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ 测试文件不存在: {pdf_path}")
        return
    
    # 创建测试实例
    test = SectionAnalysisTest()
    
    # 执行测试
    result = test.test_single_section_analysis(pdf_path, "1 概述")
    
    if result:
        # 测试JSON格式化
        json_result = test.test_json_format(result)
        
        if json_result:
            # 保存结果到文件
            output_file = "test_section_analysis_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_result)
            print(f"\n✓ 测试结果已保存到: {output_file}")
    
    print(f"\n{'='*60}")
    print("测试完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
