<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI测试 - 按审查细则显示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .criterion-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.9em;
            padding: 5px 10px;
        }
        .result-符合 { background-color: #d4edda; color: #155724; }
        .result-基本符合 { background-color: #fff3cd; color: #856404; }
        .result-不符合 { background-color: #f8d7da; color: #721c24; }
        .result-不适用 { background-color: #e2e3e5; color: #383d41; }
        .section-detail {
            font-size: 0.9em;
            margin-top: 10px;
        }
        .statistics-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">UI测试 - 按审查细则显示评审结果</h1>
        
        <button id="loadTestData" class="btn btn-primary mb-4">加载测试数据</button>
        
        <div id="resultSection" style="display: none;">
            <h3>评审结果</h3>
            
            <!-- 统计信息 -->
            <div id="statisticsCard" class="statistics-card">
                <h5>评审统计</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>总审查细则</h6>
                            <span id="totalCriteria" class="badge bg-primary fs-6">0</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>符合</h6>
                            <span id="compliantCount" class="badge bg-success fs-6">0</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>不符合</h6>
                            <span id="nonCompliantCount" class="badge bg-danger fs-6">0</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>合规率</h6>
                            <span id="complianceRate" class="badge bg-info fs-6">0%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 审查细则列表 -->
            <div id="criteriaList">
            </div>
            
            <div class="mt-4">
                <h4>总体评审意见</h4>
                <div id="summary" class="p-3 bg-light rounded"></div>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            "criteria_analysis": [
                {
                    "criterion_id": "1.0",
                    "criterion_content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。",
                    "overall_result": "基本符合",
                    "section_results": [
                        {
                            "section": "1 概述",
                            "has_content": true,
                            "result": "基本符合",
                            "explanation": "项目为10kV及以下电压等级，以县域（横州市）为单位编制"
                        },
                        {
                            "section": "2 项目建设背景和必要性",
                            "has_content": false,
                            "result": "不适用",
                            "explanation": "该章节与此审查细则无关"
                        }
                    ],
                    "improvement_suggestions": [
                        "建议在概述章节中明确说明项目编制单位的资质情况"
                    ]
                },
                {
                    "criterion_id": "2.0",
                    "criterion_content": "项目是否属于农村电网范围，是否来源于国家中央农网重点支持对象。",
                    "overall_result": "不符合",
                    "section_results": [
                        {
                            "section": "1 概述",
                            "has_content": true,
                            "result": "不符合",
                            "explanation": "未明确说明项目是否属于832个贫困县范围"
                        }
                    ],
                    "improvement_suggestions": [
                        "在概述章节中补充说明项目所在地是否属于832个贫困县",
                        "提供相关政策文件支撑材料"
                    ]
                }
            ],
            "statistics": {
                "total_criteria": 2,
                "total_sections": 11,
                "result_distribution": {
                    "符合": 0,
                    "基本符合": 1,
                    "不符合": 1,
                    "不适用": 0
                },
                "compliance_rate": 50.0
            },
            "summary": {
                "summary_text": "总体评审结论：基本符合\n主要问题：项目政策符合性说明不够完整\n改进建议：建议补充相关政策文件和资质材料"
            }
        };

        document.getElementById('loadTestData').addEventListener('click', () => {
            const result = testData;
            
            // 显示统计信息
            if (result.statistics) {
                const stats = result.statistics;
                document.getElementById('totalCriteria').textContent = result.criteria_analysis ? result.criteria_analysis.length : 0;
                document.getElementById('compliantCount').textContent = (stats.result_distribution['符合'] || 0) + (stats.result_distribution['基本符合'] || 0);
                document.getElementById('nonCompliantCount').textContent = stats.result_distribution['不符合'] || 0;
                document.getElementById('complianceRate').textContent = stats.compliance_rate + '%';
            }
            
            // 显示审查细则结果
            const criteriaList = document.getElementById('criteriaList');
            criteriaList.innerHTML = '';
            
            if (result.criteria_analysis) {
                result.criteria_analysis.forEach(criterion => {
                    const criterionCard = document.createElement('div');
                    criterionCard.className = 'criterion-card';
                    
                    // 生成章节详情
                    let sectionDetails = '';
                    if (criterion.section_results && criterion.section_results.length > 0) {
                        const relevantSections = criterion.section_results.filter(s => s.result !== '不适用');
                        if (relevantSections.length > 0) {
                            sectionDetails = `
                                <div class="section-detail">
                                    <strong>相关章节评审情况：</strong>
                                    <ul class="list-unstyled mt-2">
                                        ${relevantSections.map(section => `
                                            <li class="mb-1">
                                                <span class="badge result-badge result-${section.result}">${section.result}</span>
                                                ${section.section}
                                                ${section.explanation && section.explanation !== 'N/A' ? `<br><small class="text-muted">${section.explanation}</small>` : ''}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `;
                        }
                    }
                    
                    // 生成改进建议
                    let suggestions = '';
                    if (criterion.improvement_suggestions && criterion.improvement_suggestions.length > 0) {
                        suggestions = `
                            <div class="mt-3">
                                <strong>改进建议：</strong>
                                <ul class="mt-2">
                                    ${criterion.improvement_suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }
                    
                    criterionCard.innerHTML = `
                        <div class="criterion-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">审查细则 ${criterion.criterion_id}</h6>
                                    <p class="mb-0 text-muted">${criterion.criterion_content}</p>
                                </div>
                                <span class="badge result-badge result-${criterion.overall_result}">${criterion.overall_result}</span>
                            </div>
                        </div>
                        <div class="criterion-content">
                            ${sectionDetails}
                            ${suggestions}
                        </div>
                    `;
                    
                    criteriaList.appendChild(criterionCard);
                });
            }

            // 显示总体评审意见
            let summaryContent = '';
            if (result.summary) {
                if (typeof result.summary === 'string') {
                    summaryContent = result.summary.replace(/\n/g, '<br>');
                } else if (result.summary.summary_text) {
                    summaryContent = result.summary.summary_text.replace(/\n/g, '<br>');
                } else {
                    summaryContent = JSON.stringify(result.summary, null, 2);
                }
            }
            document.getElementById('summary').innerHTML = summaryContent;
            
            document.getElementById('resultSection').style.display = 'block';
        });
    </script>
</body>
</html>
