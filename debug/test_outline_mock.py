#!/usr/bin/env python3
"""
模拟大纲解析测试
模拟Word文档内容来测试解析逻辑
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 模拟Word文档段落内容（基于实际的编制大纲文档）
mock_paragraphs = [
    "可行性研究报告编制大纲",
    "",
    "1 概述",
    "1.1 项目概况",
    "概述项目名称、建设目标和任务、建设地点、建设内容和规模、建设工期、投资规模和资金来源、建设模式、主要技术经济指标、绩效目标等。",
    "1.2项目单位概况",
    "简述项目单位基本情况。拟新组建项目法人的，简述项目法人组建方案。对于政府资本金注入项目，简述项目法人基本信息、投资人(或者股东)构成及政府出资人代表等情况。",
    "1.3 编制依据",
    "概述国家和地方有关支持性规划、主要标准规范、专题研究成果以及其他依据。",
    "",
    "2 项目建设背景和必要性",
    "2.1 项目建设背景",
    "项目所在县域的经济社会、自然环境等简况，电力系统简况。",
    "2.2 项目建设必要性",
    "从保障民生用电、促进乡村振兴、推动县域经济发展等方面论述项目建设的必要性。",
    "",
    "3 项目需求分析与预期产出",
    "",
    "4 项目选址与要素保障",
    "4.1 项目选址或选线",
    "变电站选址。论述地方规划、地形地貌、压覆矿产、工程地质、土石方工程、环境、水文、历史文物、进出线条件、站用电源、交通运输、土地用途、周边设施影响等多种因素。",
    "",
    "5 项目建设方案",
    "投资估算编制说明应包括估算编制依据和编制范围，工程量确定的主要依据和计算原则，执行的定额、指标以及主要设备、材料价格执行文件。",
    "7.2 盈利能力分析",  # 这是错误分配的内容，应该被过滤
    "35千伏及以上电压等级项目按单个项目、35千伏以下电压等级项目按县域进行财务评价。",
    "",
    "6 项目运营方案",
    "6.4 绩效管理方案",
    "研究提出项目全生命周期关键绩效指标和绩效管理机制。",
    "",
    "7 项目投融资与财务方案",
    "7.1 投资估算",
    "",
    "8 项目影响效果分析",
    "10.2问题与建议",  # 这是错误分配的内容，应该被过滤
    "针对项目需要重点关注和进一步研究解决的问题，提出相关建议。",
    "",
    "9 项目风险管控方案",
    "9.3 风险应急预案",
    "对于项目可能发生的风险，研究制定重大风险应急预案，明确应急处置及应急演练要求等。",
    "",
    "10 研究结论及建议",
    "10.1 主要研究结论",
    "",
    "11 附表",
    ""
]

class MockDocument:
    """模拟docx.Document类"""
    def __init__(self, paragraphs):
        self.paragraphs = [MockParagraph(text) for text in paragraphs]

class MockParagraph:
    """模拟docx段落类"""
    def __init__(self, text):
        self.text = text

def test_optimized_parse_outline():
    """测试优化后的大纲解析逻辑（字符串格式）"""
    # 导入我们的解析器类
    try:
        from services.document_parser import DocumentParser

        # 创建解析器实例
        parser = DocumentParser()

        # 模拟新的parse_outline方法的核心逻辑
        outline = {}
        current_chapter = None
        current_content = []

        print("开始解析模拟的Word文档内容（新的字符串格式）...")
        print(f"总段落数: {len(mock_paragraphs)}")

        for i, text in enumerate(mock_paragraphs):
            text = text.strip()
            if not text:
                continue

            print(f"处理第{i+1}行: {text}")

            # 只识别主要章节标题（1-11章）
            is_main_title, chapter_title = parser._is_main_chapter_title(text)

            if is_main_title:
                # 保存上一个章节的内容
                if current_chapter and current_content:
                    content_text = '\n'.join(current_content)
                    outline[current_chapter] = content_text
                    print(f"  ✓ 保存章节 {current_chapter}: {len(content_text)} 字符")

                # 开始新章节
                current_chapter = chapter_title
                current_content = []
                print(f"  ✓ 找到主章节: {chapter_title}")
            elif current_chapter and text:
                # 将所有内容（包括子标题）都添加到当前章节
                current_content.append(text)
                print(f"  → 添加内容到 {current_chapter}: {text[:50]}...")

        # 保存最后一个章节的内容
        if current_chapter and current_content:
            content_text = '\n'.join(current_content)
            outline[current_chapter] = content_text
            print(f"  ✓ 保存最后章节 {current_chapter}: {len(content_text)} 字符")

        # 验证每个章节都有足够的内容
        parser._validate_outline_content_string(outline)

        # 显示最终结果
        print(f"\n{'='*60}")
        print("最终解析结果（字符串格式）:")
        print(f"{'='*60}")

        for chapter_title, content in outline.items():
            content_length = len(content) if content else 0
            print(f"\n章节: {chapter_title}")
            print(f"内容长度: {content_length} 字符")

            if content:
                # 显示内容预览
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"内容预览:\n{preview}")
            else:
                print("内容: 无")

        return outline

    except ImportError as e:
        print(f"导入错误: {e}")
        return None
    except Exception as e:
        print(f"其他错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_optimized_parse_outline()
    if result:
        print(f"\n测试成功！解析到 {len(result)} 个章节")
    else:
        print("\n测试失败！")
