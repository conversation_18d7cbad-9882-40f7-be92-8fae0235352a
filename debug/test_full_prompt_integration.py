#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的提示词集成测试
测试整个报告评审流程是否正常工作
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.model_service import ModelService


def test_section_analysis_with_new_prompts():
    """测试使用新提示词的章节分析功能"""
    print("\n=== 测试章节分析功能（新提示词） ===")

    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"

        model_service = ModelService()

        # 模拟章节分析
        result = model_service.analyze_section_batch(
            project_name="测试项目",
            section_title="1. 概述",
            section_content="本项目位于广西横州市，主要建设10kV配电线路...",
            all_criteria=[
                {"id": "1.1", "content": "项目概况应包括项目名称、建设地点等基本信息"},
                {"id": "1.2", "content": "编制依据应符合国家相关标准"}
            ],
            chapter_outline="1. 概述\n1.1 项目概况\n1.2 编制依据",
            review_guide="审查指南：请按照国家相关标准进行评审"
        )

        print(f"章节分析结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

        # 验证结果结构
        if "criteria_results" in result and len(result["criteria_results"]) == 2:
            print("✓ 章节分析功能正常工作")
            return True
        else:
            print("✗ 章节分析结果结构异常")
            return False

    except Exception as e:
        print(f"✗ 章节分析测试失败: {e}")
        return False


def test_comprehensive_batch_with_new_prompts():
    """测试使用新提示词的批量综合分析功能"""
    print("\n=== 测试批量综合分析功能（新提示词） ===")

    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"

        model_service = ModelService()

        # 模拟评审结果
        review_results = {
            "criterion_results": [
                {
                    "criterion_id": "1.1",
                    "criterion_content": "项目概况应包括项目名称、建设地点等基本信息",
                    "sections": [
                        {"section_name": "1. 概述", "result": "符合", "explanation": "包含了项目名称和建设地点"}
                    ]
                },
                {
                    "criterion_id": "1.2",
                    "criterion_content": "编制依据应符合国家相关标准",
                    "sections": [
                        {"section_name": "1. 概述", "result": "基本符合", "explanation": "编制依据基本完整"}
                    ]
                }
            ]
        }

        # 执行批量综合分析
        result = model_service.analyze_criteria_comprehensive_batch(
            review_guide="审查指南内容",
            outline="编制大纲内容",
            review_results=review_results
        )

        print(f"批量综合分析结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

        # 验证结果结构
        if isinstance(result, list) and len(result) == 2:
            print("✓ 批量综合分析功能正常工作")
            return True
        else:
            print("✗ 批量综合分析结果结构异常")
            return False

    except Exception as e:
        print(f"✗ 批量综合分析测试失败: {e}")
        return False


def test_summary_review_with_new_prompts():
    """测试使用新提示词的汇总评审功能"""
    print("\n=== 测试汇总评审功能（新提示词） ===")

    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"

        model_service = ModelService()

        # 模拟评审结果
        review_results = [
            {"section": "1. 概述", "criteria_count": 5, "compliance_rate": "80%"},
            {"section": "2. 项目建设必要性", "criteria_count": 3, "compliance_rate": "100%"}
        ]

        # 执行汇总评审
        result = model_service.summarize_review(review_results)

        print(f"汇总评审结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

        # 验证结果结构
        if "summary" in result:
            print("✓ 汇总评审功能正常工作")
            return True
        else:
            print("✗ 汇总评审结果结构异常")
            return False

    except Exception as e:
        print(f"✗ 汇总评审测试失败: {e}")
        return False


def test_prompt_template_loading():
    """测试提示词模板加载"""
    print("\n=== 测试提示词模板加载 ===")

    try:
        model_service = ModelService()
        prompt_service = model_service.prompt_service

        # 测试所有模板是否能正常加载
        templates = [
            ("章节分析", prompt_service.get_section_analysis_prompt),
            ("批量综合分析", prompt_service.get_comprehensive_batch_prompt),
            ("汇总评审", prompt_service.get_summary_review_prompt)
        ]

        for name, func in templates:
            try:
                prompt = func()
                print(f"✓ {name}模板加载成功，长度: {len(prompt)} 字符")
            except Exception as e:
                print(f"✗ {name}模板加载失败: {e}")
                return False

        print("✓ 所有提示词模板加载正常")
        return True

    except Exception as e:
        print(f"✗ 提示词模板加载测试失败: {e}")
        return False


def test_environment_variables():
    """测试环境变量配置"""
    print("\n=== 测试环境变量配置 ===")

    try:
        # 检查提示词模板路径配置
        template_vars = [
            "PROMPT_SECTION_ANALYSIS",
            "PROMPT_COMPREHENSIVE_BATCH",
            "PROMPT_SUMMARY_REVIEW"
        ]

        for var in template_vars:
            value = os.getenv(var)
            if value:
                print(f"✓ {var}: {value}")
                # 检查文件是否存在
                if os.path.exists(value):
                    print(f"  文件存在: {value}")
                else:
                    print(f"  ⚠️ 文件不存在: {value}")
            else:
                print(f"✗ {var}: 未配置")
                return False

        print("✓ 环境变量配置正常")
        return True

    except Exception as e:
        print(f"✗ 环境变量测试失败: {e}")
        return False


def test_stats_tracking():
    """测试统计信息跟踪"""
    print("\n=== 测试统计信息跟踪 ===")

    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"

        model_service = ModelService()

        # 重置统计信息
        model_service.reset_stats()
        initial_stats = model_service.get_stats()
        print(f"初始统计信息: {initial_stats}")

        # 执行一次章节分析
        model_service.analyze_section_batch(
            project_name="测试项目",
            section_title="1. 概述",
            section_content="测试内容",
            all_criteria=[{"id": "1.1", "content": "测试细则"}]
        )

        # 检查统计信息是否更新
        final_stats = model_service.get_stats()
        print(f"最终统计信息: {final_stats}")

        if final_stats["api_calls"] > initial_stats["api_calls"]:
            print("✓ 统计信息跟踪正常")
            return True
        else:
            print("✗ 统计信息未更新")
            return False

    except Exception as e:
        print(f"✗ 统计信息测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始完整的提示词集成测试...")

    test_results = []

    # 运行所有测试
    test_results.append(test_environment_variables())
    test_results.append(test_prompt_template_loading())
    test_results.append(test_section_analysis_with_new_prompts())
    test_results.append(test_comprehensive_batch_with_new_prompts())
    test_results.append(test_summary_review_with_new_prompts())
    test_results.append(test_stats_tracking())

    # 统计结果
    passed = sum(test_results)
    total = len(test_results)

    print(f"\n=== 集成测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")

    if passed == total:
        print("🎉 所有集成测试通过！提示词优化功能完全正常")
        return True
    else:
        print("❌ 部分集成测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
