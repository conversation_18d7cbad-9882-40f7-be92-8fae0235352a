#!/usr/bin/env python3
"""
测试优化后的API功能
"""

import requests
import os
import json

def test_api_with_debug():
    """测试API并查看调试信息"""
    print("=== 测试优化后的API功能 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    # API端点
    url = "http://localhost:8001/analyze"
    
    # 测试文件路径
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")
    
    if not os.path.exists(test_pdf):
        print(f"测试文件不存在: {test_pdf}")
        return False
    
    try:
        print(f"正在上传文件: {test_pdf}")
        
        # 准备文件上传
        with open(test_pdf, 'rb') as f:
            files = {'pdf_file': ('test_可行性研究报告.pdf', f, 'application/pdf')}
            
            print("发送API请求...")
            response = requests.post(url, files=files, timeout=120)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ API调用成功")
            
            # 检查响应结构
            expected_keys = ["criteria_analysis", "sections", "summary", "statistics"]
            for key in expected_keys:
                if key in result:
                    print(f"✓ 包含字段: {key}")
                else:
                    print(f"✗ 缺少字段: {key}")
            
            # 检查审查细则分析结果
            if "criteria_analysis" in result:
                criteria_count = len(result["criteria_analysis"])
                print(f"✓ 获得 {criteria_count} 个审查细则的分析结果")
                
                if criteria_count > 0:
                    first_criterion = result["criteria_analysis"][0]
                    print(f"第一个审查细则ID: {first_criterion.get('criterion_id', 'unknown')}")
                    print(f"评审结果: {first_criterion.get('overall_result', 'unknown')}")
            
            # 检查统计信息
            if "statistics" in result:
                stats = result["statistics"]
                print(f"✓ 统计信息: {stats}")
            
            # 保存结果用于调试
            output_file = "test_api_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✓ 结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("✗ API调用超时")
        return False
    except Exception as e:
        print(f"✗ API调用异常: {e}")
        return False

def test_frontend_access():
    """测试前端页面访问"""
    print("\n=== 测试前端页面访问 ===")
    
    try:
        url = "http://localhost:8001/"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✓ 前端页面访问成功")
            
            # 检查页面是否包含调试面板相关内容
            html_content = response.text
            debug_features = ["debugPanel", "addDebugLog", "debug-panel"]
            
            for feature in debug_features:
                if feature in html_content:
                    print(f"✓ 前端包含调试功能: {feature}")
                else:
                    print(f"✗ 前端缺少调试功能: {feature}")
            
            return True
        else:
            print(f"✗ 前端页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 前端页面访问异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的API和前端功能...\n")
    
    tests = [
        ("前端页面访问", test_frontend_access),
        ("API功能测试", test_api_with_debug)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有API测试通过！优化功能正常工作。")
    else:
        print("⚠️  部分API测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
