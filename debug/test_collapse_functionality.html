<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折叠功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .criterion-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
        .result-符合 { background-color: #198754; color: white; }
        .result-基本符合 { background-color: #fd7e14; color: white; }
        .result-不符合 { background-color: #dc3545; color: white; }
        .result-不适用 { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>折叠功能测试页面</h2>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
            <p>此页面专门测试"查看各章节详细评审情况"的折叠展开功能。</p>
            <p>点击下方的按钮应该能够展开/收起章节详情。</p>
        </div>

        <!-- 测试折叠功能 -->
        <div class="criterion-card">
            <div class="criterion-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">审查细则 1.1</h6>
                        <p class="mb-0 text-muted">35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。</p>
                    </div>
                    <span class="badge result-badge result-基本符合">基本符合</span>
                </div>
            </div>
            <div class="criterion-content">
                <!-- 综合分析 -->
                <div class="alert alert-info mb-3">
                    <h6><i class="fas fa-chart-line"></i> 全文综合分析：</h6>
                    <p class="mb-0">经过对报告全文的综合分析，该项目在电压等级分类和可研报告编制方面基本符合要求。</p>
                </div>

                <!-- 关键发现 -->
                <div class="mb-3">
                    <strong><i class="fas fa-search"></i> 关键发现：</strong>
                    <ul class="list-unstyled mt-2">
                        <li class="mb-1"><i class="fas fa-dot-circle text-primary"></i> 项目明确属于35千伏以下电压等级</li>
                        <li class="mb-1"><i class="fas fa-dot-circle text-primary"></i> 按县域为单位进行编制</li>
                    </ul>
                </div>

                <!-- 改进建议 -->
                <div class="mb-3">
                    <strong><i class="fas fa-lightbulb"></i> 改进建议：</strong>
                    <ul class="list-unstyled mt-2">
                        <li class="mb-1"><i class="fas fa-arrow-right text-success"></i> 建议补充电压等级分析</li>
                        <li class="mb-1"><i class="fas fa-arrow-right text-success"></i> 完善县域规划衔接说明</li>
                    </ul>
                </div>

                <!-- 章节详情（折叠显示） -->
                <div class="mt-3">
                    <p>
                        <a class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" href="#collapse-1-1" role="button" aria-expanded="false" aria-controls="collapse-1-1">
                            <i class="fas fa-list"></i> 查看各章节详细评审情况 (3个相关章节)
                        </a>
                    </p>
                    <div class="collapse" id="collapse-1-1">
                        <div class="card card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge result-badge result-符合">符合</span>
                                    <strong>1 概述</strong>
                                    <br><small class="text-muted">概述章节明确了项目的电压等级和编制依据</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge result-badge result-基本符合">基本符合</span>
                                    <strong>2 项目建设背景和必要性</strong>
                                    <br><small class="text-muted">背景描述较为充分，但与县域规划的关联性需要加强</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge result-badge result-符合">符合</span>
                                    <strong>5 项目建设方案</strong>
                                    <br><small class="text-muted">建设方案技术路线清晰，符合电压等级要求</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二个测试案例 -->
        <div class="criterion-card">
            <div class="criterion-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">审查细则 1.2</h6>
                        <p class="mb-0 text-muted">项目可行性研究报告应由具备相应能力的工程咨询单位编制。</p>
                    </div>
                    <span class="badge result-badge result-符合">符合</span>
                </div>
            </div>
            <div class="criterion-content">
                <div class="alert alert-info mb-3">
                    <h6><i class="fas fa-chart-line"></i> 全文综合分析：</h6>
                    <p class="mb-0">报告编制单位具备相应资质，符合要求。</p>
                </div>

                <div class="mt-3">
                    <p>
                        <a class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" href="#collapse-1-2" role="button" aria-expanded="false" aria-controls="collapse-1-2">
                            <i class="fas fa-list"></i> 查看各章节详细评审情况 (2个相关章节)
                        </a>
                    </p>
                    <div class="collapse" id="collapse-1-2">
                        <div class="card card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge result-badge result-符合">符合</span>
                                    <strong>1 概述</strong>
                                    <br><small class="text-muted">明确了编制单位信息</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge result-badge result-符合">符合</span>
                                    <strong>11 附表</strong>
                                    <br><small class="text-muted">提供了相关资质证明</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="mt-4">
            <h5>功能测试</h5>
            <button class="btn btn-primary" onclick="testAllCollapse()">展开所有折叠项</button>
            <button class="btn btn-secondary ms-2" onclick="hideAllCollapse()">收起所有折叠项</button>
        </div>

        <div class="mt-3">
            <div class="alert alert-success" id="testResult" style="display: none;">
                <strong>测试结果：</strong> <span id="testMessage"></span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testAllCollapse() {
            const collapseElements = document.querySelectorAll('.collapse');
            let successCount = 0;
            
            collapseElements.forEach(element => {
                const bsCollapse = new bootstrap.Collapse(element, {
                    show: true
                });
                successCount++;
            });
            
            showTestResult(`成功展开 ${successCount} 个折叠项`, 'success');
        }
        
        function hideAllCollapse() {
            const collapseElements = document.querySelectorAll('.collapse');
            let successCount = 0;
            
            collapseElements.forEach(element => {
                const bsCollapse = new bootstrap.Collapse(element, {
                    hide: true
                });
                successCount++;
            });
            
            showTestResult(`成功收起 ${successCount} 个折叠项`, 'success');
        }
        
        function showTestResult(message, type) {
            const resultDiv = document.getElementById('testResult');
            const messageSpan = document.getElementById('testMessage');
            
            messageSpan.textContent = message;
            resultDiv.className = `alert alert-${type}`;
            resultDiv.style.display = 'block';
            
            // 3秒后隐藏结果
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }
        
        // 页面加载完成后的测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，Bootstrap版本:', bootstrap.Tooltip.VERSION);
            
            // 测试Bootstrap是否正确加载
            if (typeof bootstrap !== 'undefined') {
                showTestResult('Bootstrap JavaScript 加载成功！折叠功能应该正常工作。', 'success');
            } else {
                showTestResult('Bootstrap JavaScript 加载失败！', 'danger');
            }
        });
    </script>
</body>
</html>
