#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复和改进功能
包括：
1. 解决汇总分析生成失败: 'ModelService' object has no attribute 'call_model'
2. 汇总分析由大模型对多个报告的评审结果总结分析
3. 评审管理界面的多报告评审UI优化
"""

import os
import sys
import json
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_topic_summary_service_fix():
    """测试专题汇总服务修复"""
    print("=" * 50)
    print("测试1: 专题汇总服务修复")
    print("=" * 50)
    
    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        from services.topic_summary_service import TopicSummaryService
        from services.model_service import ModelService
        
        model_service = ModelService()
        topic_summary_service = TopicSummaryService(model_service)
        
        # 测试_call_model_for_summary方法
        test_prompt = "这是一个测试提示词，用于验证汇总分析功能。"
        
        print("调用_call_model_for_summary方法...")
        result = topic_summary_service._call_model_for_summary(test_prompt)
        
        print(f"汇总分析结果: {result[:200]}...")
        
        if "[测试模式]" in result:
            print("✅ 专题汇总服务修复成功，测试模式正常工作")
            return True
        else:
            print("❌ 专题汇总服务修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试专题汇总服务修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_report_summary_service():
    """测试多报告汇总分析服务"""
    print("=" * 50)
    print("测试2: 多报告汇总分析服务")
    print("=" * 50)
    
    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        from services.multi_report_summary_service import MultiReportSummaryService
        from services.model_service import ModelService
        
        model_service = ModelService()
        multi_report_service = MultiReportSummaryService(model_service)
        
        # 模拟报告数据
        test_report_ids = ["report_1", "report_2", "report_3"]
        
        print(f"测试多报告汇总分析，报告数量: {len(test_report_ids)}")
        
        # 由于需要真实的报告数据，这里只测试服务初始化
        print("✅ 多报告汇总分析服务初始化成功")
        
        # 测试_call_model_for_summary方法
        test_prompt = "这是一个多报告汇总分析测试提示词。"
        result = multi_report_service._call_model_for_summary(test_prompt)
        
        print(f"多报告汇总分析结果: {result[:200]}...")
        
        if "[测试模式]" in result:
            print("✅ 多报告汇总分析服务正常工作")
            return True
        else:
            print("❌ 多报告汇总分析服务异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试多报告汇总分析服务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_review_management_ui():
    """测试评审管理界面UI优化"""
    print("=" * 50)
    print("测试3: 评审管理界面UI优化")
    print("=" * 50)
    
    html_file = project_root / "templates" / "review_management.html"
    
    if not html_file.exists():
        print("❌ 找不到评审管理页面文件")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查UI优化相关的元素
    ui_checks = [
        ('批量操作按钮', 'onclick="startBatchReview()"'),
        ('汇总分析按钮', 'onclick="generateMultiReportSummary()"'),
        ('全选按钮', 'onclick="selectAllReports()"'),
        ('清空按钮', 'onclick="clearAllReports()"'),
        ('选择计数显示', 'id="selectedCount"'),
        ('复选框', 'type="checkbox"'),
        ('选择状态切换', 'toggleReportSelection')
    ]
    
    missing_elements = []
    for name, element in ui_checks:
        if element not in content:
            missing_elements.append(name)
        else:
            print(f"✅ {name} 存在")
    
    if not missing_elements:
        print("✅ 评审管理界面UI优化完成")
        return True
    else:
        print(f"❌ 缺少UI元素: {missing_elements}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("=" * 50)
    print("测试4: API端点检查")
    print("=" * 50)
    
    # 检查main.py中的API路由
    main_file = project_root / "main.py"
    
    if not main_file.exists():
        print("❌ 找不到main.py文件")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查多报告汇总分析相关的API
    api_checks = [
        ('多报告汇总服务导入', 'from services.multi_report_summary_service import MultiReportSummaryService'),
        ('多报告汇总服务初始化', 'multi_report_summary_service = MultiReportSummaryService'),
        ('创建多报告汇总API', '@app.post("/api/multi-report-summary")'),
        ('获取多报告汇总列表API', '@app.get("/api/multi-report-summaries")'),
        ('获取单个多报告汇总API', '@app.get("/api/multi-report-summaries/{summary_id}")'),
        ('删除多报告汇总API', '@app.delete("/api/multi-report-summaries/{summary_id}")')
    ]
    
    missing_apis = []
    for name, api in api_checks:
        if api in content:
            print(f"✅ {name} 存在")
        else:
            missing_apis.append(name)
    
    if not missing_apis:
        print("✅ 所有API端点检查通过")
        return True
    else:
        print(f"❌ 缺少API端点: {missing_apis}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("=" * 50)
    print("测试5: 文件结构检查")
    print("=" * 50)
    
    required_files = [
        'services/multi_report_summary_service.py',
        'services/topic_summary_service.py',
        'templates/review_management.html',
        'main.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ 所有必需文件存在")
        return True
    else:
        print(f"❌ 缺少文件: {missing_files}")
        return False

def test_javascript_functions():
    """测试JavaScript函数"""
    print("=" * 50)
    print("测试6: JavaScript函数检查")
    print("=" * 50)
    
    html_file = project_root / "templates" / "review_management.html"
    
    if not html_file.exists():
        print("❌ 找不到评审管理页面文件")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查JavaScript函数
    js_functions = [
        'function toggleReportSelection(',
        'function updateSelectedCount(',
        'function selectAllReports(',
        'function clearAllReports(',
        'function startBatchReview(',
        'function generateMultiReportSummary('
    ]
    
    missing_functions = []
    for func in js_functions:
        if func in content:
            print(f"✅ {func} 存在")
        else:
            missing_functions.append(func)
    
    if not missing_functions:
        print("✅ 所有JavaScript函数检查通过")
        return True
    else:
        print(f"❌ 缺少JavaScript函数: {missing_functions}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复和改进功能...")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("专题汇总服务修复", test_topic_summary_service_fix()))
    test_results.append(("多报告汇总分析服务", test_multi_report_summary_service()))
    test_results.append(("评审管理界面UI优化", test_review_management_ui()))
    test_results.append(("API端点检查", test_api_endpoints()))
    test_results.append(("文件结构检查", test_file_structure()))
    test_results.append(("JavaScript函数检查", test_javascript_functions()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {total} 项测试，通过: {passed} 项，失败: {total - passed} 项")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有修复和改进功能测试通过！")
        print("\n功能总结:")
        print("1. ✅ 修复了汇总分析生成失败的问题")
        print("2. ✅ 实现了多报告汇总分析功能")
        print("3. ✅ 优化了评审管理界面的批量操作")
        print("4. ✅ 添加了完整的API支持")
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
