#!/usr/bin/env python3
"""
测试PDF解析优化功能
验证markdown文件读取、解析规则和格式检查
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser

def test_pdf_optimization():
    """测试PDF解析优化功能"""
    print("=" * 60)
    print("🧪 测试PDF解析优化功能")
    print("=" * 60)
    
    try:
        # 1. 初始化解析器
        print("1. 初始化DocumentParser...")
        parser = DocumentParser()
        print("✓ 初始化完成")
        
        # 2. 测试文件路径
        test_files = [
            "templates/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf",
            "templates/test_可行性研究报告.pdf"
        ]
        
        for pdf_path in test_files:
            if os.path.exists(pdf_path):
                print(f"\n2. 测试文件: {pdf_path}")
                test_single_pdf(parser, pdf_path)
                break
        else:
            print("❌ 未找到测试PDF文件")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_single_pdf(parser: DocumentParser, pdf_path: str):
    """测试单个PDF文件"""
    print(f"\n{'='*50}")
    print(f"测试文件: {os.path.basename(pdf_path)}")
    print(f"{'='*50}")
    
    try:
        # 检查是否存在同名markdown文件
        md_path = os.path.splitext(pdf_path)[0] + '.md'
        print(f"检查markdown文件: {md_path}")
        
        if os.path.exists(md_path):
            print("✓ 找到同名markdown文件")
            # 读取markdown文件内容预览
            with open(md_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"  文件大小: {len(content)} 字符")
            
            # 检查markdown格式
            lines = content.split('\n')
            h1_count = sum(1 for line in lines if line.strip().startswith('# ') and not line.strip().startswith('## '))
            h2_count = sum(1 for line in lines if line.strip().startswith('## '))
            
            print(f"  一级标题(#)数量: {h1_count}")
            print(f"  二级标题(##)数量: {h2_count}")
            
            # 显示前几个一级标题
            print("  一级标题列表:")
            h1_titles = [line.strip()[2:].strip() for line in lines if line.strip().startswith('# ') and not line.strip().startswith('## ')]
            for i, title in enumerate(h1_titles[:5]):
                print(f"    {i+1}. {title}")
            if len(h1_titles) > 5:
                print(f"    ... 还有 {len(h1_titles) - 5} 个标题")
        else:
            print("❌ 未找到同名markdown文件")
        
        # 3. 执行PDF解析
        print(f"\n3. 执行PDF解析...")
        project_name, sections = parser.parse_pdf(pdf_path)
        
        print(f"✓ 解析完成")
        print(f"  项目名称: {project_name}")
        print(f"  解析到 {len(sections)} 个章节")
        
        # 4. 验证章节结构
        print(f"\n4. 验证章节结构...")
        
        # 检查是否有资质证书章节
        if "资质证书" in sections:
            print("✓ 找到资质证书章节")
            cert_content = sections["资质证书"]
            print(f"  资质证书内容长度: {len(cert_content)} 字符")
            if cert_content.strip():
                print(f"  内容预览: {cert_content[:100]}...")
        else:
            print("❌ 未找到资质证书章节")
        
        # 检查标准章节
        standard_sections = [f"{num} {title}" for num, title in parser.standard_outline.items()]
        found_standard_sections = 0
        
        for section_name in standard_sections:
            if section_name in sections and sections[section_name].strip():
                found_standard_sections += 1
        
        print(f"  标准章节: {found_standard_sections}/{len(standard_sections)} 个有内容")
        
        # 5. 检查二级标题格式
        print(f"\n5. 检查二级标题格式...")
        total_h2_titles = 0
        valid_h2_titles = 0
        
        for section_name, content in sections.items():
            if not content.strip():
                continue
                
            lines = content.split('\n')
            section_h2_count = 0
            section_valid_h2 = 0
            
            for line in lines:
                line_stripped = line.strip()
                if line_stripped.startswith('## '):
                    total_h2_titles += 1
                    section_h2_count += 1
                    subsection_title = line_stripped[3:].strip()
                    
                    # 验证格式
                    if parser._validate_subsection_format(subsection_title):
                        valid_h2_titles += 1
                        section_valid_h2 += 1
                        print(f"  ✓ {section_name}: {subsection_title}")
                    else:
                        print(f"  ❌ {section_name}: {subsection_title} (格式不符合要求)")
            
            if section_h2_count > 0:
                print(f"    {section_name}: {section_valid_h2}/{section_h2_count} 个格式正确")
        
        print(f"\n总计: {valid_h2_titles}/{total_h2_titles} 个二级标题格式正确")
        
        # 6. 输出详细的章节信息
        print(f"\n6. 章节详细信息:")
        for section_name, content in sections.items():
            content_length = len(content.strip()) if content else 0
            status = "✓ 有内容" if content_length > 0 else "✗ 无内容"
            print(f"  {section_name}: {status} ({content_length}字符)")
            
            # 显示内容开头
            if content_length > 0:
                first_lines = content.strip().split('\n')[:3]
                for i, line in enumerate(first_lines):
                    if line.strip():
                        print(f"    第{i+1}行: {line.strip()[:50]}...")
                        break
        
        # 7. 验证特定要求
        print(f"\n7. 验证特定要求:")
        
        # 检查概述章节是否存在
        overview_found = False
        for section_name in sections:
            if "概述" in section_name and sections[section_name].strip():
                overview_found = True
                print(f"  ✓ 找到概述章节: {section_name}")
                break
        
        if not overview_found:
            print("  ❌ 未找到概述章节")
        
        # 检查资质证书章节是否在概述之前
        section_names = list(sections.keys())
        cert_index = section_names.index("资质证书") if "资质证书" in section_names else -1
        overview_index = -1
        
        for i, name in enumerate(section_names):
            if "概述" in name:
                overview_index = i
                break
        
        if cert_index >= 0 and overview_index >= 0:
            if cert_index < overview_index:
                print("  ✓ 资质证书章节在概述之前")
            else:
                print("  ❌ 资质证书章节不在概述之前")
        
        print(f"\n{'='*50}")
        print("✅ 单个文件测试完成")
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"❌ 测试文件失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    success = test_pdf_optimization()
    sys.exit(0 if success else 1)
