import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from services.report_service import ReportService
from services.review_service import ReviewService
from services.model_service import ModelService

class MultiReportSummaryService:
    """多报告汇总分析服务"""
    
    def __init__(self, model_service: ModelService):
        self.model_service = model_service
        self.report_service = ReportService()
        self.review_service = ReviewService()
        self.data_dir = "data"
        self.summaries_file = os.path.join(self.data_dir, "multi_report_summaries.json")
        self._ensure_data_dir()
        self._init_summaries_file()
    
    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def _init_summaries_file(self):
        """初始化汇总文件"""
        if not os.path.exists(self.summaries_file):
            self._save_summaries([])
    
    def _load_summaries(self) -> List[Dict[str, Any]]:
        """加载汇总数据"""
        try:
            with open(self.summaries_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_summaries(self, summaries: List[Dict[str, Any]]):
        """保存汇总数据"""
        with open(self.summaries_file, 'w', encoding='utf-8') as f:
            json.dump(summaries, f, ensure_ascii=False, indent=2)
    
    def generate_multi_report_summary(self, report_ids: List[str]) -> Dict[str, Any]:
        """生成多报告汇总分析"""
        if not report_ids:
            return {"error": "未选择任何报告"}
        
        # 获取报告和评审信息
        reports_data = []
        for report_id in report_ids:
            report = self.report_service.get_report(report_id)
            if not report:
                continue
                
            latest_review = self.review_service.get_latest_review_for_report(report_id)
            if not latest_review:
                continue
                
            reports_data.append({
                "report": report,
                "review": latest_review
            })
        
        if not reports_data:
            return {"error": "所选报告中没有已评审的报告"}
        
        # 按审查细则分组分析
        criteria_analysis = self._analyze_by_criteria(reports_data)
        
        # 生成汇总报告
        summary_result = self._generate_summary_analysis(reports_data, criteria_analysis)
        
        # 保存汇总记录
        summary_record = {
            "id": str(uuid.uuid4()),
            "report_ids": report_ids,
            "reports_count": len(reports_data),
            "summary_result": summary_result,
            "criteria_analysis": criteria_analysis,
            "created_at": datetime.now().isoformat()
        }
        
        summaries = self._load_summaries()
        summaries.append(summary_record)
        self._save_summaries(summaries)
        
        return summary_record
    
    def _analyze_by_criteria(self, reports_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """按审查细则分组分析"""
        criteria_results = {}
        
        for report_data in reports_data:
            report = report_data['report']
            review = report_data['review']
            review_results = review['result'].get('review_results', [])
            
            for result in review_results:
                criterion_id = result.get('criterion_id', '')
                criterion_text = result.get('criterion_text', '')
                
                if criterion_id not in criteria_results:
                    criteria_results[criterion_id] = {
                        "criterion_text": criterion_text,
                        "reports": []
                    }
                
                criteria_results[criterion_id]["reports"].append({
                    "report_id": report['id'],
                    "report_name": report['name'],
                    "result": result.get('result', ''),
                    "analysis": result.get('analysis', ''),
                    "compliance": result.get('result', '') in ['符合', '基本符合']
                })
        
        # 计算每个细则的统计信息
        for criterion_id, criterion_data in criteria_results.items():
            reports = criterion_data["reports"]
            total_reports = len(reports)
            compliant_reports = sum(1 for r in reports if r["compliance"])
            
            criterion_data["statistics"] = {
                "total_reports": total_reports,
                "compliant_reports": compliant_reports,
                "compliance_rate": round(compliant_reports / total_reports * 100, 2) if total_reports > 0 else 0
            }
        
        return criteria_results
    
    def _generate_summary_analysis(self, reports_data: List[Dict[str, Any]], criteria_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总分析"""
        # 构建提示词
        prompt = self._build_summary_prompt(reports_data, criteria_analysis)
        
        try:
            summary_text = self._call_model_for_summary(prompt)
        except Exception as e:
            summary_text = f"汇总分析生成失败: {str(e)}"
        
        # 计算整体统计信息
        total_reports = len(reports_data)
        overall_compliance_rates = []
        
        for report_data in reports_data:
            review = report_data['review']
            compliance_rate = review['result'].get('statistics', {}).get('compliance_rate', 0)
            overall_compliance_rates.append(compliance_rate)
        
        average_compliance_rate = sum(overall_compliance_rates) / len(overall_compliance_rates) if overall_compliance_rates else 0
        
        return {
            "summary_text": summary_text,
            "statistics": {
                "total_reports": total_reports,
                "average_compliance_rate": round(average_compliance_rate, 2),
                "criteria_count": len(criteria_analysis)
            }
        }
    
    def _build_summary_prompt(self, reports_data: List[Dict[str, Any]], criteria_analysis: Dict[str, Any]) -> str:
        """构建汇总分析提示词"""
        report_names = [data['report']['name'] for data in reports_data]
        
        prompt = f"""
请对以下 {len(reports_data)} 个可研报告的评审结果进行汇总分析：

报告列表：
{chr(10).join([f"- {name}" for name in report_names])}

按评审细则的汇总分析：
"""
        
        for criterion_id, criterion_data in criteria_analysis.items():
            stats = criterion_data["statistics"]
            prompt += f"""
{criterion_id}. {criterion_data['criterion_text']}
合规率：{stats['compliance_rate']}% ({stats['compliant_reports']}/{stats['total_reports']})

各报告评审结果：
"""
            for report in criterion_data["reports"]:
                prompt += f"  - {report['report_name']}: {report['result']}\n"
                if report['analysis']:
                    prompt += f"    分析：{report['analysis'][:100]}...\n"
        
        prompt += """

请从以下几个方面进行汇总分析：
1. 总体评审情况概述
2. 各审查细则的合规情况分析（重点关注合规率较低的细则）
3. 各报告的共性问题和差异化问题
4. 存在的主要问题和不足
5. 改进建议和意见
6. 总体结论

请提供详细、专业的分析报告。
"""
        
        return prompt
    
    def _call_model_for_summary(self, prompt: str) -> str:
        """调用大模型生成汇总分析"""
        import os
        import time
        
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return """[测试模式] 多报告汇总分析报告：

1. 总体评审情况概述
   本次共分析了多个可研报告，整体评审情况良好，大部分审查细则符合要求。

2. 各审查细则的合规情况分析
   - 大部分审查细则合规率在80%以上
   - 个别细则存在合规率较低的情况，需要重点关注
   - 技术方案相关细则合规率较高
   - 经济分析相关细则存在改进空间

3. 各报告的共性问题和差异化问题
   共性问题：
   - 部分报告在风险分析方面描述不够详细
   - 环境影响评价章节内容相对薄弱
   
   差异化问题：
   - 不同报告在技术方案深度上存在差异
   - 投资估算的详细程度不一

4. 存在的主要问题和不足
   - 部分报告的技术方案描述需要进一步完善
   - 个别报告的经济分析深度不够
   - 风险评估和应对措施需要加强

5. 改进建议和意见
   - 建议统一报告编制标准和要求
   - 加强技术方案的详细描述
   - 完善经济分析的深度和广度
   - 提高风险评估的全面性

6. 总体结论
   本批次可研报告整体质量良好，符合相关要求，建议在完善上述问题后通过评审。"""
        
        try:
            # 使用现有的模型服务方法
            from openai import OpenAI
            
            client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
            )
            
            # 记录API调用开始时间
            api_start_time = time.time()
            
            response = client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，负责对多个报告的评审结果进行汇总分析。请按照评审细则进行详细分析，提供专业的汇总报告。"},
                    {"role": "user", "content": prompt}
                ],
                timeout=180  # 多报告分析需要更长时间
            )
            
            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time
            
            # 更新统计信息
            if hasattr(self.model_service, 'stats'):
                self.model_service.stats["api_calls"] += 1
                self.model_service.stats["total_api_time"] += api_duration
                
                if hasattr(response, 'usage') and response.usage:
                    input_tokens = response.usage.prompt_tokens
                    output_tokens = response.usage.completion_tokens
                    total_tokens = response.usage.total_tokens
                    
                    self.model_service.stats["total_input_tokens"] += input_tokens
                    self.model_service.stats["total_output_tokens"] += output_tokens
                    self.model_service.stats["total_tokens"] += total_tokens
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"大模型API调用失败: {e}")
            return f"汇总分析生成失败: {str(e)}"
    
    def get_summary(self, summary_id: str) -> Optional[Dict[str, Any]]:
        """获取指定的汇总报告"""
        summaries = self._load_summaries()
        for summary in summaries:
            if summary['id'] == summary_id:
                return summary
        return None
    
    def get_all_summaries(self) -> List[Dict[str, Any]]:
        """获取所有汇总报告"""
        summaries = self._load_summaries()
        summaries.sort(key=lambda x: x['created_at'], reverse=True)
        return summaries
    
    def delete_summary(self, summary_id: str) -> bool:
        """删除汇总报告"""
        summaries = self._load_summaries()
        original_length = len(summaries)
        
        summaries = [s for s in summaries if s['id'] != summary_id]
        
        if len(summaries) < original_length:
            self._save_summaries(summaries)
            return True
        
        return False
