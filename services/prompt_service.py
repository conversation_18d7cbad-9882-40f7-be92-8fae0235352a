import os
import re
from typing import Dict, Any, Optional
from datetime import datetime


class PromptService:
    """提示词管理服务，负责加载和渲染提示词模板"""
    
    def __init__(self):
        self.template_cache = {}
        self.system_variables = self._get_system_variables()
    
    def _get_system_variables(self) -> Dict[str, Any]:
        """获取系统变量，用于模板变量替换"""
        return {
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "model_name": os.getenv("MODEL_NAME", "qwq-32b"),
            "test_mode": os.getenv("TEST_MODE", "false"),
            "version": "1.0.0"
        }
    
    def _load_template(self, template_path: str) -> str:
        """加载提示词模板文件"""
        if template_path in self.template_cache:
            return self.template_cache[template_path]
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
                self.template_cache[template_path] = template_content
                return template_content
        except FileNotFoundError:
            raise FileNotFoundError(f"提示词模板文件未找到: {template_path}")
        except Exception as e:
            raise Exception(f"加载提示词模板失败: {e}")
    
    def _render_template(self, template: str, variables: Dict[str, Any]) -> str:
        """渲染模板，替换变量"""
        # 合并系统变量和用户变量
        all_variables = {**self.system_variables, **variables}
        
        # 处理空值，避免显示None
        for key, value in all_variables.items():
            if value is None:
                all_variables[key] = "未提供"
            elif isinstance(value, str) and not value.strip():
                all_variables[key] = "未提供"
        
        # 使用format方法进行变量替换
        try:
            rendered = template.format(**all_variables)
            return rendered
        except KeyError as e:
            raise ValueError(f"模板变量缺失: {e}")
        except Exception as e:
            raise Exception(f"模板渲染失败: {e}")
    
    def get_section_analysis_prompt(self, 
                                  chapter_outline: str = None, 
                                  criteria_text: str = None, 
                                  review_guide: str = None,
                                  result_format: str = None) -> str:
        """获取章节分析提示词"""
        template_path = os.getenv("PROMPT_SECTION_ANALYSIS", "templates/prompts/section_analysis_prompt.txt")
        template = self._load_template(template_path)
        
        # 默认的结果格式
        if result_format is None:
            result_format = '''```json
{
  "summary": "100字以内总结该章节原文本内容",
  "criteria_results": [
    {
      "criterion_id": "审查细则编号",
      "result": "符合/基本符合/不符合/不适用",
      "explanation": "具体说明原因，如果不符合请详细说明问题"
    }
  ]
}
```'''
        
        variables = {
            "chapter_outline": chapter_outline or "未提供该章节的大纲信息",
            "criteria_text": criteria_text or "未提供审查细则",
            "review_guide": review_guide or "未提供审查指南",
            "result_format": result_format
        }
        
        return self._render_template(template, variables)
    
    def get_comprehensive_batch_prompt(self, outline: str = None) -> str:
        """获取批量综合分析提示词"""
        template_path = os.getenv("PROMPT_COMPREHENSIVE_BATCH", "templates/prompts/comprehensive_batch_prompt.txt")
        template = self._load_template(template_path)
        
        variables = {
            "outline": outline or "未提供编制大纲信息"
        }
        
        return self._render_template(template, variables)
    
    def get_summary_review_prompt(self, review_results: Any = None) -> str:
        """获取汇总评审提示词"""
        template_path = os.getenv("PROMPT_SUMMARY_REVIEW", "templates/prompts/summary_review_prompt.txt")
        template = self._load_template(template_path)
        
        variables = {
            "review_results": str(review_results) if review_results is not None else "未提供评审结果"
        }
        
        return self._render_template(template, variables)
    
    def clear_cache(self):
        """清空模板缓存"""
        self.template_cache.clear()
    
    def reload_system_variables(self):
        """重新加载系统变量"""
        self.system_variables = self._get_system_variables()
    
    def get_template_info(self) -> Dict[str, Any]:
        """获取模板信息"""
        return {
            "cached_templates": list(self.template_cache.keys()),
            "system_variables": self.system_variables,
            "template_paths": {
                "section_analysis": os.getenv("PROMPT_SECTION_ANALYSIS"),
                "comprehensive_batch": os.getenv("PROMPT_COMPREHENSIVE_BATCH"),
                "summary_review": os.getenv("PROMPT_SUMMARY_REVIEW")
            }
        }
