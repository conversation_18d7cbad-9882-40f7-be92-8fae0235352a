import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from services.topic_service import TopicService
from services.report_service import ReportService
from services.review_service import ReviewService
from services.model_service import ModelService

class TopicSummaryService:
    """专题汇总分析服务"""

    def __init__(self, model_service: ModelService):
        self.model_service = model_service
        self.topic_service = TopicService()
        self.report_service = ReportService()
        self.review_service = ReviewService()
        self.data_dir = "data"
        self.summaries_file = os.path.join(self.data_dir, "topic_summaries.json")
        self._ensure_data_dir()
        self._init_summaries_file()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def _init_summaries_file(self):
        """初始化汇总文件"""
        if not os.path.exists(self.summaries_file):
            self._save_summaries([])

    def _load_summaries(self) -> List[Dict[str, Any]]:
        """加载汇总数据"""
        try:
            with open(self.summaries_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def _save_summaries(self, summaries: List[Dict[str, Any]]):
        """保存汇总数据"""
        with open(self.summaries_file, 'w', encoding='utf-8') as f:
            json.dump(summaries, f, ensure_ascii=False, indent=2)

    def get_topic_status(self, topic_id: str) -> Dict[str, Any]:
        """获取专题状态信息"""
        topic = self.topic_service.get_topic(topic_id)
        if not topic:
            return {"error": "专题不存在"}

        # 获取专题下的所有报告
        reports = self.report_service.get_reports(topic_id=topic_id)

        # 获取已评审的报告
        reviewed_reports = []
        unreviewed_reports = []

        for report in reports:
            latest_review = self.review_service.get_latest_review_for_report(report['id'])
            if latest_review:
                reviewed_reports.append({
                    "report": report,
                    "review": latest_review
                })
            else:
                unreviewed_reports.append(report)

        return {
            "topic": topic,
            "total_reports": len(reports),
            "reviewed_count": len(reviewed_reports),
            "unreviewed_count": len(unreviewed_reports),
            "reviewed_reports": reviewed_reports,
            "unreviewed_reports": unreviewed_reports,
            "completion_rate": len(reviewed_reports) / len(reports) * 100 if reports else 0
        }

    def generate_topic_summary(self, topic_id: str, force_regenerate: bool = False) -> Dict[str, Any]:
        """生成专题汇总报告"""
        # 获取专题状态
        status = self.get_topic_status(topic_id)
        if "error" in status:
            return status

        # 检查是否已有汇总报告且不强制重新生成
        existing_summary = self.get_latest_summary(topic_id)
        if existing_summary and not force_regenerate:
            # 检查是否有新的评审记录
            last_summary_time = datetime.fromisoformat(existing_summary['created_at'])
            has_new_reviews = False

            for reviewed_report in status['reviewed_reports']:
                review_time = datetime.fromisoformat(reviewed_report['review']['created_at'])
                if review_time > last_summary_time:
                    has_new_reviews = True
                    break

            if not has_new_reviews:
                return existing_summary

        # 如果有未评审的报告，询问是否继续
        if status['unreviewed_count'] > 0 and not force_regenerate:
            return {
                "warning": "有未评审的报告",
                "unreviewed_reports": status['unreviewed_reports'],
                "message": f"专题中还有 {status['unreviewed_count']} 个报告未评审，是否继续生成汇总报告？"
            }

        # 生成汇总分析
        summary_result = self._analyze_topic_summary(status)

        # 保存汇总报告
        summary_record = {
            "id": str(uuid.uuid4()),
            "topic_id": topic_id,
            "summary_result": summary_result,
            "status": status,
            "created_at": datetime.now().isoformat()
        }

        summaries = self._load_summaries()
        summaries.append(summary_record)
        self._save_summaries(summaries)

        return summary_record

    def _analyze_topic_summary(self, status: Dict[str, Any]) -> Dict[str, Any]:
        """使用大模型分析专题汇总"""
        topic = status['topic']
        reviewed_reports = status['reviewed_reports']

        if not reviewed_reports:
            return {
                "summary": "该专题暂无已评审的报告",
                "criteria_analysis": {},
                "overall_statistics": {
                    "total_reports": status['total_reports'],
                    "reviewed_reports": 0,
                    "average_compliance_rate": 0
                }
            }

        # 收集所有评审结果
        all_criteria_results = {}
        total_compliance_rates = []

        for reviewed_report in reviewed_reports:
            report = reviewed_report['report']
            review = reviewed_report['review']
            review_results = review['result'].get('review_results', [])

            total_compliance_rates.append(
                review['result'].get('statistics', {}).get('compliance_rate', 0)
            )

            # 按审查细则分组
            for result in review_results:
                criterion_id = result.get('criterion_id', '')
                criterion_text = result.get('criterion_text', '')

                if criterion_id not in all_criteria_results:
                    all_criteria_results[criterion_id] = {
                        "criterion_text": criterion_text,
                        "results": []
                    }

                all_criteria_results[criterion_id]["results"].append({
                    "report_name": report['name'],
                    "result": result.get('result', ''),
                    "analysis": result.get('analysis', '')
                })

        # 使用大模型生成汇总分析
        summary_prompt = self._build_summary_prompt(topic, all_criteria_results, status)

        try:
            summary_analysis = self._call_model_for_summary(summary_prompt)
        except Exception as e:
            summary_analysis = f"汇总分析生成失败: {str(e)}"

        # 计算统计信息
        average_compliance_rate = sum(total_compliance_rates) / len(total_compliance_rates) if total_compliance_rates else 0

        return {
            "summary": summary_analysis,
            "criteria_analysis": all_criteria_results,
            "overall_statistics": {
                "total_reports": status['total_reports'],
                "reviewed_reports": len(reviewed_reports),
                "unreviewed_reports": status['unreviewed_count'],
                "average_compliance_rate": round(average_compliance_rate, 2),
                "completion_rate": round(status['completion_rate'], 2)
            }
        }

    def _build_summary_prompt(self, topic: Dict[str, Any], criteria_results: Dict[str, Any], status: Dict[str, Any]) -> str:
        """构建汇总分析提示词"""
        prompt = f"""
请对专题"{topic['name']}"的可研报告评审结果进行汇总分析。

专题信息：
- 专题名称：{topic['name']}
- 专题描述：{topic.get('description', '无')}
- 总报告数：{status['total_reports']}
- 已评审报告数：{status['reviewed_count']}
- 未评审报告数：{status['unreviewed_count']}

各审查细则的评审结果汇总：
"""

        for criterion_id, criterion_data in criteria_results.items():
            prompt += f"\n{criterion_id}. {criterion_data['criterion_text']}\n"

            for result in criterion_data['results']:
                prompt += f"  - {result['report_name']}: {result['result']}\n"
                if result['analysis']:
                    prompt += f"    分析：{result['analysis']}\n"

        prompt += """

请从以下几个方面进行汇总分析：
1. 总体评审情况概述
2. 各审查细则的合规情况分析
3. 存在的主要问题和不足
4. 改进建议和意见
5. 总体结论

请提供详细、专业的分析报告。
"""

        return prompt

    def _call_model_for_summary(self, prompt: str) -> str:
        """调用大模型生成汇总分析"""
        import os
        import time

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return """[测试模式] 专题汇总分析报告：

1. 总体评审情况概述
   本专题共包含多个可研报告，整体评审情况良好，大部分审查细则符合要求。

2. 各审查细则的合规情况分析
   - 大部分审查细则合规率较高
   - 个别细则存在不符合情况，需要重点关注

3. 存在的主要问题和不足
   - 部分报告在技术方案描述方面需要进一步完善
   - 个别报告的经济分析深度不够

4. 改进建议和意见
   - 建议加强技术方案的详细描述
   - 完善经济分析的深度和广度
   - 提高报告编制的规范性

5. 总体结论
   本专题的可研报告整体质量良好，符合相关要求，建议通过评审。"""

        try:
            # 使用现有的模型服务方法
            from openai import OpenAI

            client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
            )

            # 记录API调用开始时间
            api_start_time = time.time()

            response = client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，负责对多个报告的评审结果进行汇总分析。请提供详细、专业的分析报告。"},
                    {"role": "user", "content": prompt}
                ],
                timeout=120
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            if hasattr(self.model_service, 'stats'):
                self.model_service.stats["api_calls"] += 1
                self.model_service.stats["total_api_time"] += api_duration

                if hasattr(response, 'usage') and response.usage:
                    input_tokens = response.usage.prompt_tokens
                    output_tokens = response.usage.completion_tokens
                    total_tokens = response.usage.total_tokens

                    self.model_service.stats["total_input_tokens"] += input_tokens
                    self.model_service.stats["total_output_tokens"] += output_tokens
                    self.model_service.stats["total_tokens"] += total_tokens

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"大模型API调用失败: {e}")
            return f"汇总分析生成失败: {str(e)}"

    def get_latest_summary(self, topic_id: str) -> Optional[Dict[str, Any]]:
        """获取专题的最新汇总报告"""
        summaries = self._load_summaries()
        topic_summaries = [s for s in summaries if s['topic_id'] == topic_id]

        if not topic_summaries:
            return None

        # 按创建时间排序，返回最新的
        topic_summaries.sort(key=lambda x: x['created_at'], reverse=True)
        return topic_summaries[0]

    def get_all_summaries(self, topic_id: str = None) -> List[Dict[str, Any]]:
        """获取汇总报告列表"""
        summaries = self._load_summaries()

        if topic_id:
            summaries = [s for s in summaries if s['topic_id'] == topic_id]

        # 按创建时间排序
        summaries.sort(key=lambda x: x['created_at'], reverse=True)
        return summaries

    def delete_summary(self, summary_id: str) -> bool:
        """删除汇总报告"""
        summaries = self._load_summaries()
        original_length = len(summaries)

        summaries = [s for s in summaries if s['id'] != summary_id]

        if len(summaries) < original_length:
            self._save_summaries(summaries)
            return True

        return False
