from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi import Request
import uvicorn
from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.topic_service import TopicService
from services.report_service import ReportService
from services.review_service import ReviewService
from services.topic_summary_service import TopicSummaryService
from services.multi_report_summary_service import MultiReportSummaryService
from services.report_name_extractor import ReportNameExtractor
import os
import json
import asyncio
from dotenv import load_dotenv
from typing import Optional

load_dotenv()

app = FastAPI(title="可研报告评审助手")
# 设置模板目录
current_dir = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))
# 新增静态文件挂载
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "templates")), name="static")

# 初始化服务
model_service = ModelService()
document_parser = DocumentParser()
report_analyzer = ReportAnalyzer(model_service, document_parser)
topic_service = TopicService()
report_service = ReportService()
review_service = ReviewService()
topic_summary_service = TopicSummaryService(model_service)
multi_report_summary_service = MultiReportSummaryService(model_service)
report_name_extractor = ReportNameExtractor()

# 全局调试信息存储
debug_messages = []
debug_clients = []

def add_debug_message(message: str, level: str = "info"):
    """添加调试信息"""
    return
    import time
    debug_msg = {
        "timestamp": time.time(),
        "message": message,
        "level": level
    }
    debug_messages.append(debug_msg)

    # 只保留最近100条消息
    if len(debug_messages) > 100:
        debug_messages.pop(0)

    # 通知所有连接的客户端
    for client_queue in debug_clients:
        try:
            client_queue.put_nowait(debug_msg)
        except:
            pass


# ==================== 页面路由 ====================

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/topics", response_class=HTMLResponse)
async def topic_management(request: Request):
    return templates.TemplateResponse("topic_management.html", {"request": request})

@app.get("/reports", response_class=HTMLResponse)
async def report_management(request: Request):
    return templates.TemplateResponse("report_management.html", {"request": request})

@app.get("/reviews", response_class=HTMLResponse)
async def review_management(request: Request):
    return templates.TemplateResponse("review_management.html", {"request": request})

@app.get("/debug-stream")
async def debug_stream():
    """SSE端点，用于实时推送调试信息"""
    import queue
    import time

    client_queue = queue.Queue()
    debug_clients.append(client_queue)

    async def event_generator():
        try:
            # 发送历史消息
            for msg in debug_messages[-10:]:  # 只发送最近10条
                yield f"data: {json.dumps(msg)}\n\n"

            # 持续发送新消息
            while True:
                try:
                    # 等待新消息，超时1秒
                    msg = client_queue.get(timeout=1)
                    yield f"data: {json.dumps(msg)}\n\n"
                except queue.Empty:
                    # 发送心跳
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    print(f"SSE error: {e}")
                    break
        finally:
            # 清理客户端连接
            if client_queue in debug_clients:
                debug_clients.remove(client_queue)

    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/analyze")
async def analyze_report(pdf_file: UploadFile = File(...)):
    try:
        add_debug_message(f"收到文件: {pdf_file.filename}", "info")

        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        uploads_dir = os.path.join(current_dir, "uploads")

        # 保存上传的PDF文件
        pdf_path = os.path.join(uploads_dir, pdf_file.filename)
        os.makedirs(uploads_dir, exist_ok=True)
        with open(pdf_path, "wb") as f:
            content = await pdf_file.read()
            f.write(content)

        add_debug_message(f"文件已保存到: {pdf_path}", "success")

        # 分析报告
        add_debug_message("开始分析报告...", "info")
        result = report_analyzer.analyze(pdf_path, debug_callback=add_debug_message)
        add_debug_message("分析完成", "success")

        return JSONResponse(content=result)
    except Exception as e:
        error_msg = f"错误: {str(e)}"
        add_debug_message(error_msg, "error")
        print(error_msg)
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

# ==================== 专题管理API ====================

@app.get("/api/topics")
async def get_topics():
    """获取所有专题"""
    try:
        topics = topic_service.get_topics()
        return JSONResponse(content={"success": True, "data": topics})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.post("/api/topics")
async def create_topic(
    name: str = Form(...),
    description: str = Form(""),
    outline_file: str = Form(""),
    guide_file: str = Form(""),
    criteria_file: str = Form(""),
    start_date: str = Form(""),
    end_date: str = Form("")
):
    """创建新专题"""
    try:
        topic = topic_service.create_topic(
            name=name,
            description=description,
            outline_file=outline_file,
            guide_file=guide_file,
            criteria_file=criteria_file,
            start_date=start_date,
            end_date=end_date
        )
        return JSONResponse(content={"success": True, "data": topic})
    except ValueError as e:
        return JSONResponse(
            status_code=400,
            content={"success": False, "error": str(e)}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.put("/api/topics/{topic_id}")
async def update_topic(
    topic_id: str,
    name: str = Form(None),
    description: str = Form(None),
    outline_file: str = Form(None),
    guide_file: str = Form(None),
    criteria_file: str = Form(None),
    start_date: str = Form(None),
    end_date: str = Form(None)
):
    """更新专题"""
    try:
        update_data = {}
        if name is not None:
            update_data['name'] = name
        if description is not None:
            update_data['description'] = description
        if outline_file is not None:
            update_data['outline_file'] = outline_file
        if guide_file is not None:
            update_data['guide_file'] = guide_file
        if criteria_file is not None:
            update_data['criteria_file'] = criteria_file
        if start_date is not None:
            update_data['start_date'] = start_date
        if end_date is not None:
            update_data['end_date'] = end_date

        topic = topic_service.update_topic(topic_id, **update_data)
        if topic:
            return JSONResponse(content={"success": True, "data": topic})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "专题不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/topics/{topic_id}")
async def delete_topic(topic_id: str):
    """删除专题"""
    try:
        success = topic_service.delete_topic(topic_id)
        if success:
            return JSONResponse(content={"success": True, "message": "专题删除成功"})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "专题不存在"}
            )
    except ValueError as e:
        return JSONResponse(
            status_code=400,
            content={"success": False, "error": str(e)}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/topics/{topic_id}/outline")
async def get_topic_outline(topic_id: str):
    """获取专题编制大纲预览"""
    try:
        topic = topic_service.get_topic(topic_id)
        if not topic:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "专题不存在"}
            )

        outline_file = topic.get('outline_file', '')
        if not outline_file or not os.path.exists(outline_file):
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "编制大纲文件不存在"}
            )

        # 解析大纲内容
        outline = document_parser.parse_outline(outline_file)

        return JSONResponse(content={
            "success": True,
            "data": {
                "outline": outline,
                "file_path": outline_file
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/topics/{topic_id}/guide")
async def get_topic_guide(topic_id: str):
    """获取专题审查指南预览"""
    try:
        topic = topic_service.get_topic(topic_id)
        if not topic:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "专题不存在"}
            )

        guide_file = topic.get('guide_file', '')
        if not guide_file or not os.path.exists(guide_file):
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "审查指南文件不存在"}
            )

        # 解析审查指南内容
        guide_content = document_parser.parse_review_guide(guide_file)

        return JSONResponse(content={
            "success": True,
            "data": {
                "guide_content": guide_content,
                "file_path": guide_file
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/topics/{topic_id}/criteria")
async def get_topic_criteria(topic_id: str):
    """获取专题审查细则预览"""
    try:
        topic = topic_service.get_topic(topic_id)
        if not topic:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "专题不存在"}
            )

        criteria_file = topic.get('criteria_file', '')
        if not criteria_file or not os.path.exists(criteria_file):
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "审查细则文件不存在"}
            )

        # 解析审查细则内容
        criteria = document_parser.parse_review_criteria(criteria_file)

        return JSONResponse(content={
            "success": True,
            "data": {
                "criteria": criteria,
                "file_path": criteria_file
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 报告管理API ====================

@app.get("/api/reports")
async def get_reports(topic_id: Optional[str] = None):
    """获取报告列表"""
    try:
        reports = report_service.get_reports(topic_id=topic_id)

        # 为每个报告添加评审状态信息
        for report in reports:
            latest_review = review_service.get_latest_review_for_report(report['id'])
            if latest_review:
                report['review_status'] = 'reviewed'
                report['latest_review_id'] = latest_review['id']
                report['latest_review_date'] = latest_review['created_at']
                # 获取评审次数
                all_reviews = review_service.get_reviews(report_id=report['id'])
                report['review_count'] = len(all_reviews)
            else:
                report['review_status'] = 'not_reviewed'
                report['latest_review_id'] = None
                report['latest_review_date'] = None
                report['review_count'] = 0

        return JSONResponse(content={"success": True, "data": reports})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/reports/{report_id}/content")
async def get_report_content(report_id: str):
    """获取报告内容预览"""
    try:
        report = report_service.get_report(report_id)
        if not report:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "报告不存在"}
            )

        # 使用统一的报告内容提取方法
        pdf_path = report['file_path']
        report_data = document_parser.extract_report_content(pdf_path)

        # 构建预览数据结构
        preview_data = {
            "project_name": report_data.get('report_name', ''),
            "sections": report_data.get('sections', []),
            "report_info": {
                "name": report['name'],
                "short_name": report_data.get('short_name', ''),
                "report_date": report_data.get('report_date', ''),
                "size": report_data.get('size', 0)
            }
        }

        return JSONResponse(content={
            "success": True,
            "data": preview_data
        })

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.post("/api/reports")
async def upload_report(
    pdf_file: UploadFile = File(...),
    name: str = Form(...),
    topic_id: str = Form(""),
    description: str = Form("")
):
    """上传报告"""
    try:
        # 保存文件
        file_content = await pdf_file.read()
        file_path = report_service.save_uploaded_file(file_content, pdf_file.filename)

        # 创建报告记录
        report = report_service.create_report(
            name=name,
            file_path=file_path,
            topic_id=topic_id,
            description=description
        )

        return JSONResponse(content={"success": True, "data": report})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.put("/api/reports/{report_id}")
async def update_report(
    report_id: str,
    name: str = Form(None),
    topic_id: str = Form(None),
    description: str = Form(None)
):
    """更新报告信息"""
    try:
        update_data = {}
        if name is not None:
            update_data['name'] = name
        if topic_id is not None:
            update_data['topic_id'] = topic_id
        if description is not None:
            update_data['description'] = description

        report = report_service.update_report(report_id, **update_data)
        if report:
            return JSONResponse(content={"success": True, "data": report})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "报告不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/reports/{report_id}")
async def delete_report(report_id: str):
    """删除报告"""
    try:
        success = report_service.delete_report(report_id)
        if success:
            return JSONResponse(content={"success": True, "message": "报告删除成功"})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "报告不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 评审管理API ====================

@app.get("/api/reviews")
async def get_reviews(report_id: Optional[str] = None, topic_id: Optional[str] = None):
    """获取评审记录列表"""
    try:
        reviews = review_service.get_reviews(report_id=report_id, topic_id=topic_id)
        return JSONResponse(content={"success": True, "data": reviews})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.post("/api/reviews/{report_id}")
async def create_review(report_id: str):
    """对指定报告进行评审"""
    try:
        # 获取报告信息
        report = report_service.get_report(report_id)
        if not report:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "报告不存在"}
            )

        # 获取专题配置
        topic_id = report.get('topic_id', '')
        if topic_id:
            topic = topic_service.get_topic(topic_id)
            if topic:
                # 使用专题的配置文件
                topic_files = topic_service.get_topic_files(topic_id)
                # 临时更新环境变量以使用专题配置
                original_outline = os.getenv('OUTLINE_FILE')
                original_guide = os.getenv('GUIDE_FILE')
                original_rules = os.getenv('RULES_FILE')

                if topic_files.get('outline_file'):
                    os.environ['OUTLINE_FILE'] = topic_files['outline_file']
                if topic_files.get('guide_file'):
                    os.environ['GUIDE_FILE'] = topic_files['guide_file']
                if topic_files.get('criteria_file'):
                    os.environ['RULES_FILE'] = topic_files['criteria_file']

                # 重新初始化分析器以使用新配置
                analyzer_to_use = ReportAnalyzer(model_service, document_parser)
            else:
                analyzer_to_use = report_analyzer
        else:
            analyzer_to_use = report_analyzer

        add_debug_message(f"开始评审报告: {report['name']}", "info")

        # 分析报告
        result = analyzer_to_use.analyze(report['file_path'], debug_callback=add_debug_message)

        # 保存评审记录
        review = review_service.create_review(
            report_id=report_id,
            topic_id=topic_id,
            result=result
        )

        add_debug_message("评审完成并已保存", "success")

        # 恢复原始环境变量
        if topic_id and topic:
            if original_outline:
                os.environ['OUTLINE_FILE'] = original_outline
            if original_guide:
                os.environ['GUIDE_FILE'] = original_guide
            if original_rules:
                os.environ['RULES_FILE'] = original_rules

        return JSONResponse(content={"success": True, "data": {"review": review, "result": result}})

    except Exception as e:
        error_msg = f"评审失败: {str(e)}"
        add_debug_message(error_msg, "error")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/reviews/{review_id}")
async def get_review(review_id: str):
    """获取评审记录详情"""
    try:
        review = review_service.get_review(review_id)
        if review:
            return JSONResponse(content={"success": True, "data": review})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "评审记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/reviews/{review_id}")
async def delete_review(review_id: str):
    """删除评审记录"""
    try:
        success = review_service.delete_review(review_id)
        if success:
            return JSONResponse(content={"success": True, "message": "评审记录删除成功"})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "评审记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 专题汇总API ====================

@app.get("/api/topics/{topic_id}/status")
async def get_topic_status(topic_id: str):
    """获取专题状态信息"""
    try:
        status = topic_summary_service.get_topic_status(topic_id)
        return JSONResponse(content={"success": True, "data": status})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.post("/api/topics/{topic_id}/summary")
async def generate_topic_summary(topic_id: str, force_regenerate: bool = True):
    """生成专题汇总报告"""
    try:
        summary = topic_summary_service.generate_topic_summary(topic_id, force_regenerate)
        return JSONResponse(content={"success": True, "data": summary})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/topics/{topic_id}/summary")
async def get_topic_summary(topic_id: str):
    """获取专题的最新汇总报告"""
    try:
        summary = topic_summary_service.get_latest_summary(topic_id)
        if summary:
            return JSONResponse(content={"success": True, "data": summary})
        else:
            return JSONResponse(content={"success": True, "data": None, "message": "暂无汇总报告"})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/summaries")
async def get_all_summaries(topic_id: Optional[str] = None):
    """获取汇总报告列表"""
    try:
        summaries = topic_summary_service.get_all_summaries(topic_id)
        return JSONResponse(content={"success": True, "data": summaries})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/summaries/{summary_id}")
async def delete_summary(summary_id: str):
    """删除汇总报告"""
    try:
        success = topic_summary_service.delete_summary(summary_id)
        if success:
            return JSONResponse(content={"success": True, "message": "汇总报告删除成功"})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "汇总报告不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 多报告汇总分析API ====================

@app.post("/api/multi-report-summary")
async def create_multi_report_summary(request: Request):
    """生成多报告汇总分析"""
    try:
        body = await request.json()
        report_ids = body.get('report_ids', [])

        if not report_ids:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "未提供报告ID列表"}
            )

        add_debug_message(f"开始多报告汇总分析，报告数量: {len(report_ids)}", "info")

        # 生成汇总分析
        summary = multi_report_summary_service.generate_multi_report_summary(report_ids)

        add_debug_message("多报告汇总分析完成", "success")

        return JSONResponse(content={"success": True, "data": summary})

    except Exception as e:
        error_msg = f"多报告汇总分析失败: {str(e)}"
        add_debug_message(error_msg, "error")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/multi-report-summaries")
async def get_multi_report_summaries():
    """获取所有多报告汇总分析记录"""
    try:
        summaries = multi_report_summary_service.get_all_summaries()
        return JSONResponse(content={"success": True, "data": summaries})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/api/multi-report-summaries/{summary_id}")
async def get_multi_report_summary(summary_id: str):
    """获取指定的多报告汇总分析记录"""
    try:
        summary = multi_report_summary_service.get_summary(summary_id)
        if summary:
            return JSONResponse(content={"success": True, "data": summary})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "汇总分析记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/multi-report-summaries/{summary_id}")
async def delete_multi_report_summary(summary_id: str):
    """删除多报告汇总分析记录"""
    try:
        success = multi_report_summary_service.delete_summary(summary_id)
        if success:
            return JSONResponse(content={"success": True, "message": "汇总分析记录删除成功"})
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "汇总分析记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 工具API ====================

@app.post("/api/extract-report-name")
async def extract_report_short_name(report_name: str = Form(...)):
    """提取报告简称"""
    try:
        short_name = report_name_extractor.extract_short_name(report_name)
        return JSONResponse(content={"success": True, "data": {"short_name": short_name}})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 缓存管理API ====================

@app.get("/api/cache/status")
async def get_cache_status():
    """获取缓存状态"""
    try:
        from services.document_parser import DocumentParser
        parser = DocumentParser()

        # 统计缓存文件
        reports_cache_dir = parser.parsed_reports_dir
        templates_cache_dir = parser.parsed_templates_dir

        reports_cache_count = 0
        templates_cache_count = 0

        if os.path.exists(reports_cache_dir):
            reports_cache_count = len([f for f in os.listdir(reports_cache_dir) if f.endswith('.json')])

        if os.path.exists(templates_cache_dir):
            templates_cache_count = len([f for f in os.listdir(templates_cache_dir) if f.endswith('.json')])

        return JSONResponse(content={
            "success": True,
            "cache_status": {
                "reports_cache_count": reports_cache_count,
                "templates_cache_count": templates_cache_count,
                "reports_cache_dir": reports_cache_dir,
                "templates_cache_dir": templates_cache_dir
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.delete("/api/cache/clear")
async def clear_cache():
    """清理所有缓存"""
    try:
        from services.document_parser import DocumentParser
        parser = DocumentParser()

        cleared_count = 0

        # 清理报告缓存
        if os.path.exists(parser.parsed_reports_dir):
            for filename in os.listdir(parser.parsed_reports_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(parser.parsed_reports_dir, filename)
                    os.remove(file_path)
                    cleared_count += 1

        # 清理模板缓存
        if os.path.exists(parser.parsed_templates_dir):
            for filename in os.listdir(parser.parsed_templates_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(parser.parsed_templates_dir, filename)
                    os.remove(file_path)
                    cleared_count += 1

        return JSONResponse(content={
            "success": True,
            "message": f"已清理 {cleared_count} 个缓存文件"
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

if __name__ == "__main__":
    import sys
    port = 8000
    if len(sys.argv) > 2 and sys.argv[1] == "--port":
        port = int(sys.argv[2])
    uvicorn.run(app, host="0.0.0.0", port=port)