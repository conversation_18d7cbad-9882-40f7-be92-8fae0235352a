From: <Saved by Blink>
Snapshot-Content-Location: http://127.0.0.1:8000/
Subject: =?utf-8?Q?=E5=8F=AF=E7=A0=94=E6=8A=A5=E5=91=8A=E8=AF=84=E5=AE=A1=E5=8A=A9?=
 =?utf-8?Q?=E6=89=8B?=
Date: Fri, 30 May 2025 11:58:36 +0800
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1----"


------MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: http://127.0.0.1:8000/

<!DOCTYPE html><html lang=3D"zh"><head><meta http-equiv=3D"Content-Type" co=
ntent=3D"text/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/=
css" href=3D"cid:<EMAIL>" />
   =20
    <meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=
=3D1.0">
    <title>=E5=8F=AF=E7=A0=94=E6=8A=A5=E5=91=8A=E8=AF=84=E5=AE=A1=E5=8A=A9=
=E6=89=8B</title>
    <!-- =E6=9B=BF=E6=8D=A2=E4=B8=BA=E6=9C=AC=E5=9C=B0CSS=E8=B7=AF=E5=BE=84=
 -->
    <link href=3D"http://127.0.0.1:8000/static/bootstrap.min.css" rel=3D"st=
ylesheet">
    <link href=3D"http://127.0.0.1:8000/static/all.min.css" rel=3D"styleshe=
et">
   =20
</head>
<body>
    <div class=3D"container mt-5">
        <h1 class=3D"text-center mb-4">=E5=8F=AF=E7=A0=94=E6=8A=A5=E5=91=8A=
=E8=AF=84=E5=AE=A1=E5=8A=A9=E6=89=8B</h1>

        <div class=3D"row justify-content-center">
            <div class=3D"col-md-8">
                <div class=3D"card">
                    <div class=3D"card-body">
                        <form id=3D"uploadForm">
                            <div class=3D"mb-3">
                                <label for=3D"pdfFile" class=3D"form-label"=
>=E9=80=89=E6=8B=A9=E5=8F=AF=E7=A0=94=E6=8A=A5=E5=91=8APDF=E6=96=87=E4=BB=
=B6</label>
                                <input type=3D"file" class=3D"form-control"=
 id=3D"pdfFile" accept=3D".pdf" required=3D"">
                            </div>
                            <button type=3D"submit" class=3D"btn btn-primar=
y">=E5=BC=80=E5=A7=8B=E8=AF=84=E5=AE=A1</button>
                        </form>
                    </div>
                </div>

                <div class=3D"loading" style=3D"display: none;">
                    <div class=3D"spinner-border text-primary" role=3D"stat=
us">
                        <span class=3D"visually-hidden">=E5=8A=A0=E8=BD=BD=
=E4=B8=AD...</span>
                    </div>
                    <p class=3D"mt-2">=E6=AD=A3=E5=9C=A8=E5=88=86=E6=9E=90=
=E6=8A=A5=E5=91=8A=EF=BC=8C=E8=AF=B7=E7=A8=8D=E5=80=99...</p>
                </div>

                <!-- =E8=B0=83=E8=AF=95=E9=9D=A2=E6=9D=BF -->
                <div id=3D"debugPanel" class=3D"debug-panel" style=3D"displ=
ay: none;">
                    <div class=3D"d-flex justify-content-between align-item=
s-center mb-3">
                        <h6 class=3D"mb-0"><i class=3D"fas fa-bug"></i> =E8=
=AF=84=E5=AE=A1=E8=BF=87=E7=A8=8B=E8=B0=83=E8=AF=95=E4=BF=A1=E6=81=AF <span=
 id=3D"debugStatus" class=3D"badge bg-secondary">=E7=A6=BB=E7=BA=BF</span><=
/h6>
                        <div>
                            <button type=3D"button" class=3D"btn btn-sm btn=
-outline-primary">
                                <i class=3D"fas fa-trash"></i> =E6=B8=85=E7=
=A9=BA
                            </button>
                            <button type=3D"button" class=3D"btn btn-sm btn=
-outline-secondary">
                                <i class=3D"fas fa-times"></i> =E5=85=B3=E9=
=97=AD
                            </button>
                        </div>
                    </div>
                    <div id=3D"debugContent" class=3D"debug-log"><div class=
=3D"progress-item">
                <span class=3D"timestamp">[11:30:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E5=BC=80=E5=
=A7=8B=E5=88=86=E6=9E=90=E6=96=87=E4=BB=B6: 1.=E5=B9=BF=E8=A5=BF=E7=94=B5=
=E7=BD=91=E6=9C=89=E9=99=90=E8=B4=A3=E4=BB=BB=E5=85=AC=E5=8F=B82025=E5=B9=
=B4=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=B7=A9=E5=9B=BA=E6=8F=90=E5=8D=87=
=E5=B7=A5=E7=A8=8B=E4=B8=AD=E5=A4=AE=E9=A2=84=E7=AE=97=E5=86=85=E6=8A=95=E8=
=B5=84=E8=AE=A1=E5=88=92=E9=9C=80=E6=B1=82=E9=A1=B9=E7=9B=AE=EF=BC=88=E6=A8=
=AA=E5=B7=9E=E5=B8=82=EF=BC=89=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=B6=
=E6=8A=A5=E5=91=8A.pdf
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:30:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E6=AD=A3=E5=
=9C=A8=E4=B8=8A=E4=BC=A0=E6=96=87=E4=BB=B6...
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:30:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E6=AD=A3=E5=
=9C=A8=E8=B0=83=E7=94=A8=E5=90=8E=E7=AB=AF=E5=88=86=E6=9E=90=E6=8E=A5=E5=8F=
=A3...
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-check-circle text-success"></i> =E6=8E=
=A5=E6=94=B6=E5=88=B0=E5=88=86=E6=9E=90=E7=BB=93=E6=9E=9C=EF=BC=8C=E6=AD=A3=
=E5=9C=A8=E8=A7=A3=E6=9E=90...
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-check-circle text-success"></i> =E8=A7=
=A3=E6=9E=90=E5=AE=8C=E6=88=90=EF=BC=8C=E5=85=B1=E8=8E=B7=E5=BE=97 21 =E4=
=B8=AA=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=99=E7=9A=84=E8=AF=84=E5=AE=A1=E7=BB=
=93=E6=9E=9C
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E7=BB=9F=E8=
=AE=A1=E4=BF=A1=E6=81=AF: =E7=AC=A6=E5=90=88 15 =E9=A1=B9, =E4=B8=8D=E7=AC=
=A6=E5=90=88 6 =E9=A1=B9
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E8=AE=A1=E6=
=97=B6=E7=BB=9F=E8=AE=A1: =E6=89=A7=E8=A1=8C=E6=97=B6=E9=97=B4 240.21=E7=A7=
=92, =E6=80=BBToken 119434
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-check-circle text-success"></i> =E8=AF=
=84=E5=AE=A1=E7=BB=93=E6=9E=9C=E6=98=BE=E7=A4=BA=E5=AE=8C=E6=88=90
            </div><div class=3D"progress-item">
                <span class=3D"timestamp">[11:34:44]</span>
                <i class=3D"fas fa-info-circle text-info"></i> =E8=AF=84=E5=
=AE=A1=E6=B5=81=E7=A8=8B=E7=BB=93=E6=9D=9F
            </div></div>
                </div>

                <div id=3D"resultSection" class=3D"result-table" style=3D"d=
isplay: block;">
                    <h3>=E8=AF=84=E5=AE=A1=E7=BB=93=E6=9E=9C</h3>

                    <!-- =E8=AF=84=E5=AE=A1=E6=9C=8D=E5=8A=A1=E8=AE=A1=E6=
=97=B6=E7=BB=9F=E8=AE=A1=E5=8C=BA -->
                    <div id=3D"timingStatsCard" class=3D"statistics-card">
                        <h5><i class=3D"fas fa-clock"></i> =E8=AF=84=E5=AE=
=A1=E6=9C=8D=E5=8A=A1=E7=BB=9F=E8=AE=A1</h5>
                        <div class=3D"row">
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E6=89=A7=E8=A1=8C=E6=97=B6=E9=97=
=B4</h6>
                                    <span id=3D"executionTime" class=3D"bad=
ge bg-primary fs-6">240.21=E7=A7=92</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E8=BE=93=E5=85=A5Token</h6>
                                    <span id=3D"inputTokens" class=3D"badge=
 bg-info fs-6">92455</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E8=BE=93=E5=87=BAToken</h6>
                                    <span id=3D"outputTokens" class=3D"badg=
e bg-warning fs-6">26979</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E6=80=BBToken</h6>
                                    <span id=3D"totalTokens" class=3D"badge=
 bg-secondary fs-6">119434</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- =E7=BB=9F=E8=AE=A1=E4=BF=A1=E6=81=AF -->
                    <div id=3D"statisticsCard" class=3D"statistics-card">
                        <h5>=E8=AF=84=E5=AE=A1=E7=BB=9F=E8=AE=A1</h5>
                        <div class=3D"row">
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E6=80=BB=E5=AE=A1=E6=9F=A5=E7=BB=
=86=E5=88=99</h6>
                                    <span id=3D"totalCriteria" class=3D"bad=
ge bg-primary fs-6">21</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E7=AC=A6=E5=90=88</h6>
                                    <span id=3D"compliantCount" class=3D"ba=
dge bg-success fs-6">15</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E4=B8=8D=E7=AC=A6=E5=90=88</h6>
                                    <span id=3D"nonCompliantCount" class=3D=
"badge bg-danger fs-6">6</span>
                                </div>
                            </div>
                            <div class=3D"col-md-3">
                                <div class=3D"text-center">
                                    <h6>=E5=90=88=E8=A7=84=E7=8E=87</h6>
                                    <span id=3D"complianceRate" class=3D"ba=
dge bg-info fs-6">71.43%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- =E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=99=E5=88=97=E8=
=A1=A8 -->
                    <div id=3D"criteriaList"><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 1.1</h6>
                                        <p class=3D"mb-0 text-muted">35=E5=
=8D=83=E4=BC=8F=E5=8F=8A=E4=BB=A5=E4=B8=8A=E7=94=B5=E5=8E=8B=E7=AD=89=E7=BA=
=A7=E9=A1=B9=E7=9B=AE=E4=BB=A5=E5=8D=95=E4=B8=AA=E9=A1=B9=E7=9B=AE=E3=80=81=
35=E5=8D=83=E4=BC=8F=E4=BB=A5=E4=B8=8B=E7=94=B5=E5=8E=8B=E7=AD=89=E7=BA=A7=
=E9=A1=B9=E7=9B=AE=E4=BB=A5=E5=8E=BF=E5=9F=9F=E4=B8=BA=E5=8D=95=E4=BD=8D=E7=
=BC=96=E5=88=B6=E9=A1=B9=E7=9B=AE=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=
=B6=E6=8A=A5=E5=91=8A=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=
=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E4=
=B8=BA10kV=E5=8F=8A=E4=BB=A5=E4=B8=8B=E7=94=B5=E5=8E=8B=E7=AD=89=E7=BA=A7=
=EF=BC=8C=E6=8C=89=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=EF=BC=8C=E4=BD=86=E9=
=83=A8=E5=88=86=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=98=8E=E7=A1=AE=E8=AF=B4=E6=98=
=8E=E4=BE=9D=E6=8D=AE=E3=80=82=E7=AB=A0=E8=8A=825=E7=AC=A6=E5=90=88=EF=BC=
=8C=E7=AB=A0=E8=8A=821=E3=80=817=E3=80=8111=E5=AD=98=E5=9C=A8=E4=B8=8D=E7=
=AC=A6=E5=90=88=E9=A1=B9=EF=BC=8C=E9=9C=80=E5=AE=8C=E5=96=84=E5=8E=BF=E5=9F=
=9F=E7=BC=96=E5=88=B6=E4=BE=9D=E6=8D=AE=E8=AF=B4=E6=98=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E7=AB=A0=E8=8A=825=E6=98=8E=E7=A1=AE=
=E6=8C=89=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=EF=BC=8C=E7=AC=A6=E5=90=88=E8=
=A6=81=E6=B1=82</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E7=AB=A0=E8=8A=821=E6=9C=AA=E8=AF=B4=
=E6=98=8E=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=E4=BE=9D=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E7=AB=A0=E8=8A=827=E6=9C=AA=E6=98=8E=
=E7=A1=AE=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=A6=82=E8=BF=B0=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=E7=9A=84=E4=
=BE=9D=E6=8D=AE=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=8A=95=E8=9E=8D=E8=B5=84=
=E7=AB=A0=E8=8A=82=E5=AE=8C=E5=96=84=E5=8E=BF=E5=9F=9F=E7=BC=96=E5=88=B6=E7=
=9A=84=E4=B8=93=E9=A1=B9=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-1-1" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (3=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-1-1" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=
=E6=9C=AC=E7=AC=A6=E5=90=88</span>
                                                            <strong>1 =E6=
=A6=82=E8=BF=B0</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E4=B8=BA10kV=E5=8F=8A=E4=BB=A5=E4=B8=8B=
=E7=94=B5=E5=8E=8B=E7=AD=89=E7=BA=A7=EF=BC=8C=E6=8C=89=E5=8E=BF=E5=9F=9F=E7=
=BC=96=E5=88=B6=EF=BC=8C=E4=BD=86=E6=9C=AA=E5=9C=A8=E7=AB=A0=E8=8A=82=E4=B8=
=AD=E6=98=8E=E7=A1=AE=E8=AF=B4=E6=98=8E=E4=BB=A5=E5=8E=BF=E5=9F=9F=E4=B8=BA=
=E5=8D=95=E4=BD=8D=E7=BC=96=E5=88=B6=E7=9A=84=E4=BE=9D=E6=8D=AE=E3=80=82</s=
mall>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E4=B8=BA35=E5=8D=83=E4=BC=8F=E4=BB=A5=
=E4=B8=8B=E7=94=B5=E5=8E=8B=E7=AD=89=E7=BA=A7=EF=BC=8C=E4=BB=A5=E5=8E=BF=E5=
=9F=9F=EF=BC=88=E6=A8=AA=E5=B7=9E=E5=B8=82=EF=BC=89=E4=B8=BA=E5=8D=95=E4=BD=
=8D=E7=BC=96=E5=88=B6=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=B6=E6=8A=A5=
=E5=91=8A=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=991.1=
=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>7 =E9=
=A1=B9=E7=9B=AE=E6=8A=95=E8=9E=8D=E8=B5=84=E4=B8=8E=E8=B4=A2=E5=8A=A1=E6=96=
=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=98=8E=E7=A1=AE=E8=AF=B4=E6=98=8E=E9=A1=B9=E7=
=9B=AE=E6=98=AF=E5=90=A6=E6=8C=8935=E5=8D=83=E4=BC=8F=E4=BB=A5=E4=B8=8B=E7=
=94=B5=E5=8E=8B=E7=AD=89=E7=BA=A7=E4=BB=A5=E5=8E=BF=E5=9F=9F=E4=B8=BA=E5=8D=
=95=E4=BD=8D=E7=BC=96=E5=88=B6=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=B6=
=E6=8A=A5=E5=91=8A=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 1.2</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=B6=E6=8A=A5=E5=91=8A=
=E5=BA=94=E7=94=B1=E5=85=B7=E5=A4=87=E7=9B=B8=E5=BA=94=E8=83=BD=E5=8A=9B=E7=
=9A=84=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=E5=8D=95=E4=BD=8D=E7=BC=96=E5=88=
=B6=EF=BC=8C=E5=B9=B6=E5=8A=A0=E7=9B=96=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=
=E5=8D=95=E4=BD=8D=E5=85=AC=E7=AB=A0=E5=92=8C=E5=92=A8=E8=AF=A2=E5=B7=A5=E7=
=A8=8B=E5=B8=88(=E6=8A=95=E8=B5=84)=E6=89=A7=E4=B8=9A=E4=B8=93=E7=94=A8=E7=
=AB=A0=EF=BC=8C=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=E5=8D=95=E4=BD=8D=E5=BA=
=94=E9=80=9A=E8=BF=87=E5=85=A8=E5=9B=BD=E6=8A=95=E8=B5=84=E9=A1=B9=E7=9B=AE=
=E5=9C=A8=E7=BA=BF=E5=AE=A1=E6=89=B9=E7=9B=91=E7=AE=A1=E5=B9=B3=E5=8F=B0=E5=
=A4=87=E6=A1=88=E5=9F=BA=E6=9C=AC=E4=BF=A1=E6=81=AF=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E8=B5=84=E8=B4=A8=E8=
=AF=81=E4=B9=A6=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=EF=BC=8C=E4=BD=86=E6=A6=
=82=E8=BF=B0=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E5=8F=8A=E7=BC=96=E5=88=B6=
=E5=8D=95=E4=BD=8D=E8=B5=84=E8=B4=A8=E4=BF=A1=E6=81=AF=EF=BC=8C=E9=9C=80=E5=
=8A=A0=E5=BC=BA=E8=B5=84=E6=96=99=E5=AE=8C=E6=95=B4=E6=80=A7=E5=AE=A1=E6=9F=
=A5=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E8=B5=84=E8=B4=A8=E8=AF=81=E4=B9=A6=
=E7=AB=A0=E8=8A=82=E6=8F=90=E4=BE=9B=E5=AE=8C=E6=95=B4=E8=B5=84=E8=B4=A8=E4=
=BF=A1=E6=81=AF</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=A6=82=E8=BF=B0=E7=AB=A0=E8=8A=82=
=E7=BC=BA=E5=A4=B1=E7=BC=96=E5=88=B6=E5=8D=95=E4=BD=8D=E8=B5=84=E8=B4=A8=E8=
=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E5=8F=8A=E5=85=A8=
=E5=9B=BD=E6=8A=95=E8=B5=84=E9=A1=B9=E7=9B=AE=E5=9C=A8=E7=BA=BF=E5=AE=A1=E6=
=89=B9=E7=9B=91=E7=AE=A1=E5=B9=B3=E5=8F=B0=E5=A4=87=E6=A1=88=E4=BF=A1=E6=81=
=AF</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=A6=82=E8=BF=B0=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=E5=8D=95=E4=
=BD=8D=E8=B5=84=E8=B4=A8=E4=BF=A1=E6=81=AF</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E5=A4=87=E6=A1=88=
=E4=BF=A1=E6=81=AF=E8=AF=B4=E6=98=8E=E5=B9=B6=E5=8A=A0=E7=9B=96=E5=85=AC=E7=
=AB=A0</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-1-2" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (2=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-1-2" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>1 =E6=
=A6=82=E8=BF=B0</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=8F=90=E5=8F=8A=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=
=AF=A2=E5=8D=95=E4=BD=8D=E7=9A=84=E7=BC=96=E5=88=B6=E8=B5=84=E8=B4=A8=E3=80=
=81=E5=85=AC=E7=AB=A0=E5=8F=8A=E6=89=A7=E4=B8=9A=E4=B8=93=E7=94=A8=E7=AB=A0=
=E4=BF=A1=E6=81=AF=EF=BC=8C=E6=9C=AA=E8=AF=B4=E6=98=8E=E6=98=AF=E5=90=A6=E9=
=80=9A=E8=BF=87=E5=85=A8=E5=9B=BD=E6=8A=95=E8=B5=84=E9=A1=B9=E7=9B=AE=E5=9C=
=A8=E7=BA=BF=E5=AE=A1=E6=89=B9=E7=9B=91=E7=AE=A1=E5=B9=B3=E5=8F=B0=E5=A4=87=
=E6=A1=88=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>=E8=B5=
=84=E8=B4=A8=E8=AF=81=E4=B9=A6</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E5=86=85=E5=AE=B9=E5=8C=85=E5=90=AB=E5=
=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=E5=8D=95=E4=BD=8D=E5=B9=BF=E8=A5=BF=E7=BB=
=BF=E8=83=BD=E7=94=B5=E5=8A=9B=E5=8B=98=E5=AF=9F=E8=AE=BE=E8=AE=A1=E6=9C=89=
=E9=99=90=E5=85=AC=E5=8F=B8=E7=9A=84=E7=94=B2=E7=BA=A7=E8=B5=84=E4=BF=A1=E8=
=AF=81=E4=B9=A6=E3=80=81=E5=92=A8=E8=AF=A2=E5=B7=A5=E7=A8=8B=E5=B8=88=E7=A8=
=8B=E6=98=A5=E6=99=96=E7=9A=84=E7=99=BB=E8=AE=B0=E8=AF=81=E4=B9=A6=EF=BC=8C=
=E7=AC=A6=E5=90=88'=E7=94=B1=E5=85=B7=E5=A4=87=E7=9B=B8=E5=BA=94=E8=83=BD=
=E5=8A=9B=E7=9A=84=E5=B7=A5=E7=A8=8B=E5=92=A8=E8=AF=A2=E5=8D=95=E4=BD=8D=E7=
=BC=96=E5=88=B6=E5=B9=B6=E5=8A=A0=E7=9B=96=E5=85=AC=E7=AB=A0=E5=8F=8A=E6=89=
=A7=E4=B8=9A=E4=B8=93=E7=94=A8=E7=AB=A0'=E7=9A=84=E8=A6=81=E6=B1=82=E3=80=
=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 1.3</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E5=8F=AF=E8=A1=8C=E6=80=A7=E7=A0=94=E7=A9=B6=E6=8A=A5=E5=91=8A=
=E6=8C=89=E7=85=A7=E5=9B=BD=E5=AE=B6=E8=83=BD=E6=BA=90=E5=B1=80=E5=85=B3=E4=
=BA=8E=E5=8D=B0=E5=8F=91=E3=80=8A=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=B7=
=A9=E5=9B=BA=E6=8F=90=E5=8D=87=E5=B7=A5=E7=A8=8B=E4=B8=AD=E5=A4=AE=E9=A2=84=
=E7=AE=97=E5=86=85=E6=8A=95=E8=B5=84=E9=A1=B9=E7=9B=AE=E5=8F=AF=E8=A1=8C=E6=
=80=A7=E7=A0=94=E7=A9=B6=E6=8A=A5=E5=91=8A=E7=BC=96=E5=88=B6=E5=92=8C=E5=AE=
=A1=E6=9F=A5=E6=8C=87=E5=8D=97=E3=80=8B=E7=9A=84=E9=80=9A=E7=9F=A5-=E5=9B=
=BD=E8=83=BD=E5=8F=91=E6=96=B0=E8=83=BD=E8=A7=84=E3=80=942023=E3=80=9578=E5=
=8F=B7=E6=96=87=E7=9A=84=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=E7=BC=96=E5=88=
=B6=EF=BC=8C=E4=B8=8D=E5=BE=97=E9=9A=8F=E6=84=8F=E6=94=B9=E5=8A=A8=E6=88=96=
=E8=80=85=E7=AB=A0=E8=8A=82=E4=B8=8D=E5=85=A8=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=
=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E5=A4=A7=E9=83=A8=E5=
=88=86=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E7=BC=96=E5=88=B6=E5=A4=A7=E7=BA=
=B2=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E6=8A=95=E8=9E=8D=E8=B5=84=E7=AB=A0=
=E8=8A=82=E7=BC=BA=E5=A4=B1=E5=80=BA=E5=8A=A1=E6=B8=85=E5=81=BF=E5=88=86=E6=
=9E=90=EF=BC=8C=E9=99=84=E8=A1=A8=E5=86=85=E5=AE=B9=E4=B8=8D=E5=AE=8C=E6=95=
=B4=EF=BC=8C=E9=9C=80=E5=AE=8C=E5=96=84=E7=AB=A0=E8=8A=82=E5=AE=8C=E6=95=B4=
=E6=80=A7=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=
=E6=95=B4=E4=BD=93=E7=AC=A6=E5=90=88=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=8A=95=E8=9E=8D=E8=B5=84=E7=AB=A0=
=E8=8A=82=E7=BC=BA=E5=B0=917.4=E5=80=BA=E5=8A=A1=E6=B8=85=E5=81=BF=E5=88=86=
=E6=9E=90</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=99=84=E8=A1=A8=E6=9C=AA=E5=AE=8C=
=E6=95=B4=E5=91=88=E7=8E=B0=E8=AE=BE=E5=A4=87=E6=9D=90=E6=96=99=E6=B8=85=E5=
=86=8C=E7=AD=89=E6=A0=B8=E5=BF=83=E5=86=85=E5=AE=B9</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E8=A1=A5=E5=85=857.4=E5=80=BA=E5=8A=
=A1=E6=B8=85=E5=81=BF=E8=83=BD=E5=8A=9B=E5=88=86=E6=9E=90=E5=86=85=E5=AE=B9=
</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=AE=8C=E5=96=84=E9=99=84=E8=A1=A8=
=E7=AB=A0=E8=8A=82=E7=9A=84=E8=AE=BE=E5=A4=87=E6=9D=90=E6=96=99=E6=B8=85=E5=
=86=8C=E5=92=8C=E5=B7=A5=E7=A8=8B=E9=87=8F=E6=B8=85=E5=8D=95</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-1-3" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (9=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-1-3" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>1 =E6=
=A6=82=E8=BF=B0</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E7=AC=A6=E5=90=88=E7=
=BC=96=E5=88=B6=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=EF=BC=8C=E6=9C=AA=E5=8F=
=91=E7=8E=B0=E7=AB=A0=E8=8A=82=E6=94=B9=E5=8A=A8=E6=88=96=E7=BC=BA=E5=A4=B1=
=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>2 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BF=85=E8=A6=
=81=E6=80=A7</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E5=AE=8C=E6=95=B4=EF=
=BC=8C=E5=8C=85=E5=90=AB=E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=
=AF=E3=80=81=E5=BF=85=E8=A6=81=E6=80=A7=E3=80=81=E6=94=BF=E7=AD=96=E7=AC=A6=
=E5=90=88=E6=80=A7=EF=BC=8C=E7=AC=A6=E5=90=88=E7=BC=96=E5=88=B6=E5=A4=A7=E7=
=BA=B2=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E5=AE=8C=E6=95=B4=E5=
=8C=85=E5=90=AB4.1-4.3=E5=86=85=E5=AE=B9=EF=BC=8C=E4=B8=8E=E7=BC=96=E5=88=
=B6=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=E7=9A=84=E9=A1=B9=E7=9B=AE=E9=80=89=
=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=9C=E7=AB=A0=E8=8A=82=E4=
=B8=80=E8=87=B4=EF=BC=8C=E6=9C=AA=E5=8F=91=E7=8E=B0=E7=AB=A0=E8=8A=82=E7=BC=
=BA=E5=A4=B1=E6=88=96=E6=A0=87=E9=A2=98=E6=94=B9=E5=8A=A8=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E5=AE=8C=E6=95=B4=EF=
=BC=8C=E5=8C=85=E5=90=AB=E9=85=8D=E7=94=B5=E5=B7=A5=E7=A8=8B=E3=80=81=E7=BA=
=BF=E8=B7=AF=E5=B7=A5=E7=A8=8B=E3=80=81=E5=81=9C=E7=94=B5=E8=BF=87=E6=B8=A1=
=E6=96=B9=E6=A1=88=E7=AD=89=E5=AD=90=E9=A1=B9=EF=BC=8C=E7=AC=A6=E5=90=88=E7=
=BC=96=E5=88=B6=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>7 =E9=
=A1=B9=E7=9B=AE=E6=8A=95=E8=9E=8D=E8=B5=84=E4=B8=8E=E8=B4=A2=E5=8A=A1=E6=96=
=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=AE=8C=E6=95=B4=E7=9A=84=E7=
=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=EF=BC=8C=E7=BC=BA=E5=B0=917.4=E5=80=BA=E5=
=8A=A1=E6=B8=85=E5=81=BF=E8=83=BD=E5=8A=9B=E5=88=86=E6=9E=90=E5=86=85=E5=AE=
=B9=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>8 =E9=
=A1=B9=E7=9B=AE=E5=BD=B1=E5=93=8D=E6=95=88=E6=9E=9C=E5=88=86=E6=9E=90</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E6=A0=87=E9=A2=98=E4=B8=8E=E5=A4=A7=E7=
=BA=B2=E8=A6=81=E6=B1=82=E7=9A=848.1=E8=87=B38.4=E5=AF=B9=E5=BA=94=EF=BC=8C=
=E5=86=85=E5=AE=B9=E7=BB=93=E6=9E=84=E5=AE=8C=E6=95=B4=EF=BC=8C=E6=9C=AA=E5=
=8F=91=E7=8E=B0=E7=AB=A0=E8=8A=82=E7=BC=BA=E5=A4=B1=E6=88=96=E6=A0=87=E9=A2=
=98=E6=94=B9=E5=8A=A8=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>9 =E9=
=A1=B9=E7=9B=AE=E9=A3=8E=E9=99=A9=E7=AE=A1=E6=8E=A7=E6=96=B9=E6=A1=88</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E7=AC=AC=E4=B9=9D=E7=AB=A0=E5=86=85=E5=AE=B9=E7=AC=A6=E5=
=90=88=E3=80=8A=E5=AE=A1=E6=9F=A5=E6=8C=87=E5=8D=97=E3=80=8B=E4=B8=AD=E5=85=
=B3=E4=BA=8E=E9=A3=8E=E9=99=A9=E8=AF=86=E5=88=AB=E4=B8=8E=E7=AE=A1=E6=8E=A7=
=E6=96=B9=E6=A1=88=E7=9A=84=E7=BC=96=E5=88=B6=E8=A6=81=E6=B1=82=EF=BC=8C=E7=
=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E5=AE=8C=E6=95=B4=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>10 =E7=
=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=8F=8A=E5=BB=BA=E8=AE=AE</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E7=BB=93=E6=9E=84=E7=AC=A6=E5=90=88=E7=
=BC=96=E5=88=B6=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=EF=BC=8C=E5=8C=85=E5=90=
=AB=E4=B8=BB=E8=A6=81=E7=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=92=8C=E9=97=AE=
=E9=A2=98=E4=B8=8E=E5=BB=BA=E8=AE=AE=E4=B8=A4=E4=B8=AA=E5=AD=90=E7=AB=A0=E8=
=8A=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>11 =E9=
=99=84=E8=A1=A8</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=99=84=E8=A1=A8=E5=86=85=E5=AE=B9=E6=9C=AA=E6=8C=89=E7=
=BC=96=E5=88=B6=E5=A4=A7=E7=BA=B2=E8=A6=81=E6=B1=82=E5=AE=8C=E6=95=B4=E5=91=
=88=E7=8E=B0=E8=AE=BE=E5=A4=87=E6=9D=90=E6=96=99=E6=B8=85=E5=86=8C=E3=80=81=
=E5=B7=A5=E7=A8=8B=E9=87=8F=E6=B8=85=E5=8D=95=E7=AD=89=E6=A0=B8=E5=BF=83=E5=
=86=85=E5=AE=B9=EF=BC=8C=E4=BB=85=E5=8C=85=E5=90=AB=E6=A0=87=E9=A2=98=E5=92=
=8C=E4=B8=8D=E5=AE=8C=E6=95=B4=E5=9B=BE=E7=89=87=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 1.4</h6>
                                        <p class=3D"mb-0 text-muted">=E6=8F=
=90=E4=BE=9B=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E3=80=82=E6=8A=95=
=E8=B5=84=E4=BC=B0=E7=AE=97=E5=BA=94=E4=BE=9D=E6=8D=AE=E5=9B=BD=E5=AE=B6=E9=
=A2=81=E5=B8=83=E7=9A=84=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E7=BC=96=E5=88=
=B6=E5=8A=9E=E6=B3=95=E5=92=8C=E6=8C=87=E6=A0=87=E8=BF=9B=E8=A1=8C=E7=BC=96=
=E5=88=B6=EF=BC=8C=E5=B9=B6=E8=AF=B4=E6=98=8E=E4=BC=B0=E7=AE=97=E6=96=B9=E6=
=B3=95=E5=92=8C=E7=BC=96=E5=88=B6=E4=BE=9D=E6=8D=AE=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E6=8A=95=E8=9E=8D=E8=
=B5=84=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=
=86=E6=A6=82=E8=BF=B0=E5=92=8C=E7=BB=93=E8=AE=BA=E7=AB=A0=E8=8A=82=E6=9C=AA=
=E5=AE=8C=E6=95=B4=E8=AF=B4=E6=98=8E=E4=BC=B0=E7=AE=97=E6=96=B9=E6=B3=95=EF=
=BC=8C=E9=99=84=E8=A1=A8=E7=BC=BA=E5=A4=B1=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=
=97=E4=B9=A6=EF=BC=8C=E9=9C=80=E5=8A=A0=E5=BC=BA=E6=8A=95=E8=B5=84=E4=BC=B0=
=E7=AE=97=E8=A7=84=E8=8C=83=E6=80=A7=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=8A=95=E8=9E=8D=E8=B5=84=E7=AB=A0=
=E8=8A=82=E8=AF=B4=E6=98=8E=E4=BC=B0=E7=AE=97=E6=96=B9=E6=B3=95=E5=92=8C=E4=
=BE=9D=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=A6=82=E8=BF=B0=E5=92=8C=E7=BB=93=
=E8=AE=BA=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=AE=8C=E6=95=B4=E4=
=BC=B0=E7=AE=97=E4=B9=A6</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=99=84=E8=A1=A8=E7=BC=BA=E5=A4=B1=
=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E5=8F=8A=E7=9B=B8=E5=85=B3=E4=
=BE=9D=E6=8D=AE</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=A6=82=E8=BF=B0=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E6=96=B9=E6=
=B3=95=E5=92=8C=E4=BE=9D=E6=8D=AE=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=AE=8C=E5=96=84=E9=99=84=E8=A1=A8=
=E7=9A=84=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E5=92=8C=E7=BC=96=E5=
=88=B6=E4=BE=9D=E6=8D=AE</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-1-4" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (4=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-1-4" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>1 =E6=
=A6=82=E8=BF=B0</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=AE=8C=E6=95=B4=E6=8A=95=E8=
=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=EF=BC=8C=E4=BB=85=E6=8F=90=E5=8F=8A=E9=9D=
=99=E6=80=81=E5=92=8C=E5=8A=A8=E6=80=81=E6=80=BB=E6=8A=95=E8=B5=84=EF=BC=8C=
=E6=9C=AA=E8=AF=B4=E6=98=8E=E4=BC=B0=E7=AE=97=E6=96=B9=E6=B3=95=E5=92=8C=E4=
=BE=9D=E6=8D=AE=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>7 =E9=
=A1=B9=E7=9B=AE=E6=8A=95=E8=9E=8D=E8=B5=84=E4=B8=8E=E8=B4=A2=E5=8A=A1=E6=96=
=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=BE=9D=E6=8D=AE=E5=
=9B=BD=E5=AE=B6=E5=AE=9A=E9=A2=9D=E5=92=8C=E4=BB=B7=E6=A0=BC=E8=B0=83=E6=95=
=B4=E6=96=87=E4=BB=B6=E7=BC=96=E5=88=B6=EF=BC=8C=E4=B8=94=E8=AF=B4=E6=98=8E=
=E4=BA=86=E4=BC=B0=E7=AE=97=E6=96=B9=E6=B3=95=E5=92=8C=E7=BC=96=E5=88=B6=E4=
=BE=9D=E6=8D=AE=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>10 =E7=
=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=8F=8A=E5=BB=BA=E8=AE=AE</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E4=B8=AD=E4=BB=85=E6=8F=90=E5=8F=8A=E6=
=80=BB=E6=8A=95=E8=B5=84=E9=87=91=E9=A2=9D318.20=E4=B8=87=E5=85=83=EF=BC=8C=
=E6=9C=AA=E8=AF=B4=E6=98=8E=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E6=96=B9=E6=
=B3=95=E3=80=81=E7=BC=96=E5=88=B6=E4=BE=9D=E6=8D=AE=E5=8F=8A=E6=8A=95=E8=B5=
=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>11 =E9=
=99=84=E8=A1=A8</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=99=84=E8=A1=A8=E6=9C=AA=E6=8F=90=E4=BE=9B=E6=8A=95=E8=
=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=EF=BC=8C=E6=9C=AA=E8=AF=B4=E6=98=8E=E4=BC=
=B0=E7=AE=97=E6=96=B9=E6=B3=95=E5=92=8C=E7=BC=96=E5=88=B6=E4=BE=9D=E6=8D=AE=
=EF=BC=8C=E4=B8=8E=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=99=E8=A6=81=E6=B1=82=E7=
=9A=84=E5=B7=A5=E7=A8=8B=E4=BC=B0=E7=AE=97=E4=B9=A6=E7=BC=BA=E5=A4=B1=E3=80=
=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 2.5</h6>
                                        <p class=3D"mb-0 text-muted">1.=E9=
=A1=B9=E7=9B=AE=E6=98=AF=E5=90=A6=E5=B1=9E=E4=BA=8E=E5=86=9C=E6=9D=91=E7=94=
=B5=E7=BD=91=E8=8C=83=E5=9B=B4=EF=BC=8C=E6=98=AF=E5=90=A6=E6=9D=A5=E6=BA=90=
=E4=BA=8E=E5=9B=BD=E5=AE=B6=E4=B8=AD=E5=A4=AE=E5=86=9C=E7=BD=91=E9=87=8D=E7=
=82=B9=E6=94=AF=E6=8C=81=E5=AF=B9=E8=B1=A1=EF=BC=88832=E4=B8=AA=E8=B4=AB=E5=
=9B=B0=E5=8E=BF=EF=BC=89=E3=80=82
2.=E9=A1=B9=E7=9B=AE=E6=98=AF=E5=90=A6=E5=B7=B2=E7=BA=B3=E5=85=A5=E5=BD=93=
=E5=9C=B0=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=B7=A9=E5=9B=BA=E6=8F=90=E5=
=8D=87=E5=B7=A5=E7=A8=8B=E8=A7=84=E5=88=92=E5=92=8C=E6=BB=9A=E5=8A=A8=E6=8A=
=95=E8=B5=84=E8=AE=A1=E5=88=92=EF=BC=8C=E9=A1=B9=E7=9B=AE=E5=8F=AF=E8=A1=8C=
=E6=80=A7=E7=A0=94=E7=A9=B6=E6=8A=A5=E5=91=8A=E7=BB=8F=E6=89=80=E5=9C=A8=E7=
=9C=81=E7=BA=A7=E5=8F=91=E5=B1=95=E6=94=B9=E9=9D=A9=E5=A7=94(=E8=83=BD=E6=
=BA=90=E5=B1=80)=E5=AE=A1=E6=89=B9=E5=90=8C=E6=84=8F=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E8=A6=
=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=E6=9C=AA=
=E6=98=8E=E7=A1=AE=E7=BA=B3=E5=85=A5=E8=A7=84=E5=88=92=E6=83=85=E5=86=B5=EF=
=BC=8C=E9=9C=80=E5=8A=A0=E5=BC=BA=E8=A7=84=E5=88=92=E8=A1=94=E6=8E=A5=E6=80=
=A7=E8=AF=B4=E6=98=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E8=83=8C=E6=99=AF=E6=98=8E=E7=A1=AE=E5=B1=9E=E4=BA=8E=E5=86=9C=E6=9D=91=E7=
=94=B5=E7=BD=91=E8=8C=83=E5=9B=B4</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=
=E6=80=A7=E9=83=A8=E5=88=86=E6=8F=90=E5=8F=8A=E8=A7=84=E5=88=92=E8=A1=94=E6=
=8E=A5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E7=BA=B3=E5=85=A5=E8=A7=84=E5=
=88=92=E6=83=85=E5=86=B5</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=80=89=E5=9D=80=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E9=A1=B9=E7=9B=AE=E7=BA=B3=E5=85=A5=E8=A7=84=E5=
=88=92=E7=9A=84=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E8=A7=84=E5=88=92=
=E5=AE=A1=E6=89=B9=E6=96=87=E4=BB=B6=E7=9A=84=E5=BC=95=E7=94=A8=E4=BE=9D=E6=
=8D=AE</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-2-5" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-2-5" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>2 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BF=85=E8=A6=
=81=E6=80=A7</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E6=98=8E=E7=A1=AE=E5=B1=9E=E4=BA=8E=E5=
=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E8=8C=83=E5=9B=B4=EF=BC=8C=E4=B8=94=E6=94=
=BF=E7=AD=96=E7=AC=A6=E5=90=88=E6=80=A7=E9=83=A8=E5=88=86=E6=8F=90=E5=8F=8A=
=E7=AC=A6=E5=90=88=E6=A8=AA=E5=B7=9E=E5=B8=82=E5=9B=BD=E5=9C=9F=E7=A9=BA=E9=
=97=B4=E8=A7=84=E5=88=92=E5=92=8C=E9=85=8D=E7=94=B5=E7=BD=91=E8=A7=84=E5=88=
=92=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.6</h6>
                                        <p class=3D"mb-0 text-muted">=E4=B8=
=AD=E5=A4=AE=E9=A2=84=E7=AE=97=E5=86=85=E6=8A=95=E8=B5=84=E6=94=AF=E6=8C=81=
=E6=AF=94=E4=BE=8B=E6=98=AF=E5=90=A6=E5=90=88=E8=A7=84=EF=BC=88=E4=B8=AD=E5=
=A4=AE=E9=A2=84=E7=AE=97=E5=86=85=E6=8A=95=E8=B5=84=E5=8D=A0=E6=AF=94=E4=B8=
=BA20%=EF=BC=89=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E6=9C=AA=E5=9C=A8=E7=
=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=E6=8F=90=E5=8F=8A=E4=B8=AD=E5=A4=AE=E9=A2=
=84=E7=AE=97=E5=86=85=E6=8A=95=E8=B5=84=E6=AF=94=E4=BE=8B=EF=BC=8C=E9=9C=80=
=E8=A1=A5=E5=85=85=E6=8A=95=E8=B5=84=E5=90=88=E8=A7=84=E6=80=A7=E5=88=86=E6=
=9E=90=E5=86=85=E5=AE=B9=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E5=90=84=E7=AB=A0=E8=8A=82=E5=9D=87=
=E6=9C=AA=E6=B6=89=E5=8F=8A=E4=B8=AD=E5=A4=AE=E9=A2=84=E7=AE=97=E5=86=85=E6=
=8A=95=E8=B5=84=E6=AF=94=E4=BE=8B=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E6=8A=95=
=E8=B5=84=E5=8D=A0=E6=AF=94=E5=90=88=E8=A7=84=E6=80=A7=E5=88=86=E6=9E=90</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=BC=95=E7=94=A8=E7=9B=B8=
=E5=85=B3=E6=8A=95=E8=B5=84=E6=AF=94=E4=BE=8B=E6=94=BF=E7=AD=96=E6=96=87=E4=
=BB=B6</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=8A=95=E8=9E=8D=E8=B5=84=
=E7=AB=A0=E8=8A=82=E8=A1=A5=E5=85=85=E4=B8=AD=E5=A4=AE=E9=A2=84=E7=AE=97=E5=
=86=85=E6=8A=95=E8=B5=84=E6=AF=94=E4=BE=8B=E5=88=86=E6=9E=90</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=BC=95=E7=94=A8=E5=9B=BD=E5=AE=B6=
=E5=85=B3=E4=BA=8E=E4=B8=AD=E5=A4=AE=E9=A2=84=E7=AE=97=E5=86=85=E6=8A=95=E8=
=B5=84=E6=AF=94=E4=BE=8B=E7=9A=84=E6=94=BF=E7=AD=96=E6=96=87=E4=BB=B6</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.7</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E4=B8=BB=E8=A6=81=E7=9B=AE=E7=9A=84=E6=98=AF=
=E4=B8=BA=E4=BF=9D=E9=9A=9C=E5=86=9C=E6=9D=91=E7=94=9F=E4=BA=A7=E7=94=9F=E6=
=B4=BB=E7=94=B5=E5=8A=9B=E9=9C=80=E6=B1=82=EF=BC=8C=E6=9C=8D=E5=8A=A1=E5=86=
=9C=E4=B8=9A=E5=86=9C=E6=9D=91=E7=8E=B0=E4=BB=A3=E5=8C=96=EF=BC=8C=E5=B7=A5=
=E4=B8=9A=E5=9B=AD=E3=80=81=E4=B9=A1=E9=95=87=E4=BC=81=E4=B8=9A=E3=80=81=E5=
=8C=BB=E9=99=A2=E3=80=81=E5=8E=BF=E6=94=BF=E5=BA=9C=E7=AD=89=E4=BF=9D=E4=BE=
=9B=E7=94=B5=E7=82=B9=E4=B8=8D=E5=B1=9E=E4=BA=8E=E4=B8=AD=E5=A4=AE=E5=86=9C=
=E7=BD=91=E6=8A=95=E8=B5=84=E8=8C=83=E5=9B=B4=E3=80=82=E5=AE=A1=E6=9F=A510=
=E5=8D=83=E4=BC=8F=E5=8F=8A=E4=BB=A5=E4=B8=8B=E7=94=B5=E5=8E=8B=E7=AD=89=E7=
=BA=A7=E9=A1=B9=E7=9B=AE=E9=A2=84=E6=9C=9F=E6=8E=A5=E5=85=A5=E5=86=9C=E6=9D=
=91=E5=B1=85=E6=B0=91=E3=80=81=E5=86=9C=E4=B8=9A=E7=94=9F=E4=BA=A7=E5=86=9C=
=E4=BA=A7=E5=93=81=E5=8A=A0=E5=B7=A5=E7=AD=89=E6=B6=89=E5=86=9C=E7=94=A8=E6=
=88=B7=E5=AE=B9=E9=87=8F=E4=B8=8D=E5=BE=97=E4=BD=8E=E4=BA=8E=E6=8E=A5=E5=85=
=A5=E7=94=A8=E6=88=B7=E6=80=BB=E5=AE=B9=E9=87=8F=E7=9A=8460%=EF=BC=8C110=EF=
=BC=8866=EF=BC=89=E5=8D=83=E4=BC=8F=E3=80=8135=E5=8D=83=E4=BC=8F=E7=94=B5=
=E5=8E=8B=E7=AD=89=E7=BA=A7=E9=A1=B9=E7=9B=AE=E9=A2=84=E6=9C=9F=E6=8E=A5=E5=
=85=A5=E6=B6=89=E5=86=9C=E7=94=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E4=B8=8D=E5=BE=
=97=E4=BD=8E=E4=BA=8E=E6=8E=A5=E5=85=A5=E7=94=A8=E6=88=B7=E6=80=BB=E5=AE=B9=
=E9=87=8F=E7=9A=8450%=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E7=AC=A6=E5=90=88=E6=B6=89=E5=86=9C=E7=94=
=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E5=85=B6=
=E4=BB=96=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E4=BE=9B=E7=9B=B8=E5=85=B3=E6=
=95=B0=E6=8D=AE=EF=BC=8C=E9=9C=80=E5=8A=A0=E5=BC=BA=E6=95=B0=E6=8D=AE=E6=94=
=AF=E6=92=91=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E6=98=8E=E7=A1=AE=E6=B6=89=E5=86=9C=E7=94=A8=E6=88=B7=E5=
=AE=B9=E9=87=8F=E6=AF=94=E4=BE=8B</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E5=85=B6=E4=BB=96=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E6=8F=90=E4=BE=9B=E7=94=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E6=95=B0=E6=
=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=9C=A8=E9=99=84=E8=A1=A8=
=E4=B8=AD=E5=88=97=E6=98=8E=E7=94=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E7=BB=9F=E8=
=AE=A1</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=99=84=E8=A1=A8=E4=B8=AD=
=E5=A2=9E=E5=8A=A0=E7=94=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E7=BB=9F=E8=AE=A1=E8=
=A1=A8</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E5=BD=B1=E5=93=8D=E6=95=88=
=E6=9E=9C=E5=88=86=E6=9E=90=E7=AB=A0=E8=8A=82=E8=A1=A5=E5=85=85=E7=94=A8=E6=
=88=B7=E5=AE=B9=E9=87=8F=E6=95=B0=E6=8D=AE</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-3-7" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-3-7" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E4=B8=BA10=E5=8D=83=E4=BC=8F=E9=85=8D=
=E7=94=B5=E5=B7=A5=E7=A8=8B=EF=BC=8C=E6=98=8E=E7=A1=AE=E9=A2=84=E6=9C=9F=E6=
=8E=A5=E5=85=A5=E6=B6=89=E5=86=9C=E7=94=A8=E6=88=B7=E5=AE=B9=E9=87=8F=E6=AF=
=94=E4=BE=8B=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=99=
3.7=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.8</h6>
                                        <p class=3D"mb-0 text-muted">=E5=BB=
=BA=E8=AE=BE=E5=86=85=E5=AE=B9=E6=98=AF=E5=90=A6=E6=B6=89=E5=8F=8A=E7=94=A8=
=E6=88=B7=E5=B7=A5=E7=A8=8B=E3=80=81=E8=BF=81=E6=94=B9=E5=B7=A5=E7=A8=8B=E7=
=AD=89=E7=94=A8=E6=88=B7=E5=87=BA=E8=B5=84=E5=86=85=E5=AE=B9=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E6=9C=AA=E6=B6=89=E5=8F=8A=E7=94=A8=E6=88=
=B7=E5=B7=A5=E7=A8=8B=E6=88=96=E8=BF=81=E6=94=B9=E5=B7=A5=E7=A8=8B=EF=BC=8C=
=E7=AC=A6=E5=90=88=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=9C=80=E7=A1=AE=E8=
=AE=A4=E6=98=AF=E5=90=A6=E5=AD=98=E5=9C=A8=E9=9A=90=E6=80=A7=E7=94=A8=E6=88=
=B7=E5=87=BA=E8=B5=84=E5=86=85=E5=AE=B9=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E6=9C=AA=E6=8F=90=E5=8F=8A=E7=94=A8=E6=88=B7=E5=B7=A5=E7=
=A8=8B=E6=88=96=E8=BF=81=E6=94=B9=E5=B7=A5=E7=A8=8B</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=8F=91=E7=8E=B0=E7=94=A8=
=E6=88=B7=E5=87=BA=E8=B5=84=E5=86=85=E5=AE=B9=E6=8F=8F=E8=BF=B0</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=9C=80=E8=BF=9B=E4=B8=80=E6=AD=A5=
=E7=A1=AE=E8=AE=A4=E6=98=AF=E5=90=A6=E5=AD=98=E5=9C=A8=E9=9A=90=E6=80=A7=E7=
=94=A8=E6=88=B7=E5=87=BA=E8=B5=84</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E6=98=8E=E7=A1=AE=E7=94=A8=E6=88=B7=E5=
=87=BA=E8=B5=84=E5=86=85=E5=AE=B9</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E7=94=A8=E6=88=B7=
=E5=87=BA=E8=B5=84=E5=86=85=E5=AE=B9=E7=9A=84=E4=B8=93=E9=A1=B9=E8=AF=B4=E6=
=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-3-8" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-3-8" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=8F=90=E5=8F=8A=E7=94=A8=E6=88=B7=E5=B7=A5=E7=
=A8=8B=E6=88=96=E8=BF=81=E6=94=B9=E5=B7=A5=E7=A8=8B=E5=86=85=E5=AE=B9=EF=BC=
=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=993.8=E8=A6=81=E6=B1=
=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.9</h6>
                                        <p class=3D"mb-0 text-muted">=E5=8E=
=9F=E5=88=99=E4=B8=8A=E4=B8=8D=E6=94=AF=E6=8C=81=E4=B8=93=E9=A1=B9=E7=94=B5=
=E7=BD=91=E6=99=BA=E8=83=BD=E5=8C=96=E5=BB=BA=E8=AE=BE=E6=94=B9=E9=80=A0=E9=
=A1=B9=E7=9B=AE=E3=80=82
=E8=87=AA=E5=8A=A8=E5=8C=96=E5=BC=80=E5=85=B3=E5=B1=9E=E4=BA=8E=E6=99=BA=E8=
=83=BD=E5=8C=96=E9=A1=B9=E7=9B=AE=EF=BC=8C=E6=8C=89=E7=85=A7=E8=87=AA=E5=8A=
=A8=E5=8C=96=E5=BC=80=E5=85=B3=E6=8A=95=E8=B5=84=E5=8D=A0=E6=AF=94=E4=B8=8D=
=E5=BE=97=E9=AB=98=E4=BA=8E=E6=80=BB=E6=8A=95=E8=B5=84=E7=9A=8430%=E4=BD=9C=
=E4=B8=BA=E8=BE=B9=E7=95=8C=E6=9D=A1=E4=BB=B6=EF=BC=88=E6=97=A2=E6=9C=89=E7=
=BA=BF=E8=B7=AF=E6=94=B9=E9=80=A0=E6=88=96=E6=96=B0=E5=BB=BA=E7=BA=BF=E8=B7=
=AF=E5=90=8C=E6=AD=A5=E6=96=B0=E8=A3=85=E5=BC=80=E5=85=B3=E7=9A=84=E9=A1=B9=
=E7=9B=AE=EF=BC=89=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E6=9C=AA=E6=B6=89=E5=8F=8A=E6=99=BA=E8=83=
=BD=E5=8C=96=E6=94=B9=E9=80=A0=EF=BC=8C=E7=AC=A6=E5=90=88=E8=A6=81=E6=B1=82=
=EF=BC=8C=E4=BD=86=E9=9C=80=E7=A1=AE=E8=AE=A4=E6=98=AF=E5=90=A6=E5=8C=85=E5=
=90=AB=E8=87=AA=E5=8A=A8=E5=8C=96=E5=BC=80=E5=85=B3=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E6=9C=AA=E6=8F=90=E5=8F=8A=E6=99=BA=E8=83=BD=E5=8C=96=E6=
=94=B9=E9=80=A0</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=8F=91=E7=8E=B0=E8=87=AA=
=E5=8A=A8=E5=8C=96=E5=BC=80=E5=85=B3=E7=9B=B8=E5=85=B3=E5=86=85=E5=AE=B9</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=9C=80=E8=A1=A5=E5=85=85=E6=99=BA=
=E8=83=BD=E5=8C=96=E9=A1=B9=E7=9B=AE=E8=BE=B9=E7=95=8C=E6=9D=A1=E4=BB=B6=E8=
=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E6=98=8E=E7=A1=AE=E6=99=BA=E8=83=BD=E5=
=8C=96=E9=A1=B9=E7=9B=AE=E8=BE=B9=E7=95=8C</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E8=87=AA=E5=8A=A8=
=E5=8C=96=E5=BC=80=E5=85=B3=E6=8A=95=E8=B5=84=E5=8D=A0=E6=AF=94=E5=88=86=E6=
=9E=90</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-3-9" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-3-9" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=B6=89=E5=8F=8A=E4=B8=93=E9=A1=B9=E7=94=B5=E7=
=BD=91=E6=99=BA=E8=83=BD=E5=8C=96=E5=BB=BA=E8=AE=BE=E6=94=B9=E9=80=A0=E9=A1=
=B9=E7=9B=AE=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=99=
3.9=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.10</h6>
                                        <p class=3D"mb-0 text-muted">=E5=8E=
=9F=E5=88=99=E4=B8=8A=E4=B8=8D=E6=94=AF=E6=8C=81=E7=94=B5=E5=8A=A8=E6=B1=BD=
=E8=BD=A6=E3=80=81=E6=96=B0=E8=83=BD=E6=BA=90=E7=AD=89=E7=9A=84=E9=85=8D=E5=
=A5=97=E6=8E=A5=E5=85=A5=E9=A1=B9=E7=9B=AE=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E6=9C=AA=E6=B6=89=E5=8F=8A=E6=96=B0=E8=83=
=BD=E6=BA=90=E9=85=8D=E5=A5=97=E6=8E=A5=E5=85=A5=EF=BC=8C=E7=AC=A6=E5=90=88=
=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=9C=80=E7=A1=AE=E8=AE=A4=E6=98=AF=E5=
=90=A6=E5=8C=85=E5=90=AB=E7=94=B5=E5=8A=A8=E6=B1=BD=E8=BD=A6=E5=85=85=E7=94=
=B5=E8=AE=BE=E6=96=BD=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E6=9C=AA=E6=8F=90=E5=8F=8A=E6=96=B0=E8=83=BD=E6=BA=90=E9=
=85=8D=E5=A5=97=E6=8E=A5=E5=85=A5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=8F=91=E7=8E=B0=E7=94=B5=
=E5=8A=A8=E6=B1=BD=E8=BD=A6=E5=85=85=E7=94=B5=E8=AE=BE=E6=96=BD=E7=9B=B8=E5=
=85=B3=E5=86=85=E5=AE=B9</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=9C=80=E8=A1=A5=E5=85=85=E6=96=B0=
=E8=83=BD=E6=BA=90=E9=85=8D=E5=A5=97=E6=8E=A5=E5=85=A5=E7=9A=84=E4=B8=93=E9=
=A1=B9=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E6=98=8E=E7=A1=AE=E6=96=B0=E8=83=BD=E6=
=BA=90=E9=85=8D=E5=A5=97=E6=8E=A5=E5=85=A5=E6=83=85=E5=86=B5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E7=94=B5=E5=8A=A8=
=E6=B1=BD=E8=BD=A6=E5=85=85=E7=94=B5=E8=AE=BE=E6=96=BD=E8=A7=84=E5=88=92=E5=
=86=85=E5=AE=B9</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-3-10" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-3-10" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=B6=89=E5=8F=8A=E7=94=B5=E5=8A=A8=E6=B1=BD=E8=
=BD=A6=E6=88=96=E6=96=B0=E8=83=BD=E6=BA=90=E9=85=8D=E5=A5=97=E6=8E=A5=E5=85=
=A5=E9=A1=B9=E7=9B=AE=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=
=E5=88=993.10=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 3.11</h6>
                                        <p class=3D"mb-0 text-muted">=E5=8E=
=9F=E5=88=99=E4=B8=8A=E4=B8=8D=E6=94=AF=E6=8C=81=E9=9D=9E=E5=BF=85=E8=A6=81=
=E5=85=A5=E5=9C=B0=E7=94=B5=E7=BC=86=E9=A1=B9=E7=9B=AE=E3=80=82=E5=8D=95=E4=
=B8=AA=E9=A1=B9=E7=9B=AE=E7=9A=84=E7=94=B5=E7=BC=86=E5=8E=9F=E5=88=99=E4=B8=
=8A=E4=B8=8D=E8=83=BD=E8=B6=85=E8=BF=87200m=EF=BC=88=E5=8F=98=E7=94=B5=E7=
=AB=99=E5=87=BA=E7=BA=BF=E3=80=81=E8=B7=A8=E8=B6=8A=E9=87=8D=E8=A6=81=E4=BA=
=A4=E9=80=9A=E8=A6=81=E9=81=93=E7=AD=89=E5=BF=85=E9=A1=BB=E4=BD=BF=E7=94=A8=
=E7=94=B5=E7=BC=86=E7=9A=84=E9=99=A4=E5=A4=96=EF=BC=89=EF=BC=8C=E4=B8=8D=E5=
=BE=97=E5=90=AB=E6=9C=89=E7=8E=AF=E7=BD=91=E6=9F=9C=E7=AD=89=E7=94=B5=E7=BC=
=86=E7=BA=BF=E8=B7=AF=E4=B8=8A=E7=9A=84=E8=AE=BE=E5=A4=87=E3=80=82
=E9=9D=9E=E5=BF=85=E8=A6=81=E5=85=A5=E5=9C=B0=E7=94=B5=E7=BC=86=E9=A1=B9=E7=
=9B=AE=E5=88=A4=E5=88=AB=E6=96=B9=E6=B3=95=EF=BC=9A
=EF=BC=881=EF=BC=89=E5=AE=A1=E6=9F=A5=E6=98=AF=E5=90=A6=E5=B0=86=E5=8E=9F=
=E6=9D=A5=E6=9E=B6=E7=A9=BA=E7=BA=BF=E8=B7=AF=E6=8B=86=E9=99=A4=E5=B9=B6=E5=
=9C=A8=E7=9B=B8=E5=90=8C=E7=BA=BF=E8=A1=8C=E4=B8=8A=E6=96=B0=E5=BB=BA=E7=94=
=B5=E7=BC=86=EF=BC=9B
=EF=BC=882=EF=BC=89=E5=AE=A1=E6=9F=A5=E5=8E=9F=E6=9D=A5=E6=9E=B6=E7=A9=BA=
=E7=9A=84=E4=BE=9B=E7=94=B5=E8=B7=AF=E5=BE=84=E6=98=AF=E5=90=A6=E5=8F=98=E6=
=88=90=E4=BA=86=E7=94=B5=E7=BC=86=E8=B7=AF=E5=BE=84=EF=BC=8C=E5=8D=B3=E5=8E=
=9F=E6=9D=A5=E7=94=B1=E6=9E=B6=E7=A9=BA=E7=BA=BF=E8=B7=AF=E8=BF=9E=E6=8E=A5=
=E7=9A=84=E9=85=8D=E5=8F=98=E6=88=96=E8=80=85=E6=94=AF=E8=B7=AF=E8=B4=9F=E8=
=8D=B7=EF=BC=8C=E6=94=B9=E7=94=B1=E7=94=B5=E7=BC=86=E7=BA=BF=E8=B7=AF=E6=8E=
=A5=E5=88=B0=E7=94=B5=E6=BA=90=E7=82=B9=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E6=9C=AA=E6=B6=89=E5=8F=8A=E9=9D=9E=E5=BF=
=85=E8=A6=81=E5=85=A5=E5=9C=B0=E7=94=B5=E7=BC=86=EF=BC=8C=E7=AC=A6=E5=90=88=
=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=9C=80=E7=A1=AE=E8=AE=A4=E7=94=B5=E7=
=BC=86=E8=B7=AF=E5=BE=84=E6=98=AF=E5=90=A6=E7=AC=A6=E5=90=88=E8=A7=84=E8=8C=
=83=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E6=9C=AA=E6=8F=90=E5=8F=8A=E9=9D=9E=E5=BF=85=E8=A6=81=E5=
=85=A5=E5=9C=B0=E7=94=B5=E7=BC=86</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=8F=91=E7=8E=B0=E7=94=B5=
=E7=BC=86=E8=B7=AF=E5=BE=84=E8=BF=9D=E8=A7=84=E6=83=85=E5=86=B5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=9C=80=E8=A1=A5=E5=85=85=E7=94=B5=
=E7=BC=86=E8=B7=AF=E5=BE=84=E5=90=88=E8=A7=84=E6=80=A7=E8=AF=B4=E6=98=8E</l=
i>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E6=98=8E=E7=A1=AE=E7=94=B5=E7=BC=86=E8=
=B7=AF=E5=BE=84=E5=90=88=E8=A7=84=E6=80=A7</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E7=94=B5=E7=BC=86=
=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E7=9A=84=E4=B8=93=E9=A1=B9=E8=AF=B4=E6=
=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-3-11" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-3-11" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=B6=89=E5=8F=8A=E9=9D=9E=E5=BF=85=E8=A6=81=E5=
=85=A5=E5=9C=B0=E7=94=B5=E7=BC=86=E9=A1=B9=E7=9B=AE=EF=BC=8C=E7=AC=A6=E5=90=
=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=88=993.11=E8=A6=81=E6=B1=82=E3=80=82</sma=
ll>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 4.12</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E6=98=AF=E5=90=A6=E7=A1=AE=E4=B8=BA=E5=86=9C=E6=9D=91=E7=94=B5=
=E7=BD=91=E9=A1=B9=E7=9B=AE=EF=BC=8C=E6=98=AF=E5=90=A6=E5=B7=B2=E7=BA=B3=E5=
=85=A5=E6=9C=AC=E5=9C=B0=E5=8C=BA=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=B7=
=A9=E5=9B=BA=E6=8F=90=E5=8D=87=E5=B7=A5=E7=A8=8B=E8=A7=84=E5=88=92=E3=80=82=
</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E8=A6=
=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=E6=9C=AA=
=E6=98=8E=E7=A1=AE=E7=BA=B3=E5=85=A5=E8=A7=84=E5=88=92=E6=83=85=E5=86=B5=EF=
=BC=8C=E9=9C=80=E5=8A=A0=E5=BC=BA=E8=A7=84=E5=88=92=E8=A1=94=E6=8E=A5=E6=80=
=A7=E8=AF=B4=E6=98=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E8=83=8C=E6=99=AF=E6=98=8E=E7=A1=AE=E5=B1=9E=E4=BA=8E=E5=86=9C=E6=9D=91=E7=
=94=B5=E7=BD=91=E8=8C=83=E5=9B=B4</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=
=E6=80=A7=E9=83=A8=E5=88=86=E6=8F=90=E5=8F=8A=E8=A7=84=E5=88=92=E8=A1=94=E6=
=8E=A5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E7=BA=B3=E5=85=A5=E8=A7=84=E5=
=88=92=E6=83=85=E5=86=B5</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=80=89=E5=9D=80=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E9=A1=B9=E7=9B=AE=E7=BA=B3=E5=85=A5=E8=A7=84=E5=
=88=92=E7=9A=84=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E8=A7=84=E5=88=92=
=E5=AE=A1=E6=89=B9=E6=96=87=E4=BB=B6=E7=9A=84=E5=BC=95=E7=94=A8=E4=BE=9D=E6=
=8D=AE</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-4-12" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-4-12" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>2 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BF=85=E8=A6=
=81=E6=80=A7</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E6=98=8E=E7=A1=AE=E5=B1=9E=E4=BA=8E=E5=
=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E8=8C=83=E5=9B=B4=EF=BC=8C=E4=B8=94=E7=AC=
=A6=E5=90=88=E6=A8=AA=E5=B7=9E=E5=B8=82=E5=9B=BD=E5=9C=9F=E7=A9=BA=E9=97=B4=
=E8=A7=84=E5=88=92=E5=92=8C=E9=85=8D=E7=94=B5=E7=BD=91=E8=A7=84=E5=88=92=E3=
=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 4.13</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E5=BF=85=E8=A6=81=E6=80=A7=E7=9A=84=E8=AE=BA=
=E8=BF=B0=E6=98=AF=E5=90=A6=E6=B8=85=E6=A5=9A=E3=80=81=E5=85=85=E5=88=86=EF=
=BC=8C=E9=A1=B9=E7=9B=AE=E6=98=AF=E5=90=A6=E7=A1=AE=E6=9C=89=E5=BF=85=E8=A6=
=81=E5=BB=BA=E8=AE=BE=E3=80=82
=E4=BE=8B=E5=A6=82=EF=BC=9A=E6=98=AF=E5=90=A6=E4=BB=8E=E4=BE=9B=E7=94=B5=E8=
=83=BD=E5=8A=9B=E3=80=81=E4=BE=9B=E7=94=B5=E5=8F=AF=E9=9D=A0=E6=80=A7=E3=80=
=81=E7=94=B5=E8=83=BD=E8=B4=A8=E9=87=8F=E7=AD=89=E6=96=B9=E9=9D=A2=E7=AE=80=
=E8=BF=B0=E5=BD=93=E5=9C=B0=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=AD=98=E5=
=9C=A8=E7=9A=84=E7=AA=81=E5=87=BA=E7=9F=9B=E7=9B=BE=E9=97=AE=E9=A2=98=EF=BC=
=8C=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E7=9A=84=E5=BF=85=E8=A6=81=E6=80=A7=E5=92=8C=E5=BB=BA=E8=AE=BE=E6=97=B6=E6=
=9C=BA=E7=9A=84=E9=80=82=E5=BD=93=E6=80=A7=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=
=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E7=AB=A0=E8=8A=82=E5=85=85=E5=88=86=E8=AE=
=BA=E8=BF=B0=E5=BF=85=E8=A6=81=E6=80=A7=EF=BC=8C=E4=BD=86=E9=80=89=E5=9D=80=
=E5=92=8C=E5=BD=B1=E5=93=8D=E5=88=86=E6=9E=90=E7=AB=A0=E8=8A=82=E7=BC=BA=E4=
=B9=8F=E5=85=B7=E4=BD=93=E6=95=B0=E6=8D=AE=E6=94=AF=E6=92=91=EF=BC=8C=E9=9C=
=80=E5=8A=A0=E5=BC=BA=E8=AE=BA=E8=AF=81=E6=B7=B1=E5=BA=A6=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E8=83=8C=E6=99=AF=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E7=94=B5=E7=BD=91=E9=
=97=AE=E9=A2=98</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E6=8F=90=E4=BE=9B=E4=BE=9B=E7=94=B5=E8=83=BD=E5=8A=9B=E7=AD=89=E5=
=85=B7=E4=BD=93=E6=95=B0=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E5=BD=B1=E5=93=8D=E5=88=86=E6=9E=90=
=E7=AB=A0=E8=8A=82=E7=BC=BA=E4=B9=8F=E5=85=B7=E4=BD=93=E9=97=AE=E9=A2=98=E5=
=88=86=E6=9E=90</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=80=89=E5=9D=80=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E4=BE=9B=E7=94=B5=E8=83=BD=E5=8A=9B=E7=AD=89=E5=
=85=B7=E4=BD=93=E6=95=B0=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E5=BD=B1=E5=93=8D=E5=88=86=
=E6=9E=90=E7=AB=A0=E8=8A=82=E5=A2=9E=E5=8A=A0=E5=85=B7=E4=BD=93=E9=97=AE=E9=
=A2=98=E5=88=86=E6=9E=90</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-4-13" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (4=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-4-13" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>2 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BF=85=E8=A6=
=81=E6=80=A7</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E4=BB=8E=E4=BE=9B=E7=94=B5=E8=83=BD=E5=
=8A=9B=E3=80=81=E7=BA=BF=E8=B7=AF=E8=B4=9F=E8=BD=BD=E7=8E=87=E7=AD=89=E6=96=
=B9=E9=9D=A2=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E4=BA=86=E7=94=B5=E7=BD=91=
=E5=AD=98=E5=9C=A8=E7=9A=84=E9=97=AE=E9=A2=98=EF=BC=8C=E8=AE=BA=E8=AF=81=E4=
=BA=86=E9=A1=B9=E7=9B=AE=E5=BF=85=E8=A6=81=E6=80=A7=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E9=A1=B9=E7=
=9B=AE=E5=BB=BA=E8=AE=BE=E5=BF=85=E8=A6=81=E6=80=A7=EF=BC=8C=E7=BC=BA=E4=B9=
=8F=E4=BE=9B=E7=94=B5=E8=83=BD=E5=8A=9B=E3=80=81=E5=8F=AF=E9=9D=A0=E6=80=A7=
=E3=80=81=E7=94=B5=E8=83=BD=E8=B4=A8=E9=87=8F=E7=AD=89=E6=96=B9=E9=9D=A2=E7=
=9A=84=E5=85=B7=E4=BD=93=E9=97=AE=E9=A2=98=E5=88=86=E6=9E=90=E3=80=82</smal=
l>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=
=E6=9C=AC=E7=AC=A6=E5=90=88</span>
                                                            <strong>8 =E9=
=A1=B9=E7=9B=AE=E5=BD=B1=E5=93=8D=E6=95=88=E6=9E=9C=E5=88=86=E6=9E=90</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E5=BF=85=E8=A6=81=E6=80=A7=E8=AE=BA=E8=
=BF=B0=E6=8F=90=E5=8F=8A=E4=BE=9B=E7=94=B5=E5=8F=AF=E9=9D=A0=E6=80=A7=E6=8F=
=90=E5=8D=87=E5=8F=8A=E7=A4=BE=E4=BC=9A=E7=BB=8F=E6=B5=8E=E6=95=88=E7=9B=8A=
=EF=BC=8C=E4=BD=86=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E4=BE=9B=E7=
=94=B5=E8=83=BD=E5=8A=9B=E3=80=81=E7=94=B5=E8=83=BD=E8=B4=A8=E9=87=8F=E7=AD=
=89=E5=85=B7=E4=BD=93=E9=97=AE=E9=A2=98=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=
=E6=9C=AC=E7=AC=A6=E5=90=88</span>
                                                            <strong>10 =E7=
=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=8F=8A=E5=BB=BA=E8=AE=AE</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E5=BF=85=E8=A6=81=E6=
=80=A7=E9=83=A8=E5=88=86=E6=8F=90=E5=8F=8A=E8=A7=A3=E5=86=B3=E8=BF=87=E8=BD=
=BD=E3=80=81=E6=AE=8B=E6=97=A7=E7=AD=89=E9=97=AE=E9=A2=98=EF=BC=8C=E4=BD=86=
=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E4=BE=9B=E7=94=B5=E8=83=BD=E5=
=8A=9B=E3=80=81=E5=8F=AF=E9=9D=A0=E6=80=A7=E7=AD=89=E5=85=B7=E4=BD=93=E6=95=
=B0=E6=8D=AE=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 4.14</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E7=9A=84=E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=E6=80=A7=E8=AE=BA=
=E8=BF=B0=E6=98=AF=E5=90=A6=E6=B8=85=E6=A5=9A=E3=80=82
=E9=A1=B9=E7=9B=AE=E4=B8=8E=E5=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E5=B7=A9=E5=
=9B=BA=E6=8F=90=E5=8D=87=E5=B7=A5=E7=A8=8B=E8=A7=84=E5=88=92=E4=BB=A5=E5=8F=
=8A=E7=BB=8F=E6=B5=8E=E7=A4=BE=E4=BC=9A=E5=8F=91=E5=B1=95=E8=A7=84=E5=88=92=
=E3=80=81=E5=8C=BA=E5=9F=9F=E8=A7=84=E5=88=92=E3=80=81=E5=9B=BD=E5=9C=9F=E7=
=A9=BA=E9=97=B4=E8=A7=84=E5=88=92=E7=AD=89=E8=A7=84=E5=88=92=E7=9A=84=E8=A1=
=94=E6=8E=A5=E6=80=A7=EF=BC=8C=E4=B8=8E=E6=9C=8D=E5=8A=A1=E4=B9=A1=E6=9D=91=
=E6=8C=AF=E5=85=B4=E3=80=81=E5=85=B1=E5=90=8C=E5=AF=8C=E8=A3=95=E3=80=81=E6=
=89=A9=E5=A4=A7=E5=86=85=E9=9C=80=E3=80=81=E7=A7=91=E6=8A=80=E5=88=9B=E6=96=
=B0=E3=80=81=E8=8A=82=E8=83=BD=E5=87=8F=E6=8E=92=E3=80=81=E7=A2=B3=E8=BE=BE=
=E5=B3=B0=E7=A2=B3=E4=B8=AD=E5=92=8C=E7=AD=89=E9=87=8D=E5=A4=A7=E6=94=BF=E7=
=AD=96=E7=9B=AE=E6=A0=87=E7=9A=84=E7=AC=A6=E5=90=88=E6=80=A7=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=
=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BD=B1=E5=93=8D=E5=88=86=E6=9E=
=90=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=
=E6=80=A7=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=80=89=E5=9D=80=E7=AB=A0=E8=
=8A=82=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E8=A7=84=E5=88=92=E8=A1=
=94=E6=8E=A5=E6=80=A7=EF=BC=8C=E9=9C=80=E5=8A=A0=E5=BC=BA=E6=94=BF=E7=AD=96=
=E8=A1=94=E6=8E=A5=E8=AF=B4=E6=98=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E8=83=8C=E6=99=AF=E6=98=8E=E7=A1=AE=E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=E6=
=80=A7</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E5=BD=B1=E5=93=8D=E5=88=86=E6=9E=90=
=E7=AB=A0=E8=8A=82=E6=8F=90=E5=8F=8A=E6=94=BF=E7=AD=96=E5=85=B3=E8=81=94=E6=
=80=A7</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E8=A7=84=E5=88=92=E8=A1=94=E6=
=8E=A5</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=80=89=E5=9D=80=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E8=A7=84=E5=88=92=E8=A1=94=E6=8E=A5=E6=80=A7=E8=
=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E6=94=BF=E7=AD=96=
=E7=AC=A6=E5=90=88=E6=80=A7=E7=9A=84=E4=B8=93=E9=A1=B9=E5=88=86=E6=9E=90</l=
i>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-4-14" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (3=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-4-14" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>2 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E8=83=8C=E6=99=AF=E5=92=8C=E5=BF=85=E8=A6=
=81=E6=80=A7</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=94=BF=E7=AD=96=E7=AC=A6=E5=90=88=E6=80=A7=E9=83=A8=E5=
=88=86=E6=98=8E=E7=A1=AE=E6=8F=90=E5=8F=8A=E4=B8=8E=E4=B9=A1=E6=9D=91=E6=8C=
=AF=E5=85=B4=E3=80=81=E7=A2=B3=E8=BE=BE=E5=B3=B0=E7=A2=B3=E4=B8=AD=E5=92=8C=
=E7=AD=89=E6=94=BF=E7=AD=96=E7=9B=AE=E6=A0=87=E7=9A=84=E7=AC=A6=E5=90=88=E6=
=80=A7=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E9=98=90=E8=BF=B0=E9=A1=B9=E7=9B=AE=E4=B8=8E=E5=
=86=9C=E6=9D=91=E7=94=B5=E7=BD=91=E8=A7=84=E5=88=92=E3=80=81=E4=B9=A1=E6=9D=
=91=E6=8C=AF=E5=85=B4=E7=AD=89=E6=94=BF=E7=AD=96=E7=9A=84=E8=A1=94=E6=8E=A5=
=E6=80=A7=E5=8F=8A=E7=AC=A6=E5=90=88=E6=80=A7=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>8 =E9=
=A1=B9=E7=9B=AE=E5=BD=B1=E5=93=8D=E6=95=88=E6=9E=9C=E5=88=86=E6=9E=90</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E5=86=85=E5=AE=B9=E6=8F=90=E5=8F=8A=E9=A1=B9=E7=9B=AE=E4=
=B8=8E=E4=B9=A1=E6=9D=91=E6=8C=AF=E5=85=B4=E3=80=81=E7=A2=B3=E8=BE=BE=E5=B3=
=B0=E7=A2=B3=E4=B8=AD=E5=92=8C=E7=AD=89=E6=94=BF=E7=AD=96=E7=9A=84=E5=85=B3=
=E8=81=94=E6=80=A7=EF=BC=8C=E7=AC=A6=E5=90=88=E6=94=BF=E7=AD=96=E7=AC=A6=E5=
=90=88=E6=80=A7=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 5.15</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E6=96=B9=E6=A1=88=E7=9A=84=E6=8A=80=E6=9C=AF=E8=B7=AF=E7=BA=BF=
=E6=98=AF=E5=90=A6=E5=90=88=E7=90=86=EF=BC=8C=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=
=A1=88=E6=98=AF=E5=90=A6=E8=BE=BE=E5=88=B0=E6=B7=B1=E5=BA=A6=E8=A6=81=E6=B1=
=82=EF=BC=8C=E4=BE=9D=E6=8D=AE=E7=9A=84=E6=8A=80=E6=9C=AF=E6=A0=87=E5=87=86=
=E6=98=AF=E5=90=A6=E5=90=88=E9=80=82=E3=80=82
=E4=BC=98=E5=85=88=E9=87=87=E7=94=A8=E5=85=B8=E5=9E=8B=E4=BE=9B=E7=94=B5=E6=
=A8=A1=E5=BC=8F=E3=80=81=E5=85=B8=E5=9E=8B=E8=AE=BE=E8=AE=A1=E5=92=8C=E9=80=
=9A=E7=94=A8=E9=80=A0=E4=BB=B7=EF=BC=8C=E6=8E=A8=E8=BF=9B=E5=86=9C=E6=9D=91=
=E7=94=B5=E7=BD=91=E8=A3=85=E5=A4=87=E6=A0=87=E5=87=86=E5=8C=96=E3=80=82=E5=
=AF=B9=E4=BA=8E=E7=89=B9=E6=AE=8A=E5=9C=B0=E6=AE=B5=E3=80=81=E8=87=AA=E7=84=
=B6=E7=81=BE=E5=AE=B3=E9=A2=91=E5=8F=91=E5=9C=B0=E5=8C=BA=E4=BB=A5=E5=8F=8A=
=E5=85=B7=E6=9C=89=E9=AB=98=E5=8D=B1=E5=92=8C=E9=87=8D=E8=A6=81=E7=94=A8=E6=
=88=B7=E7=9A=84=E7=BA=BF=E8=B7=AF=E3=80=81=E9=87=8D=E8=A6=81=E8=81=94=E7=BB=
=9C=E7=BA=BF=E8=B7=AF=E7=AD=89=EF=BC=8C=E5=8F=AF=E5=AE=9E=E8=A1=8C=E5=B7=AE=
=E5=BC=82=E5=8C=96=E8=AE=BE=E8=AE=A1=EF=BC=8C=E6=8F=90=E9=AB=98=E5=86=9C=E6=
=9D=91=E7=94=B5=E7=BD=91=E6=8A=B5=E5=BE=A1=E8=87=AA=E7=84=B6=E7=81=BE=E5=AE=
=B3=E7=9A=84=E8=83=BD=E5=8A=9B=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E9=87=87=E7=94=A8=E5=85=B8=E5=9E=8B=E4=BE=
=9B=E7=94=B5=E6=A8=A1=E5=BC=8F=EF=BC=8C=E7=AC=A6=E5=90=88=E6=8A=80=E6=9C=AF=
=E8=B7=AF=E7=BA=BF=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=9C=80=E8=A1=A5=E5=
=85=85=E5=B7=AE=E5=BC=82=E5=8C=96=E8=AE=BE=E8=AE=A1=E8=AF=B4=E6=98=8E=E3=80=
=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=
=E6=96=B9=E6=A1=88=E9=87=87=E7=94=A8=E5=85=B8=E5=9E=8B=E4=BE=9B=E7=94=B5=E6=
=A8=A1=E5=BC=8F</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E5=8F=8A=E7=89=B9=
=E6=AE=8A=E5=9C=B0=E6=AE=B5=E5=B7=AE=E5=BC=82=E5=8C=96=E8=AE=BE=E8=AE=A1</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E6=8A=80=
=E6=9C=AF=E6=A0=87=E5=87=86=E6=89=A7=E8=A1=8C=E6=B8=85=E5=8D=95</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E8=A1=A5=E5=85=85=E5=B7=AE=E5=BC=82=E5=
=8C=96=E8=AE=BE=E8=AE=A1=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E6=8A=80=E6=9C=AF=
=E6=A0=87=E5=87=86=E6=89=A7=E8=A1=8C=E6=B8=85=E5=8D=95</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-5-15" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (3=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-5-15" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E8=B7=AF=E5=BE=84=E9=80=89=E6=8B=A9=E7=AC=A6=E5=90=88=E5=
=85=B8=E5=9E=8B=E4=BE=9B=E7=94=B5=E6=A8=A1=E5=BC=8F=E8=A6=81=E6=B1=82=EF=BC=
=8C=E6=98=8E=E7=A1=AE=E9=81=BF=E5=BC=80=E6=95=8F=E6=84=9F=E5=8C=BA=E5=9F=9F=
=E5=B9=B6=E8=80=83=E8=99=91=E8=BF=90=E8=A1=8C=E7=BB=B4=E6=8A=A4=E4=BE=BF=E5=
=88=A9=E6=80=A7=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=87=87=E7=94=A8=E5=85=B8=E5=9E=8B=E4=BE=9B=E7=94=B5=E6=
=A8=A1=E5=BC=8F=EF=BC=88=E5=A6=82=E5=AF=BC=E7=BA=BF=E6=88=AA=E9=9D=A2=E9=80=
=89=E6=8B=A9=E8=A1=A83-1=EF=BC=89=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=995.15=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>8 =E9=
=A1=B9=E7=9B=AE=E5=BD=B1=E5=93=8D=E6=95=88=E6=9E=9C=E5=88=86=E6=9E=90</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E5=86=85=E5=AE=B9=E6=8F=90=E5=88=B0=E9=
=87=87=E7=94=A8=E5=85=B8=E5=9E=8B=E4=BE=9B=E7=94=B5=E6=A8=A1=E5=BC=8F=E3=80=
=81=E6=A0=87=E5=87=86=E5=8C=96=E8=AE=BE=E8=AE=A1=E5=8F=8A=E5=B7=AE=E5=BC=82=
=E5=8C=96=E8=AE=BE=E8=AE=A1=EF=BC=8C=E7=AC=A6=E5=90=88=E6=8A=80=E6=9C=AF=E8=
=B7=AF=E7=BA=BF=E5=90=88=E7=90=86=E6=80=A7=E8=A6=81=E6=B1=82=E3=80=82</smal=
l>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 5.16</h6>
                                        <p class=3D"mb-0 text-muted">=E6=98=
=AF=E5=90=A6=E5=AD=98=E5=9C=A8=E8=BF=9D=E5=8F=8D=E5=86=9C=E6=9D=91=E7=94=B5=
=E7=BD=91=E5=BB=BA=E8=AE=BE=E6=94=B9=E9=80=A0=E7=9B=B8=E5=85=B3=E6=8A=80=E6=
=9C=AF=E5=8E=9F=E5=88=99=E7=9A=84=E6=83=85=E5=86=B5=EF=BC=8C=E6=98=AF=E5=90=
=A6=E5=AD=98=E5=9C=A8=E8=B6=85=E6=A0=87=E5=87=86=E5=BB=BA=E8=AE=BE=E6=88=96=
=E6=94=B9=E9=80=A0=E4=B8=8D=E5=BD=BB=E5=BA=95=E7=9A=84=E9=97=AE=E9=A2=98=E3=
=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E6=9C=AA=E5=8F=91=E7=8E=B0=E8=B6=85=E6=A0=
=87=E5=87=86=E5=BB=BA=E8=AE=BE=E9=97=AE=E9=A2=98=EF=BC=8C=E7=AC=A6=E5=90=88=
=E6=8A=80=E6=9C=AF=E5=8E=9F=E5=88=99=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=
=9C=80=E5=8A=A0=E5=BC=BA=E6=A0=87=E5=87=86=E6=89=A7=E8=A1=8C=E8=AF=B4=E6=98=
=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=8F=91=E7=8E=B0=E8=BF=9D=
=E5=8F=8D=E6=8A=80=E6=9C=AF=E5=8E=9F=E5=88=99=E6=83=85=E5=86=B5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E5=8F=8A=E6=A0=87=
=E5=87=86=E6=89=A7=E8=A1=8C=E6=83=85=E5=86=B5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E6=A0=87=
=E5=87=86=E5=AF=B9=E6=AF=94=E5=88=86=E6=9E=90</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E8=A1=A5=E5=85=85=E6=A0=87=E5=87=86=E6=
=89=A7=E8=A1=8C=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E6=A0=87=E5=87=86=
=E5=AF=B9=E6=AF=94=E5=88=86=E6=9E=90=E5=86=85=E5=AE=B9</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-5-16" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (2=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-5-16" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E5=8F=91=E7=8E=B0=E8=BF=9D=E5=8F=8D=E6=8A=80=E6=
=9C=AF=E5=8E=9F=E5=88=99=E6=88=96=E8=B6=85=E6=A0=87=E5=87=86=E5=BB=BA=E8=AE=
=BE=E7=9A=84=E9=97=AE=E9=A2=98=E6=8F=8F=E8=BF=B0=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E5=8F=91=E7=8E=B0=E8=BF=9D=E5=8F=8D=E6=8A=80=E6=
=9C=AF=E5=8E=9F=E5=88=99=E6=88=96=E8=B6=85=E6=A0=87=E5=87=86=E5=BB=BA=E8=AE=
=BE=E5=86=85=E5=AE=B9=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=
=E5=88=995.16=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 5.17</h6>
                                        <p class=3D"mb-0 text-muted">=E6=98=
=AF=E5=90=A6=E5=AD=98=E5=9C=A8=E5=A4=A7=E6=8B=86=E5=A4=A7=E5=BB=BA=E7=9A=84=
=E6=83=85=E5=86=B5=EF=BC=8C=E6=98=AF=E5=90=A6=E5=BC=80=E5=B1=95=E7=9B=AE=E6=
=A0=87=E7=94=B5=E7=BD=91=E5=AE=89=E5=85=A8=E9=9A=90=E6=82=A3=E5=88=86=E6=9E=
=90=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=A1=B9=E7=9B=AE=E5=
=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88=E5=BC=80=E5=B1=95=E5=AE=89=E5=85=A8=E9=9A=
=90=E6=82=A3=E5=88=86=E6=9E=90=EF=BC=8C=E7=AC=A6=E5=90=88=E8=A6=81=E6=B1=82=
=EF=BC=8C=E4=BD=86=E9=9C=80=E7=A1=AE=E8=AE=A4=E6=98=AF=E5=90=A6=E5=8C=85=E5=
=90=AB=E5=A4=A7=E6=8B=86=E5=A4=A7=E5=BB=BA=E6=83=85=E5=86=B5=E8=AF=B4=E6=98=
=8E=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=98=8E=E7=A1=AE=E5=BC=80=E5=B1=95=
=E5=AE=89=E5=85=A8=E9=9A=90=E6=82=A3=E5=88=86=E6=9E=90</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E5=8F=8A=E5=A4=A7=
=E6=8B=86=E5=A4=A7=E5=BB=BA=E6=83=85=E5=86=B5</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E6=94=B9=
=E9=80=A0=E5=BD=BB=E5=BA=95=E6=80=A7=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A1=B9=E7=9B=AE=E5=BB=BA=
=E8=AE=BE=E6=96=B9=E6=A1=88=E4=B8=AD=E8=A1=A5=E5=85=85=E5=A4=A7=E6=8B=86=E5=
=A4=A7=E5=BB=BA=E6=83=85=E5=86=B5=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E6=94=B9=E9=80=A0=
=E5=BD=BB=E5=BA=95=E6=80=A7=E5=88=86=E6=9E=90=E5=86=85=E5=AE=B9</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-5-17" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (1=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-5-17" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>5 =E9=
=A1=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E6=96=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=9C=AA=E6=8F=90=E5=8F=8A=E5=A4=A7=E6=8B=86=E5=A4=A7=E5=
=BB=BA=E6=83=85=E5=86=B5=EF=BC=8C=E4=B8=94=E6=98=8E=E7=A1=AE=E5=BC=80=E5=B1=
=95=E7=9B=AE=E6=A0=87=E7=94=B5=E7=BD=91=E5=AE=89=E5=85=A8=E9=9A=90=E6=82=A3=
=E5=88=86=E6=9E=90=EF=BC=8C=E7=AC=A6=E5=90=88=E5=AE=A1=E6=9F=A5=E7=BB=86=E5=
=88=995.17=E8=A6=81=E6=B1=82=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 5.18</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E5=BB=BA=E8=AE=BE=E7=9A=84=E5=A4=96=E9=83=A8=E6=9D=A1=E4=BB=B6=
=E6=98=AF=E5=90=A6=E8=90=BD=E5=AE=9E=EF=BC=8C=E6=98=AF=E5=90=A6=E8=8E=B7=E5=
=8F=96=E7=9B=B8=E5=85=B3=E5=8D=8F=E8=AE=AE=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=
=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E9=80=89=E5=9D=80=E7=
=AB=A0=E8=8A=82=E6=98=8E=E7=A1=AE=E5=8F=96=E5=BE=97=E7=94=A8=E5=9C=B0=E5=8D=
=8F=E8=AE=AE=EF=BC=8C=E7=AC=A6=E5=90=88=E5=A4=96=E9=83=A8=E6=9D=A1=E4=BB=B6=
=E8=90=BD=E5=AE=9E=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E9=A3=8E=E9=99=A9=E7=
=AB=A0=E8=8A=82=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E5=8D=8F=E8=AE=
=AE=E8=90=BD=E5=AE=9E=E6=83=85=E5=86=B5=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=80=89=E5=9D=80=E7=AB=A0=E8=8A=82=
=E5=8F=96=E5=BE=97=E7=94=A8=E5=9C=B0=E5=8D=8F=E8=AE=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=A3=8E=E9=99=A9=E7=AB=A0=E8=8A=82=
=E6=9C=AA=E8=AF=A6=E7=BB=86=E8=AF=B4=E6=98=8E=E5=8D=8F=E8=AE=AE=E8=90=BD=E5=
=AE=9E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E5=85=B6=
=E4=BB=96=E5=8D=8F=E8=AE=AE=E8=8E=B7=E5=8F=96=E6=83=85=E5=86=B5</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E9=A3=8E=E9=99=A9=E7=AE=A1=
=E6=8E=A7=E7=AB=A0=E8=8A=82=E8=A1=A5=E5=85=85=E5=8D=8F=E8=AE=AE=E8=90=BD=E5=
=AE=9E=E6=83=85=E5=86=B5=E8=AF=B4=E6=98=8E</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E5=85=B6=E4=BB=96=
=E5=8D=8F=E8=AE=AE=E8=8E=B7=E5=8F=96=E6=83=85=E5=86=B5=E7=9A=84=E4=B8=93=E9=
=A1=B9=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-5-18" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (2=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-5-18" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>4 =E9=
=A1=B9=E7=9B=AE=E9=80=89=E5=9D=80=E4=B8=8E=E8=A6=81=E7=B4=A0=E4=BF=9D=E9=9A=
=9C</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=98=8E=E7=A1=AE=E8=AF=B4=E6=98=8E=E5=B7=B2=E5=8F=96=E5=
=BE=97=E7=9B=B8=E5=85=B3=E9=83=A8=E9=97=A8=E7=9A=84=E7=94=A8=E5=9C=B0=E5=8D=
=8F=E8=AE=AE=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88">=E5=9F=BA=
=E6=9C=AC=E7=AC=A6=E5=90=88</span>
                                                            <strong>9 =E9=
=A1=B9=E7=9B=AE=E9=A3=8E=E9=99=A9=E7=AE=A1=E6=8E=A7=E6=96=B9=E6=A1=88</stro=
ng>
                                                            <br><small clas=
s=3D"text-muted">=E7=AC=AC=E4=B9=9D=E7=AB=A0=E6=8F=90=E5=88=B0=E5=BE=81=E5=
=9C=B0=E5=8F=AF=E8=83=BD=E5=BC=95=E5=8F=91=E7=9A=84=E7=A4=BE=E4=BC=9A=E9=A3=
=8E=E9=99=A9=EF=BC=8C=E4=BD=86=E6=9C=AA=E6=98=8E=E7=A1=AE=E8=AF=B4=E6=98=8E=
=E5=A4=96=E9=83=A8=E6=9D=A1=E4=BB=B6=EF=BC=88=E5=A6=82=E5=BE=81=E5=9C=B0=E5=
=8D=8F=E8=AE=AE=EF=BC=89=E6=98=AF=E5=90=A6=E5=B7=B2=E8=90=BD=E5=AE=9E=E3=80=
=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 5.19</h6>
                                        <p class=3D"mb-0 text-muted">=E5=B7=
=A5=E7=A8=8B=E8=A7=84=E6=A8=A1=E4=B8=80=E8=87=B4=E6=80=A7=EF=BC=9A
=E5=BB=BA=E8=AE=BE=E8=A7=84=E6=A8=A1=E4=B8=8E=E5=8F=AF=E7=A0=94=E6=89=B9=E5=
=A4=8D=E6=96=87=E4=BB=B6=E6=98=AF=E5=90=A6=E4=B8=80=E8=87=B4=EF=BC=88=E9=9C=
=80=E6=8F=90=E4=BE=9B=E9=A1=B9=E7=9B=AE=E5=8F=AF=E7=A0=94=E6=89=B9=E5=A4=8D=
=E6=96=87=E4=BB=B6=EF=BC=89=E3=80=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E6=9C=AA=E6=8F=90=E4=
=BE=9B=E5=8F=AF=E7=A0=94=E6=89=B9=E5=A4=8D=E6=96=87=E4=BB=B6=EF=BC=8C=E6=97=
=A0=E6=B3=95=E9=AA=8C=E8=AF=81=E5=BB=BA=E8=AE=BE=E8=A7=84=E6=A8=A1=E4=B8=80=
=E8=87=B4=E6=80=A7=EF=BC=8C=E9=9C=80=E8=A1=A5=E5=85=85=E7=9B=B8=E5=85=B3=E6=
=96=87=E4=BB=B6=E8=B5=84=E6=96=99=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E5=8F=AF=
=E7=A0=94=E6=89=B9=E5=A4=8D=E6=96=87=E4=BB=B6</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E8=BF=9B=E8=A1=8C=E5=BB=BA=
=E8=AE=BE=E8=A7=84=E6=A8=A1=E4=B8=80=E8=87=B4=E6=80=A7=E5=88=86=E6=9E=90</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=BC=95=E7=94=A8=E7=9B=B8=
=E5=85=B3=E6=89=B9=E5=A4=8D=E6=96=87=E4=BB=B6</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E8=A1=A5=E5=85=85=E9=A1=B9=E7=9B=AE=
=E5=8F=AF=E7=A0=94=E6=89=B9=E5=A4=8D=E6=96=87=E4=BB=B6</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=A6=82=E8=BF=B0=E7=AB=A0=
=E8=8A=82=E5=A2=9E=E5=8A=A0=E5=BB=BA=E8=AE=BE=E8=A7=84=E6=A8=A1=E4=B8=80=E8=
=87=B4=E6=80=A7=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 6.20</h6>
                                        <p class=3D"mb-0 text-muted">=E9=A1=
=B9=E7=9B=AE=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E3=80=81=E8=9E=8D=E8=B5=84=
=E6=96=B9=E6=A1=88=E7=AD=89=E6=98=AF=E5=90=A6=E5=90=88=E7=90=86=EF=BC=8C=E6=
=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E7=BC=96=E5=88=B6=E6=98=AF=E5=90=
=A6=E8=A7=84=E8=8C=83=E3=80=82
=E6=89=A7=E8=A1=8C=E7=9A=84=E5=AE=9A=E9=A2=9D=E3=80=81=E6=8C=87=E6=A0=87=E4=
=BB=A5=E5=8F=8A=E4=B8=BB=E8=A6=81=E8=AE=BE=E5=A4=87=E3=80=81=E6=9D=90=E6=96=
=99=E4=BB=B7=E6=A0=BC=E6=89=A7=E8=A1=8C=E6=96=87=E4=BB=B6=EF=BC=8C=E5=BB=BA=
=E8=AE=BE=E5=9C=BA=E5=9C=B0=E5=BE=81=E7=94=A8=E5=8F=8A=E6=B8=85=E7=90=86=E8=
=B4=B9=E7=94=A8=E8=AE=A1=E7=AE=97=E4=BE=9D=E6=8D=AE=E6=88=96=E7=9B=B8=E5=85=
=B3=E6=A0=87=E5=87=86=EF=BC=8C=E4=BE=9D=E6=8D=AE=E7=9A=84=E6=8A=80=E6=9C=AF=
=E7=BB=8F=E6=B5=8E=E6=96=87=E4=BB=B6=E5=92=8C=E5=90=84=E9=A1=B9=E8=B4=B9=E7=
=94=A8=E8=AE=A1=E7=AE=97=E7=9A=84=E6=89=A7=E8=A1=8C=E6=96=87=E4=BB=B6=EF=BC=
=8C=E5=8F=82=E7=85=A7=E6=89=A7=E8=A1=8C=E7=9A=84=E5=86=9C=E6=9D=91=E7=94=B5=
=E7=BD=91=E5=BB=BA=E8=AE=BE=E7=9B=B8=E5=85=B3=E8=B4=A2=E5=8A=A1=E6=94=BF=E7=
=AD=96=E7=AD=89=E6=98=AF=E5=90=A6=E7=AC=A6=E5=90=88=E8=A7=84=E5=AE=9A=E3=80=
=82</p>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E6=8A=95=E8=9E=8D=E8=
=B5=84=E7=AB=A0=E8=8A=82=E7=AC=A6=E5=90=88=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=
=97=E8=A7=84=E8=8C=83=E6=80=A7=E8=A6=81=E6=B1=82=EF=BC=8C=E4=BD=86=E7=BB=93=
=E8=AE=BA=E5=92=8C=E9=99=84=E8=A1=A8=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E4=
=BE=9B=E5=AE=8C=E6=95=B4=E4=BC=B0=E7=AE=97=E4=B9=A6=EF=BC=8C=E9=9C=80=E5=8A=
=A0=E5=BC=BA=E8=B5=84=E6=96=99=E5=AE=8C=E6=95=B4=E6=80=A7=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=8A=95=E8=9E=8D=E8=B5=84=E7=AB=A0=
=E8=8A=82=E8=AF=B4=E6=98=8E=E4=BC=B0=E7=AE=97=E4=BE=9D=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E7=BB=93=E8=AE=BA=E7=AB=A0=E8=8A=82=
=E7=BC=BA=E5=A4=B1=E4=BC=B0=E7=AE=97=E4=B9=A6</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E9=99=84=E8=A1=A8=E6=9C=AA=E6=8F=90=
=E4=BE=9B=E5=AE=8C=E6=95=B4=E4=BC=B0=E7=AE=97=E4=B9=A6</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E7=BB=93=E8=AE=BA=E7=AB=A0=
=E8=8A=82=E8=A1=A5=E5=85=85=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6</l=
i>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=AE=8C=E5=96=84=E9=99=84=E8=A1=A8=
=E7=9A=84=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=E5=86=85=E5=AE=B9</l=
i>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-6-20" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (3=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-6-20" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E7=AC=A6=E5=90=88">=E7=AC=A6=E5=90=88</span>
                                                            <strong>7 =E9=
=A1=B9=E7=9B=AE=E6=8A=95=E8=9E=8D=E8=B5=84=E4=B8=8E=E8=B4=A2=E5=8A=A1=E6=96=
=B9=E6=A1=88</strong>
                                                            <br><small clas=
s=3D"text-muted">=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=BE=9D=E6=8D=AE=E6=
=98=8E=E7=A1=AE=EF=BC=8C=E5=AE=9A=E9=A2=9D=E3=80=81=E4=BB=B7=E6=A0=BC=E6=89=
=A7=E8=A1=8C=E6=96=87=E4=BB=B6=E7=AC=A6=E5=90=88=E8=A7=84=E5=AE=9A=E3=80=82=
</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>10 =E7=
=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=8F=8A=E5=BB=BA=E8=AE=AE</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E4=BE=9B=E6=8A=95=E8=
=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=EF=BC=8C=E6=9C=AA=E8=AF=B4=E6=98=8E=E4=BC=
=B0=E7=AE=97=E4=BE=9D=E6=8D=AE=E5=8F=8A=E6=8A=80=E7=BB=8F=E8=AF=84=E4=BB=B7=
=E5=86=85=E5=AE=B9=E3=80=82</small>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>11 =E9=
=99=84=E8=A1=A8</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=99=84=E8=A1=A8=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=AE=8C=E6=
=95=B4=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B9=A6=EF=BC=8C=E6=97=A0=E6=B3=
=95=E9=AA=8C=E8=AF=81=E4=BC=B0=E7=AE=97=E7=BC=96=E5=88=B6=E8=A7=84=E8=8C=83=
=E6=80=A7=E3=80=82</small>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div><div class=3D"criterion-card">
                            <div class=3D"criterion-header">
                                <div class=3D"d-flex justify-content-betwee=
n align-items-start">
                                    <div>
                                        <h6 class=3D"mb-1">=E5=AE=A1=E6=9F=
=A5=E7=BB=86=E5=88=99 6.21</h6>
                                        <p class=3D"mb-0 text-muted">=E5=8D=
=95=E4=BD=8D=E5=AE=B9=E9=87=8F=E3=80=81=E5=8D=95=E4=BD=8D=E5=85=AC=E9=87=8C=
=E9=80=A0=E4=BB=B7=E6=98=AF=E5=90=A6=E5=90=88=E7=90=86=E3=80=82
=E5=AF=B9=E6=AF=94=E8=83=BD=E6=BA=90=E5=B1=80=E4=BC=B0=E7=AE=97=E5=8D=95=E4=
=BB=B7=E8=A1=A8=EF=BC=8C=E5=8D=95=E4=BD=8D=E6=8A=95=E8=B5=84=E8=B6=8530%=E5=
=8F=8A=E4=BB=A5=E4=B8=8A=E7=9A=84=E9=A1=B9=E7=9B=AE=E9=9C=80=E5=9C=A8=E5=8F=
=AF=E7=A0=94=E6=8A=A5=E5=91=8A=E4=B8=AD=E5=A2=9E=E5=8A=A0=E6=8A=95=E8=B5=84=
=E5=81=8F=E9=AB=98=E7=9A=84=E5=8E=9F=E5=9B=A0=E5=88=86=E6=9E=90=E3=80=82</p=
>
                                    </div>
                                    <span class=3D"badge result-badge resul=
t-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=E5=90=88</span>
                                </div>
                            </div>
                            <div class=3D"criterion-content">
                               =20
                                <div class=3D"alert alert-info mb-3">
                                    <h6><i class=3D"fas fa-chart-line"></i>=
 =E5=85=A8=E6=96=87=E7=BB=BC=E5=90=88=E5=88=86=E6=9E=90=EF=BC=9A</h6>
                                    <p class=3D"mb-0">=E6=9C=AA=E6=8F=90=E4=
=BE=9B=E5=8D=95=E4=BD=8D=E9=80=A0=E4=BB=B7=E6=95=B0=E6=8D=AE=E5=8F=8A=E5=AF=
=B9=E6=AF=94=E5=88=86=E6=9E=90=EF=BC=8C=E6=97=A0=E6=B3=95=E9=AA=8C=E8=AF=81=
=E9=80=A0=E4=BB=B7=E5=90=88=E7=90=86=E6=80=A7=EF=BC=8C=E9=9C=80=E8=A1=A5=E5=
=85=85=E6=8A=80=E7=BB=8F=E8=AF=84=E4=BB=B7=E5=86=85=E5=AE=B9=E3=80=82</p>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-search"></i>=
 =E5=85=B3=E9=94=AE=E5=8F=91=E7=8E=B0=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E6=8F=90=E4=BE=9B=E5=8D=95=
=E4=BD=8D=E5=AE=B9=E9=87=8F/=E5=85=AC=E9=87=8C=E9=80=A0=E4=BB=B7=E6=95=B0=
=E6=8D=AE</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=AF=B9=E6=AF=94=E8=83=BD=
=E6=BA=90=E5=B1=80=E4=BC=B0=E7=AE=97=E5=8D=95=E4=BB=B7</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-dot-circle text-primary"></i> =E6=9C=AA=E5=88=86=E6=9E=90=E8=B6=8530=
%=E6=8A=95=E8=B5=84=E5=8E=9F=E5=9B=A0</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                <div class=3D"mb-3">
                                    <strong><i class=3D"fas fa-lightbulb"><=
/i> =E6=94=B9=E8=BF=9B=E5=BB=BA=E8=AE=AE=EF=BC=9A</strong>
                                    <ul class=3D"list-unstyled mt-2">
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=9C=A8=E6=8A=95=E8=9E=8D=E8=B5=84=
=E7=AB=A0=E8=8A=82=E8=A1=A5=E5=85=85=E5=8D=95=E4=BD=8D=E9=80=A0=E4=BB=B7=E5=
=88=86=E6=9E=90</li>
                                       =20
                                            <li class=3D"mb-1"><i class=3D"=
fas fa-arrow-right text-success"></i> =E5=A2=9E=E5=8A=A0=E8=B6=8530%=E6=8A=
=95=E8=B5=84=E5=8E=9F=E5=9B=A0=E4=B8=93=E9=A1=B9=E8=AF=B4=E6=98=8E</li>
                                       =20
                                    </ul>
                                </div>
                           =20
                               =20
                                    <div class=3D"mt-3">
                                        <p>
                                            <a class=3D"btn btn-outline-sec=
ondary btn-sm" data-bs-toggle=3D"collapse" href=3D"http://127.0.0.1:8000/#c=
ollapse-6-21" role=3D"button" aria-expanded=3D"true">
                                                <i class=3D"fas fa-list"></=
i> =E6=9F=A5=E7=9C=8B=E5=90=84=E7=AB=A0=E8=8A=82=E8=AF=A6=E7=BB=86=E8=AF=84=
=E5=AE=A1=E6=83=85=E5=86=B5 (2=E4=B8=AA=E7=9B=B8=E5=85=B3=E7=AB=A0=E8=8A=82=
)
                                            </a>
                                        </p>
                                        <div class=3D"collapse show" id=3D"=
collapse-6-21" style=3D"">
                                            <div class=3D"card card-body">
                                                <ul class=3D"list-unstyled"=
>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>10 =E7=
=A0=94=E7=A9=B6=E7=BB=93=E8=AE=BA=E5=8F=8A=E5=BB=BA=E8=AE=AE</strong>
                                                            <br><small clas=
s=3D"text-muted">=E7=AB=A0=E8=8A=82=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=8D=95=E4=
=BD=8D=E5=AE=B9=E9=87=8F=E6=88=96=E5=8D=95=E4=BD=8D=E5=85=AC=E9=87=8C=E9=80=
=A0=E4=BB=B7=E6=95=B0=E6=8D=AE=EF=BC=8C=E6=9C=AA=E5=AF=B9=E6=AF=94=E8=83=BD=
=E6=BA=90=E5=B1=80=E4=BC=B0=E7=AE=97=E5=8D=95=E4=BB=B7=E8=A1=A8=E3=80=82</s=
mall>
                                                        </li>
                                                   =20
                                                        <li class=3D"mb-2">
                                                            <span class=3D"=
badge result-badge result-=E4=B8=8D=E7=AC=A6=E5=90=88">=E4=B8=8D=E7=AC=A6=
=E5=90=88</span>
                                                            <strong>11 =E9=
=99=84=E8=A1=A8</strong>
                                                            <br><small clas=
s=3D"text-muted">=E9=99=84=E8=A1=A8=E6=9C=AA=E6=8F=90=E4=BE=9B=E5=8D=95=E4=
=BD=8D=E9=80=A0=E4=BB=B7=E5=88=86=E6=9E=90=E6=95=B0=E6=8D=AE=EF=BC=8C=E6=97=
=A0=E6=B3=95=E5=88=A4=E6=96=AD=E6=98=AF=E5=90=A6=E8=B6=8530%=E3=80=82</smal=
l>
                                                        </li>
                                                   =20
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                               =20
                            </div>
                        </div></div>

                    <div class=3D"mt-4">
                        <h4>=E6=80=BB=E4=BD=93=E8=AF=84=E5=AE=A1=E6=84=8F=
=E8=A7=81</h4>
                        <div id=3D"summary" class=3D"p-3 bg-light rounded">=
=E8=AF=A5=E9=A1=B9=E7=9B=AE=E6=80=BB=E4=BD=93=E7=AC=A6=E5=90=88=E5=86=9C=E6=
=9D=91=E7=94=B5=E7=BD=91=E5=B7=A9=E5=9B=BA=E6=8F=90=E5=8D=87=E5=B7=A5=E7=A8=
=8B=E5=8F=AF=E7=A0=94=E6=8A=A5=E5=91=8A=E7=BC=96=E5=88=B6=E8=A6=81=E6=B1=82=
=EF=BC=8C=E4=BD=86=E5=AD=98=E5=9C=A8=E8=B5=84=E8=B4=A8=E4=BF=A1=E6=81=AF=E4=
=B8=8D=E5=AE=8C=E6=95=B4=E3=80=81=E6=8A=95=E8=B5=84=E4=BC=B0=E7=AE=97=E4=B8=
=8D=E8=A7=84=E8=8C=83=E3=80=81=E9=99=84=E8=A1=A8=E5=86=85=E5=AE=B9=E7=BC=BA=
=E5=A4=B1=E7=AD=89=E7=AA=81=E5=87=BA=E9=97=AE=E9=A2=98=E3=80=82=E9=9C=80=E9=
=87=8D=E7=82=B9=E8=A1=A5=E5=85=85=E7=BC=96=E5=88=B6=E5=8D=95=E4=BD=8D=E8=B5=
=84=E8=B4=A8=E8=AF=B4=E6=98=8E=E3=80=81=E6=8A=95=E8=B5=84=E6=AF=94=E4=BE=8B=
=E5=88=86=E6=9E=90=E3=80=81=E5=8F=AF=E7=A0=94=E6=89=B9=E5=A4=8D=E6=96=87=E4=
=BB=B6=E5=8F=8A=E9=80=A0=E4=BB=B7=E5=AF=B9=E6=AF=94=E6=95=B0=E6=8D=AE=EF=BC=
=8C=E5=AE=8C=E5=96=84=E9=99=84=E8=A1=A8=E5=86=85=E5=AE=B9=E5=92=8C=E5=80=BA=
=E5=8A=A1=E6=B8=85=E5=81=BF=E5=88=86=E6=9E=90=EF=BC=8C=E4=BB=A5=E6=8F=90=E5=
=8D=87=E6=8A=A5=E5=91=8A=E5=90=88=E8=A7=84=E6=80=A7=E5=92=8C=E6=8A=80=E6=9C=
=AF=E7=BB=8F=E6=B5=8E=E5=90=88=E7=90=86=E6=80=A7=E3=80=82</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

   =20

    <!-- Bootstrap JavaScript -->
   =20

</body></html>
------MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: http://127.0.0.1:8000/static/all.min.css

@charset "utf-8";

.fa { font-family: var(--fa-style-family,"Font Awesome 6 Free"); font-weigh=
t: var(--fa-style,900); }

.fa, .fa-brands, .fa-duotone, .fa-light, .fa-regular, .fa-solid, .fa-thin, =
.fab, .fad, .fal, .far, .fas, .fat { -webkit-font-smoothing: antialiased; d=
isplay: var(--fa-display,inline-block); font-style: normal; font-variant: n=
ormal; line-height: 1; text-rendering: auto; }

.fa-1x { font-size: 1em; }

.fa-2x { font-size: 2em; }

.fa-3x { font-size: 3em; }

.fa-4x { font-size: 4em; }

.fa-5x { font-size: 5em; }

.fa-6x { font-size: 6em; }

.fa-7x { font-size: 7em; }

.fa-8x { font-size: 8em; }

.fa-9x { font-size: 9em; }

.fa-10x { font-size: 10em; }

.fa-2xs { font-size: 0.625em; line-height: 0.1em; vertical-align: 0.225em; =
}

.fa-xs { font-size: 0.75em; line-height: 0.08333em; vertical-align: 0.125em=
; }

.fa-sm { font-size: 0.875em; line-height: 0.07143em; vertical-align: 0.0535=
7em; }

.fa-lg { font-size: 1.25em; line-height: 0.05em; vertical-align: -0.075em; =
}

.fa-xl { font-size: 1.5em; line-height: 0.04167em; vertical-align: -0.125em=
; }

.fa-2xl { font-size: 2em; line-height: 0.03125em; vertical-align: -0.1875em=
; }

.fa-fw { text-align: center; width: 1.25em; }

.fa-ul { list-style-type: none; margin-left: var(--fa-li-margin,2.5em); pad=
ding-left: 0px; }

.fa-ul > li { position: relative; }

.fa-li { left: calc(var(--fa-li-width, 2em)*-1); position: absolute; text-a=
lign: center; width: var(--fa-li-width,2em); line-height: inherit; }

.fa-border { border-radius: var(--fa-border-radius,.1em); border: var(--fa-=
border-width,.08em) var(--fa-border-style,solid) var(--fa-border-color,#eee=
); padding: var(--fa-border-padding,.2em .25em .15em); }

.fa-pull-left { float: left; margin-right: var(--fa-pull-margin,.3em); }

.fa-pull-right { float: right; margin-left: var(--fa-pull-margin,.3em); }

.fa-beat { animation-name: fa-beat; animation-delay: var(--fa-animation-del=
ay,0); animation-direction: var(--fa-animation-direction,normal); animation=
-duration: var(--fa-animation-duration,1s); animation-iteration-count: var(=
--fa-animation-iteration-count,infinite); animation-timing-function: var(--=
fa-animation-timing,ease-in-out); }

.fa-bounce { animation-name: fa-bounce; animation-delay: var(--fa-animation=
-delay,0); animation-direction: var(--fa-animation-direction,normal); anima=
tion-duration: var(--fa-animation-duration,1s); animation-iteration-count: =
var(--fa-animation-iteration-count,infinite); animation-timing-function: va=
r(--fa-animation-timing,cubic-bezier(.28,.84,.42,1)); }

.fa-fade { animation-name: fa-fade; animation-iteration-count: var(--fa-ani=
mation-iteration-count,infinite); animation-timing-function: var(--fa-anima=
tion-timing,cubic-bezier(.4,0,.6,1)); }

.fa-beat-fade, .fa-fade { animation-delay: var(--fa-animation-delay,0); ani=
mation-direction: var(--fa-animation-direction,normal); animation-duration:=
 var(--fa-animation-duration,1s); }

.fa-beat-fade { animation-name: fa-beat-fade; animation-iteration-count: va=
r(--fa-animation-iteration-count,infinite); animation-timing-function: var(=
--fa-animation-timing,cubic-bezier(.4,0,.6,1)); }

.fa-flip { animation-name: fa-flip; animation-delay: var(--fa-animation-del=
ay,0); animation-direction: var(--fa-animation-direction,normal); animation=
-duration: var(--fa-animation-duration,1s); animation-iteration-count: var(=
--fa-animation-iteration-count,infinite); animation-timing-function: var(--=
fa-animation-timing,ease-in-out); }

.fa-shake { animation-name: fa-shake; animation-duration: var(--fa-animatio=
n-duration,1s); animation-iteration-count: var(--fa-animation-iteration-cou=
nt,infinite); animation-timing-function: var(--fa-animation-timing,linear);=
 }

.fa-shake, .fa-spin { animation-delay: var(--fa-animation-delay,0); animati=
on-direction: var(--fa-animation-direction,normal); }

.fa-spin { animation-name: fa-spin; animation-duration: var(--fa-animation-=
duration,2s); animation-iteration-count: var(--fa-animation-iteration-count=
,infinite); animation-timing-function: var(--fa-animation-timing,linear); }

.fa-spin-reverse { --fa-animation-direction: reverse; }

.fa-pulse, .fa-spin-pulse { animation-name: fa-spin; animation-direction: v=
ar(--fa-animation-direction,normal); animation-duration: var(--fa-animation=
-duration,1s); animation-iteration-count: var(--fa-animation-iteration-coun=
t,infinite); animation-timing-function: var(--fa-animation-timing,steps(8))=
; }

@media (prefers-reduced-motion: reduce) {
  .fa-beat, .fa-beat-fade, .fa-bounce, .fa-fade, .fa-flip, .fa-pulse, .fa-s=
hake, .fa-spin, .fa-spin-pulse { animation-delay: -1ms; animation-duration:=
 1ms; animation-iteration-count: 1; transition-delay: 0s; transition-durati=
on: 0s; }
}

@-webkit-keyframes fa-beat {=20
  0%, 90% { transform: scale(1); }
  45% { transform: scale(var(--fa-beat-scale,1.25)); }
}

@keyframes fa-beat {=20
  0%, 90% { transform: scale(1); }
  45% { transform: scale(var(--fa-beat-scale,1.25)); }
}

@-webkit-keyframes fa-bounce {=20
  0% { transform: scale(1) translateY(0px); }
  10% { transform: scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce=
-start-scale-y,.9)) translateY(0); }
  30% { transform: scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-j=
ump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em)); }
  50% { transform: scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce=
-land-scale-y,.95)) translateY(0); }
  57% { transform: scale(1) translateY(var(--fa-bounce-rebound,-.125em)); }
  64% { transform: scale(1) translateY(0px); }
  100% { transform: scale(1) translateY(0px); }
}

@keyframes fa-bounce {=20
  0% { transform: scale(1) translateY(0px); }
  10% { transform: scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce=
-start-scale-y,.9)) translateY(0); }
  30% { transform: scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-j=
ump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em)); }
  50% { transform: scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce=
-land-scale-y,.95)) translateY(0); }
  57% { transform: scale(1) translateY(var(--fa-bounce-rebound,-.125em)); }
  64% { transform: scale(1) translateY(0px); }
  100% { transform: scale(1) translateY(0px); }
}

@-webkit-keyframes fa-fade {=20
  50% { opacity: var(--fa-fade-opacity,.4); }
}

@keyframes fa-fade {=20
  50% { opacity: var(--fa-fade-opacity,.4); }
}

@-webkit-keyframes fa-beat-fade {=20
  0%, 100% { opacity: var(--fa-beat-fade-opacity,.4); transform: scale(1); =
}
  50% { opacity: 1; transform: scale(var(--fa-beat-fade-scale,1.125)); }
}

@keyframes fa-beat-fade {=20
  0%, 100% { opacity: var(--fa-beat-fade-opacity,.4); transform: scale(1); =
}
  50% { opacity: 1; transform: scale(var(--fa-beat-fade-scale,1.125)); }
}

@-webkit-keyframes fa-flip {=20
  50% { transform: rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-=
flip-z,0),var(--fa-flip-angle,-180deg)); }
}

@keyframes fa-flip {=20
  50% { transform: rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-=
flip-z,0),var(--fa-flip-angle,-180deg)); }
}

@-webkit-keyframes fa-shake {=20
  0% { transform: rotate(-15deg); }
  4% { transform: rotate(15deg); }
  8%, 24% { transform: rotate(-18deg); }
  12%, 28% { transform: rotate(18deg); }
  16% { transform: rotate(-22deg); }
  20% { transform: rotate(22deg); }
  32% { transform: rotate(-12deg); }
  36% { transform: rotate(12deg); }
  40%, 100% { transform: rotate(0deg); }
}

@keyframes fa-shake {=20
  0% { transform: rotate(-15deg); }
  4% { transform: rotate(15deg); }
  8%, 24% { transform: rotate(-18deg); }
  12%, 28% { transform: rotate(18deg); }
  16% { transform: rotate(-22deg); }
  20% { transform: rotate(22deg); }
  32% { transform: rotate(-12deg); }
  36% { transform: rotate(12deg); }
  40%, 100% { transform: rotate(0deg); }
}

@-webkit-keyframes fa-spin {=20
  0% { transform: rotate(0deg); }
  100% { transform: rotate(1turn); }
}

@keyframes fa-spin {=20
  0% { transform: rotate(0deg); }
  100% { transform: rotate(1turn); }
}

.fa-rotate-90 { transform: rotate(90deg); }

.fa-rotate-180 { transform: rotate(180deg); }

.fa-rotate-270 { transform: rotate(270deg); }

.fa-flip-horizontal { transform: scaleX(-1); }

.fa-flip-vertical { transform: scaleY(-1); }

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical { transform: scale(-1);=
 }

.fa-rotate-by { transform: rotate(var(--fa-rotate-angle,none)); }

.fa-stack { display: inline-block; height: 2em; line-height: 2em; position:=
 relative; vertical-align: middle; width: 2.5em; }

.fa-stack-1x, .fa-stack-2x { left: 0px; position: absolute; text-align: cen=
ter; width: 100%; z-index: var(--fa-stack-z-index,auto); }

.fa-stack-1x { line-height: inherit; }

.fa-stack-2x { font-size: 2em; }

.fa-inverse { color: var(--fa-inverse,#fff); }

.fa-0::before { content: "0"; }

.fa-1::before { content: "1"; }

.fa-2::before { content: "2"; }

.fa-3::before { content: "3"; }

.fa-4::before { content: "4"; }

.fa-5::before { content: "5"; }

.fa-6::before { content: "6"; }

.fa-7::before { content: "7"; }

.fa-8::before { content: "8"; }

.fa-9::before { content: "9"; }

.fa-a::before { content: "A"; }

.fa-address-book::before, .fa-contact-book::before { content: "=EF=8A=B9"; =
}

.fa-address-card::before, .fa-contact-card::before, .fa-vcard::before { con=
tent: "=EF=8A=BB"; }

.fa-align-center::before { content: "=EF=80=B7"; }

.fa-align-justify::before { content: "=EF=80=B9"; }

.fa-align-left::before { content: "=EF=80=B6"; }

.fa-align-right::before { content: "=EF=80=B8"; }

.fa-anchor::before { content: "=EF=84=BD"; }

.fa-angle-down::before { content: "=EF=84=87"; }

.fa-angle-left::before { content: "=EF=84=84"; }

.fa-angle-right::before { content: "=EF=84=85"; }

.fa-angle-up::before { content: "=EF=84=86"; }

.fa-angle-double-down::before, .fa-angles-down::before { content: "=EF=84=
=83"; }

.fa-angle-double-left::before, .fa-angles-left::before { content: "=EF=84=
=80"; }

.fa-angle-double-right::before, .fa-angles-right::before { content: "=EF=84=
=81"; }

.fa-angle-double-up::before, .fa-angles-up::before { content: "=EF=84=82"; =
}

.fa-ankh::before { content: "=EF=99=84"; }

.fa-apple-alt::before, .fa-apple-whole::before { content: "=EF=97=91"; }

.fa-archway::before { content: "=EF=95=97"; }

.fa-arrow-down::before { content: "=EF=81=A3"; }

.fa-arrow-down-1-9::before, .fa-sort-numeric-asc::before, .fa-sort-numeric-=
down::before { content: "=EF=85=A2"; }

.fa-arrow-down-9-1::before, .fa-sort-numeric-desc::before, .fa-sort-numeric=
-down-alt::before { content: "=EF=A2=86"; }

.fa-arrow-down-a-z::before, .fa-sort-alpha-asc::before, .fa-sort-alpha-down=
::before { content: "=EF=85=9D"; }

.fa-arrow-down-long::before, .fa-long-arrow-down::before { content: "=EF=85=
=B5"; }

.fa-arrow-down-short-wide::before, .fa-sort-amount-desc::before, .fa-sort-a=
mount-down-alt::before { content: "=EF=A2=84"; }

.fa-arrow-down-wide-short::before, .fa-sort-amount-asc::before, .fa-sort-am=
ount-down::before { content: "=EF=85=A0"; }

.fa-arrow-down-z-a::before, .fa-sort-alpha-desc::before, .fa-sort-alpha-dow=
n-alt::before { content: "=EF=A2=81"; }

.fa-arrow-left::before { content: "=EF=81=A0"; }

.fa-arrow-left-long::before, .fa-long-arrow-left::before { content: "=EF=85=
=B7"; }

.fa-arrow-pointer::before, .fa-mouse-pointer::before { content: "=EF=89=85"=
; }

.fa-arrow-right::before { content: "=EF=81=A1"; }

.fa-arrow-right-arrow-left::before, .fa-exchange::before { content: "=EF=83=
=AC"; }

.fa-arrow-right-from-bracket::before, .fa-sign-out::before { content: "=EF=
=82=8B"; }

.fa-arrow-right-long::before, .fa-long-arrow-right::before { content: "=EF=
=85=B8"; }

.fa-arrow-right-to-bracket::before, .fa-sign-in::before { content: "=EF=82=
=90"; }

.fa-arrow-left-rotate::before, .fa-arrow-rotate-back::before, .fa-arrow-rot=
ate-backward::before, .fa-arrow-rotate-left::before, .fa-undo::before { con=
tent: "=EF=83=A2"; }

.fa-arrow-right-rotate::before, .fa-arrow-rotate-forward::before, .fa-arrow=
-rotate-right::before, .fa-redo::before { content: "=EF=80=9E"; }

.fa-arrow-trend-down::before { content: "=EE=82=97"; }

.fa-arrow-trend-up::before { content: "=EE=82=98"; }

.fa-arrow-turn-down::before, .fa-level-down::before { content: "=EF=85=89";=
 }

.fa-arrow-turn-up::before, .fa-level-up::before { content: "=EF=85=88"; }

.fa-arrow-up::before { content: "=EF=81=A2"; }

.fa-arrow-up-1-9::before, .fa-sort-numeric-up::before { content: "=EF=85=A3=
"; }

.fa-arrow-up-9-1::before, .fa-sort-numeric-up-alt::before { content: "=EF=
=A2=87"; }

.fa-arrow-up-a-z::before, .fa-sort-alpha-up::before { content: "=EF=85=9E";=
 }

.fa-arrow-up-from-bracket::before { content: "=EE=82=9A"; }

.fa-arrow-up-long::before, .fa-long-arrow-up::before { content: "=EF=85=B6"=
; }

.fa-arrow-up-right-from-square::before, .fa-external-link::before { content=
: "=EF=82=8E"; }

.fa-arrow-up-short-wide::before, .fa-sort-amount-up-alt::before { content: =
"=EF=A2=85"; }

.fa-arrow-up-wide-short::before, .fa-sort-amount-up::before { content: "=EF=
=85=A1"; }

.fa-arrow-up-z-a::before, .fa-sort-alpha-up-alt::before { content: "=EF=A2=
=82"; }

.fa-arrows-h::before, .fa-arrows-left-right::before { content: "=EF=81=BE";=
 }

.fa-arrows-rotate::before, .fa-refresh::before, .fa-sync::before { content:=
 "=EF=80=A1"; }

.fa-arrows-up-down::before, .fa-arrows-v::before { content: "=EF=81=BD"; }

.fa-arrows-up-down-left-right::before, .fa-arrows::before { content: "=EF=
=81=87"; }

.fa-asterisk::before { content: "*"; }

.fa-at::before { content: "@"; }

.fa-atom::before { content: "=EF=97=92"; }

.fa-audio-description::before { content: "=EF=8A=9E"; }

.fa-austral-sign::before { content: "=EE=82=A9"; }

.fa-award::before { content: "=EF=95=99"; }

.fa-b::before { content: "B"; }

.fa-baby::before { content: "=EF=9D=BC"; }

.fa-baby-carriage::before, .fa-carriage-baby::before { content: "=EF=9D=BD"=
; }

.fa-backward::before { content: "=EF=81=8A"; }

.fa-backward-fast::before, .fa-fast-backward::before { content: "=EF=81=89"=
; }

.fa-backward-step::before, .fa-step-backward::before { content: "=EF=81=88"=
; }

.fa-bacon::before { content: "=EF=9F=A5"; }

.fa-bacteria::before { content: "=EE=81=99"; }

.fa-bacterium::before { content: "=EE=81=9A"; }

.fa-bag-shopping::before, .fa-shopping-bag::before { content: "=EF=8A=90"; =
}

.fa-bahai::before { content: "=EF=99=A6"; }

.fa-baht-sign::before { content: "=EE=82=AC"; }

.fa-ban::before, .fa-cancel::before { content: "=EF=81=9E"; }

.fa-ban-smoking::before, .fa-smoking-ban::before { content: "=EF=95=8D"; }

.fa-band-aid::before, .fa-bandage::before { content: "=EF=91=A2"; }

.fa-barcode::before { content: "=EF=80=AA"; }

.fa-bars::before, .fa-navicon::before { content: "=EF=83=89"; }

.fa-bars-progress::before, .fa-tasks-alt::before { content: "=EF=A0=A8"; }

.fa-bars-staggered::before, .fa-reorder::before, .fa-stream::before { conte=
nt: "=EF=95=90"; }

.fa-baseball-ball::before, .fa-baseball::before { content: "=EF=90=B3"; }

.fa-baseball-bat-ball::before { content: "=EF=90=B2"; }

.fa-basket-shopping::before, .fa-shopping-basket::before { content: "=EF=8A=
=91"; }

.fa-basketball-ball::before, .fa-basketball::before { content: "=EF=90=B4";=
 }

.fa-bath::before, .fa-bathtub::before { content: "=EF=8B=8D"; }

.fa-battery-0::before, .fa-battery-empty::before { content: "=EF=89=84"; }

.fa-battery-5::before, .fa-battery-full::before, .fa-battery::before { cont=
ent: "=EF=89=80"; }

.fa-battery-3::before, .fa-battery-half::before { content: "=EF=89=82"; }

.fa-battery-2::before, .fa-battery-quarter::before { content: "=EF=89=83"; =
}

.fa-battery-4::before, .fa-battery-three-quarters::before { content: "=EF=
=89=81"; }

.fa-bed::before { content: "=EF=88=B6"; }

.fa-bed-pulse::before, .fa-procedures::before { content: "=EF=92=87"; }

.fa-beer-mug-empty::before, .fa-beer::before { content: "=EF=83=BC"; }

.fa-bell::before { content: "=EF=83=B3"; }

.fa-bell-concierge::before, .fa-concierge-bell::before { content: "=EF=95=
=A2"; }

.fa-bell-slash::before { content: "=EF=87=B6"; }

.fa-bezier-curve::before { content: "=EF=95=9B"; }

.fa-bicycle::before { content: "=EF=88=86"; }

.fa-binoculars::before { content: "=EF=87=A5"; }

.fa-biohazard::before { content: "=EF=9E=80"; }

.fa-bitcoin-sign::before { content: "=EE=82=B4"; }

.fa-blender::before { content: "=EF=94=97"; }

.fa-blender-phone::before { content: "=EF=9A=B6"; }

.fa-blog::before { content: "=EF=9E=81"; }

.fa-bold::before { content: "=EF=80=B2"; }

.fa-bolt::before, .fa-zap::before { content: "=EF=83=A7"; }

.fa-bolt-lightning::before { content: "=EE=82=B7"; }

.fa-bomb::before { content: "=EF=87=A2"; }

.fa-bone::before { content: "=EF=97=97"; }

.fa-bong::before { content: "=EF=95=9C"; }

.fa-book::before { content: "=EF=80=AD"; }

.fa-atlas::before, .fa-book-atlas::before { content: "=EF=95=98"; }

.fa-bible::before, .fa-book-bible::before { content: "=EF=99=87"; }

.fa-book-journal-whills::before, .fa-journal-whills::before { content: "=EF=
=99=AA"; }

.fa-book-medical::before { content: "=EF=9F=A6"; }

.fa-book-open::before { content: "=EF=94=98"; }

.fa-book-open-reader::before, .fa-book-reader::before { content: "=EF=97=9A=
"; }

.fa-book-quran::before, .fa-quran::before { content: "=EF=9A=87"; }

.fa-book-dead::before, .fa-book-skull::before { content: "=EF=9A=B7"; }

.fa-bookmark::before { content: "=EF=80=AE"; }

.fa-border-all::before { content: "=EF=A1=8C"; }

.fa-border-none::before { content: "=EF=A1=90"; }

.fa-border-style::before, .fa-border-top-left::before { content: "=EF=A1=93=
"; }

.fa-bowling-ball::before { content: "=EF=90=B6"; }

.fa-box::before { content: "=EF=91=A6"; }

.fa-archive::before, .fa-box-archive::before { content: "=EF=86=87"; }

.fa-box-open::before { content: "=EF=92=9E"; }

.fa-box-tissue::before { content: "=EE=81=9B"; }

.fa-boxes-alt::before, .fa-boxes-stacked::before, .fa-boxes::before { conte=
nt: "=EF=91=A8"; }

.fa-braille::before { content: "=EF=8A=A1"; }

.fa-brain::before { content: "=EF=97=9C"; }

.fa-brazilian-real-sign::before { content: "=EE=91=AC"; }

.fa-bread-slice::before { content: "=EF=9F=AC"; }

.fa-briefcase::before { content: "=EF=82=B1"; }

.fa-briefcase-medical::before { content: "=EF=91=A9"; }

.fa-broom::before { content: "=EF=94=9A"; }

.fa-broom-ball::before, .fa-quidditch-broom-ball::before, .fa-quidditch::be=
fore { content: "=EF=91=98"; }

.fa-brush::before { content: "=EF=95=9D"; }

.fa-bug::before { content: "=EF=86=88"; }

.fa-bug-slash::before { content: "=EE=92=90"; }

.fa-building::before { content: "=EF=86=AD"; }

.fa-bank::before, .fa-building-columns::before, .fa-institution::before, .f=
a-museum::before, .fa-university::before { content: "=EF=86=9C"; }

.fa-bullhorn::before { content: "=EF=82=A1"; }

.fa-bullseye::before { content: "=EF=85=80"; }

.fa-burger::before, .fa-hamburger::before { content: "=EF=A0=85"; }

.fa-bus::before { content: "=EF=88=87"; }

.fa-bus-alt::before, .fa-bus-simple::before { content: "=EF=95=9E"; }

.fa-briefcase-clock::before, .fa-business-time::before { content: "=EF=99=
=8A"; }

.fa-c::before { content: "C"; }

.fa-birthday-cake::before, .fa-cake-candles::before, .fa-cake::before { con=
tent: "=EF=87=BD"; }

.fa-calculator::before { content: "=EF=87=AC"; }

.fa-calendar::before { content: "=EF=84=B3"; }

.fa-calendar-check::before { content: "=EF=89=B4"; }

.fa-calendar-day::before { content: "=EF=9E=83"; }

.fa-calendar-alt::before, .fa-calendar-days::before { content: "=EF=81=B3";=
 }

.fa-calendar-minus::before { content: "=EF=89=B2"; }

.fa-calendar-plus::before { content: "=EF=89=B1"; }

.fa-calendar-week::before { content: "=EF=9E=84"; }

.fa-calendar-times::before, .fa-calendar-xmark::before { content: "=EF=89=
=B3"; }

.fa-camera-alt::before, .fa-camera::before { content: "=EF=80=B0"; }

.fa-camera-retro::before { content: "=EF=82=83"; }

.fa-camera-rotate::before { content: "=EE=83=98"; }

.fa-campground::before { content: "=EF=9A=BB"; }

.fa-candy-cane::before { content: "=EF=9E=86"; }

.fa-cannabis::before { content: "=EF=95=9F"; }

.fa-capsules::before { content: "=EF=91=AB"; }

.fa-automobile::before, .fa-car::before { content: "=EF=86=B9"; }

.fa-battery-car::before, .fa-car-battery::before { content: "=EF=97=9F"; }

.fa-car-crash::before { content: "=EF=97=A1"; }

.fa-car-alt::before, .fa-car-rear::before { content: "=EF=97=9E"; }

.fa-car-side::before { content: "=EF=97=A4"; }

.fa-caravan::before { content: "=EF=A3=BF"; }

.fa-caret-down::before { content: "=EF=83=97"; }

.fa-caret-left::before { content: "=EF=83=99"; }

.fa-caret-right::before { content: "=EF=83=9A"; }

.fa-caret-up::before { content: "=EF=83=98"; }

.fa-carrot::before { content: "=EF=9E=87"; }

.fa-cart-arrow-down::before { content: "=EF=88=98"; }

.fa-cart-flatbed::before, .fa-dolly-flatbed::before { content: "=EF=91=B4";=
 }

.fa-cart-flatbed-suitcase::before, .fa-luggage-cart::before { content: "=EF=
=96=9D"; }

.fa-cart-plus::before { content: "=EF=88=97"; }

.fa-cart-shopping::before, .fa-shopping-cart::before { content: "=EF=81=BA"=
; }

.fa-cash-register::before { content: "=EF=9E=88"; }

.fa-cat::before { content: "=EF=9A=BE"; }

.fa-cedi-sign::before { content: "=EE=83=9F"; }

.fa-cent-sign::before { content: "=EE=8F=B5"; }

.fa-certificate::before { content: "=EF=82=A3"; }

.fa-chair::before { content: "=EF=9B=80"; }

.fa-blackboard::before, .fa-chalkboard::before { content: "=EF=94=9B"; }

.fa-chalkboard-teacher::before, .fa-chalkboard-user::before { content: "=EF=
=94=9C"; }

.fa-champagne-glasses::before, .fa-glass-cheers::before { content: "=EF=9E=
=9F"; }

.fa-charging-station::before { content: "=EF=97=A7"; }

.fa-area-chart::before, .fa-chart-area::before { content: "=EF=87=BE"; }

.fa-bar-chart::before, .fa-chart-bar::before { content: "=EF=82=80"; }

.fa-chart-column::before { content: "=EE=83=A3"; }

.fa-chart-gantt::before { content: "=EE=83=A4"; }

.fa-chart-line::before, .fa-line-chart::before { content: "=EF=88=81"; }

.fa-chart-pie::before, .fa-pie-chart::before { content: "=EF=88=80"; }

.fa-check::before { content: "=EF=80=8C"; }

.fa-check-double::before { content: "=EF=95=A0"; }

.fa-check-to-slot::before, .fa-vote-yea::before { content: "=EF=9D=B2"; }

.fa-cheese::before { content: "=EF=9F=AF"; }

.fa-chess::before { content: "=EF=90=B9"; }

.fa-chess-bishop::before { content: "=EF=90=BA"; }

.fa-chess-board::before { content: "=EF=90=BC"; }

.fa-chess-king::before { content: "=EF=90=BF"; }

.fa-chess-knight::before { content: "=EF=91=81"; }

.fa-chess-pawn::before { content: "=EF=91=83"; }

.fa-chess-queen::before { content: "=EF=91=85"; }

.fa-chess-rook::before { content: "=EF=91=87"; }

.fa-chevron-down::before { content: "=EF=81=B8"; }

.fa-chevron-left::before { content: "=EF=81=93"; }

.fa-chevron-right::before { content: "=EF=81=94"; }

.fa-chevron-up::before { content: "=EF=81=B7"; }

.fa-child::before { content: "=EF=86=AE"; }

.fa-church::before { content: "=EF=94=9D"; }

.fa-circle::before { content: "=EF=84=91"; }

.fa-arrow-circle-down::before, .fa-circle-arrow-down::before { content: "=
=EF=82=AB"; }

.fa-arrow-circle-left::before, .fa-circle-arrow-left::before { content: "=
=EF=82=A8"; }

.fa-arrow-circle-right::before, .fa-circle-arrow-right::before { content: "=
=EF=82=A9"; }

.fa-arrow-circle-up::before, .fa-circle-arrow-up::before { content: "=EF=82=
=AA"; }

.fa-check-circle::before, .fa-circle-check::before { content: "=EF=81=98"; =
}

.fa-chevron-circle-down::before, .fa-circle-chevron-down::before { content:=
 "=EF=84=BA"; }

.fa-chevron-circle-left::before, .fa-circle-chevron-left::before { content:=
 "=EF=84=B7"; }

.fa-chevron-circle-right::before, .fa-circle-chevron-right::before { conten=
t: "=EF=84=B8"; }

.fa-chevron-circle-up::before, .fa-circle-chevron-up::before { content: "=
=EF=84=B9"; }

.fa-circle-dollar-to-slot::before, .fa-donate::before { content: "=EF=92=B9=
"; }

.fa-circle-dot::before, .fa-dot-circle::before { content: "=EF=86=92"; }

.fa-arrow-alt-circle-down::before, .fa-circle-down::before { content: "=EF=
=8D=98"; }

.fa-circle-exclamation::before, .fa-exclamation-circle::before { content: "=
=EF=81=AA"; }

.fa-circle-h::before, .fa-hospital-symbol::before { content: "=EF=91=BE"; }

.fa-adjust::before, .fa-circle-half-stroke::before { content: "=EF=81=82"; =
}

.fa-circle-info::before, .fa-info-circle::before { content: "=EF=81=9A"; }

.fa-arrow-alt-circle-left::before, .fa-circle-left::before { content: "=EF=
=8D=99"; }

.fa-circle-minus::before, .fa-minus-circle::before { content: "=EF=81=96"; =
}

.fa-circle-notch::before { content: "=EF=87=8E"; }

.fa-circle-pause::before, .fa-pause-circle::before { content: "=EF=8A=8B"; =
}

.fa-circle-play::before, .fa-play-circle::before { content: "=EF=85=84"; }

.fa-circle-plus::before, .fa-plus-circle::before { content: "=EF=81=95"; }

.fa-circle-question::before, .fa-question-circle::before { content: "=EF=81=
=99"; }

.fa-circle-radiation::before, .fa-radiation-alt::before { content: "=EF=9E=
=BA"; }

.fa-arrow-alt-circle-right::before, .fa-circle-right::before { content: "=
=EF=8D=9A"; }

.fa-circle-stop::before, .fa-stop-circle::before { content: "=EF=8A=8D"; }

.fa-arrow-alt-circle-up::before, .fa-circle-up::before { content: "=EF=8D=
=9B"; }

.fa-circle-user::before, .fa-user-circle::before { content: "=EF=8A=BD"; }

.fa-circle-xmark::before, .fa-times-circle::before, .fa-xmark-circle::befor=
e { content: "=EF=81=97"; }

.fa-city::before { content: "=EF=99=8F"; }

.fa-clapperboard::before { content: "=EE=84=B1"; }

.fa-clipboard::before { content: "=EF=8C=A8"; }

.fa-clipboard-check::before { content: "=EF=91=AC"; }

.fa-clipboard-list::before { content: "=EF=91=AD"; }

.fa-clock-four::before, .fa-clock::before { content: "=EF=80=97"; }

.fa-clock-rotate-left::before, .fa-history::before { content: "=EF=87=9A"; =
}

.fa-clone::before { content: "=EF=89=8D"; }

.fa-closed-captioning::before { content: "=EF=88=8A"; }

.fa-cloud::before { content: "=EF=83=82"; }

.fa-cloud-arrow-down::before, .fa-cloud-download-alt::before, .fa-cloud-dow=
nload::before { content: "=EF=83=AD"; }

.fa-cloud-arrow-up::before, .fa-cloud-upload-alt::before, .fa-cloud-upload:=
:before { content: "=EF=83=AE"; }

.fa-cloud-meatball::before { content: "=EF=9C=BB"; }

.fa-cloud-moon::before { content: "=EF=9B=83"; }

.fa-cloud-moon-rain::before { content: "=EF=9C=BC"; }

.fa-cloud-rain::before { content: "=EF=9C=BD"; }

.fa-cloud-showers-heavy::before { content: "=EF=9D=80"; }

.fa-cloud-sun::before { content: "=EF=9B=84"; }

.fa-cloud-sun-rain::before { content: "=EF=9D=83"; }

.fa-clover::before { content: "=EE=84=B9"; }

.fa-code::before { content: "=EF=84=A1"; }

.fa-code-branch::before { content: "=EF=84=A6"; }

.fa-code-commit::before { content: "=EF=8E=86"; }

.fa-code-compare::before { content: "=EE=84=BA"; }

.fa-code-fork::before { content: "=EE=84=BB"; }

.fa-code-merge::before { content: "=EF=8E=87"; }

.fa-code-pull-request::before { content: "=EE=84=BC"; }

.fa-coins::before { content: "=EF=94=9E"; }

.fa-colon-sign::before { content: "=EE=85=80"; }

.fa-comment::before { content: "=EF=81=B5"; }

.fa-comment-dollar::before { content: "=EF=99=91"; }

.fa-comment-dots::before, .fa-commenting::before { content: "=EF=92=AD"; }

.fa-comment-medical::before { content: "=EF=9F=B5"; }

.fa-comment-slash::before { content: "=EF=92=B3"; }

.fa-comment-sms::before, .fa-sms::before { content: "=EF=9F=8D"; }

.fa-comments::before { content: "=EF=82=86"; }

.fa-comments-dollar::before { content: "=EF=99=93"; }

.fa-compact-disc::before { content: "=EF=94=9F"; }

.fa-compass::before { content: "=EF=85=8E"; }

.fa-compass-drafting::before, .fa-drafting-compass::before { content: "=EF=
=95=A8"; }

.fa-compress::before { content: "=EF=81=A6"; }

.fa-computer-mouse::before, .fa-mouse::before { content: "=EF=A3=8C"; }

.fa-cookie::before { content: "=EF=95=A3"; }

.fa-cookie-bite::before { content: "=EF=95=A4"; }

.fa-copy::before { content: "=EF=83=85"; }

.fa-copyright::before { content: "=EF=87=B9"; }

.fa-couch::before { content: "=EF=92=B8"; }

.fa-credit-card-alt::before, .fa-credit-card::before { content: "=EF=82=9D"=
; }

.fa-crop::before { content: "=EF=84=A5"; }

.fa-crop-alt::before, .fa-crop-simple::before { content: "=EF=95=A5"; }

.fa-cross::before { content: "=EF=99=94"; }

.fa-crosshairs::before { content: "=EF=81=9B"; }

.fa-crow::before { content: "=EF=94=A0"; }

.fa-crown::before { content: "=EF=94=A1"; }

.fa-crutch::before { content: "=EF=9F=B7"; }

.fa-cruzeiro-sign::before { content: "=EE=85=92"; }

.fa-cube::before { content: "=EF=86=B2"; }

.fa-cubes::before { content: "=EF=86=B3"; }

.fa-d::before { content: "D"; }

.fa-database::before { content: "=EF=87=80"; }

.fa-backspace::before, .fa-delete-left::before { content: "=EF=95=9A"; }

.fa-democrat::before { content: "=EF=9D=87"; }

.fa-desktop-alt::before, .fa-desktop::before { content: "=EF=8E=90"; }

.fa-dharmachakra::before { content: "=EF=99=95"; }

.fa-diagram-next::before { content: "=EE=91=B6"; }

.fa-diagram-predecessor::before { content: "=EE=91=B7"; }

.fa-diagram-project::before, .fa-project-diagram::before { content: "=EF=95=
=82"; }

.fa-diagram-successor::before { content: "=EE=91=BA"; }

.fa-diamond::before { content: "=EF=88=99"; }

.fa-diamond-turn-right::before, .fa-directions::before { content: "=EF=97=
=AB"; }

.fa-dice::before { content: "=EF=94=A2"; }

.fa-dice-d20::before { content: "=EF=9B=8F"; }

.fa-dice-d6::before { content: "=EF=9B=91"; }

.fa-dice-five::before { content: "=EF=94=A3"; }

.fa-dice-four::before { content: "=EF=94=A4"; }

.fa-dice-one::before { content: "=EF=94=A5"; }

.fa-dice-six::before { content: "=EF=94=A6"; }

.fa-dice-three::before { content: "=EF=94=A7"; }

.fa-dice-two::before { content: "=EF=94=A8"; }

.fa-disease::before { content: "=EF=9F=BA"; }

.fa-divide::before { content: "=EF=94=A9"; }

.fa-dna::before { content: "=EF=91=B1"; }

.fa-dog::before { content: "=EF=9B=93"; }

.fa-dollar-sign::before, .fa-dollar::before, .fa-usd::before { content: "$"=
; }

.fa-dolly-box::before, .fa-dolly::before { content: "=EF=91=B2"; }

.fa-dong-sign::before { content: "=EE=85=A9"; }

.fa-door-closed::before { content: "=EF=94=AA"; }

.fa-door-open::before { content: "=EF=94=AB"; }

.fa-dove::before { content: "=EF=92=BA"; }

.fa-compress-alt::before, .fa-down-left-and-up-right-to-center::before { co=
ntent: "=EF=90=A2"; }

.fa-down-long::before, .fa-long-arrow-alt-down::before { content: "=EF=8C=
=89"; }

.fa-download::before { content: "=EF=80=99"; }

.fa-dragon::before { content: "=EF=9B=95"; }

.fa-draw-polygon::before { content: "=EF=97=AE"; }

.fa-droplet::before, .fa-tint::before { content: "=EF=81=83"; }

.fa-droplet-slash::before, .fa-tint-slash::before { content: "=EF=97=87"; }

.fa-drum::before { content: "=EF=95=A9"; }

.fa-drum-steelpan::before { content: "=EF=95=AA"; }

.fa-drumstick-bite::before { content: "=EF=9B=97"; }

.fa-dumbbell::before { content: "=EF=91=8B"; }

.fa-dumpster::before { content: "=EF=9E=93"; }

.fa-dumpster-fire::before { content: "=EF=9E=94"; }

.fa-dungeon::before { content: "=EF=9B=99"; }

.fa-e::before { content: "E"; }

.fa-deaf::before, .fa-deafness::before, .fa-ear-deaf::before, .fa-hard-of-h=
earing::before { content: "=EF=8A=A4"; }

.fa-assistive-listening-systems::before, .fa-ear-listen::before { content: =
"=EF=8A=A2"; }

.fa-earth-africa::before, .fa-globe-africa::before { content: "=EF=95=BC"; =
}

.fa-earth-america::before, .fa-earth-americas::before, .fa-earth::before, .=
fa-globe-americas::before { content: "=EF=95=BD"; }

.fa-earth-asia::before, .fa-globe-asia::before { content: "=EF=95=BE"; }

.fa-earth-europe::before, .fa-globe-europe::before { content: "=EF=9E=A2"; =
}

.fa-earth-oceania::before, .fa-globe-oceania::before { content: "=EE=91=BB"=
; }

.fa-egg::before { content: "=EF=9F=BB"; }

.fa-eject::before { content: "=EF=81=92"; }

.fa-elevator::before { content: "=EE=85=AD"; }

.fa-ellipsis-h::before, .fa-ellipsis::before { content: "=EF=85=81"; }

.fa-ellipsis-v::before, .fa-ellipsis-vertical::before { content: "=EF=85=82=
"; }

.fa-envelope::before { content: "=EF=83=A0"; }

.fa-envelope-open::before { content: "=EF=8A=B6"; }

.fa-envelope-open-text::before { content: "=EF=99=98"; }

.fa-envelopes-bulk::before, .fa-mail-bulk::before { content: "=EF=99=B4"; }

.fa-equals::before { content: "=3D"; }

.fa-eraser::before { content: "=EF=84=AD"; }

.fa-ethernet::before { content: "=EF=9E=96"; }

.fa-eur::before, .fa-euro-sign::before, .fa-euro::before { content: "=EF=85=
=93"; }

.fa-exclamation::before { content: "!"; }

.fa-expand::before { content: "=EF=81=A5"; }

.fa-eye::before { content: "=EF=81=AE"; }

.fa-eye-dropper-empty::before, .fa-eye-dropper::before, .fa-eyedropper::bef=
ore { content: "=EF=87=BB"; }

.fa-eye-low-vision::before, .fa-low-vision::before { content: "=EF=8A=A8"; =
}

.fa-eye-slash::before { content: "=EF=81=B0"; }

.fa-f::before { content: "F"; }

.fa-angry::before, .fa-face-angry::before { content: "=EF=95=96"; }

.fa-dizzy::before, .fa-face-dizzy::before { content: "=EF=95=A7"; }

.fa-face-flushed::before, .fa-flushed::before { content: "=EF=95=B9"; }

.fa-face-frown::before, .fa-frown::before { content: "=EF=84=99"; }

.fa-face-frown-open::before, .fa-frown-open::before { content: "=EF=95=BA";=
 }

.fa-face-grimace::before, .fa-grimace::before { content: "=EF=95=BF"; }

.fa-face-grin::before, .fa-grin::before { content: "=EF=96=80"; }

.fa-face-grin-beam::before, .fa-grin-beam::before { content: "=EF=96=82"; }

.fa-face-grin-beam-sweat::before, .fa-grin-beam-sweat::before { content: "=
=EF=96=83"; }

.fa-face-grin-hearts::before, .fa-grin-hearts::before { content: "=EF=96=84=
"; }

.fa-face-grin-squint::before, .fa-grin-squint::before { content: "=EF=96=85=
"; }

.fa-face-grin-squint-tears::before, .fa-grin-squint-tears::before { content=
: "=EF=96=86"; }

.fa-face-grin-stars::before, .fa-grin-stars::before { content: "=EF=96=87";=
 }

.fa-face-grin-tears::before, .fa-grin-tears::before { content: "=EF=96=88";=
 }

.fa-face-grin-tongue::before, .fa-grin-tongue::before { content: "=EF=96=89=
"; }

.fa-face-grin-tongue-squint::before, .fa-grin-tongue-squint::before { conte=
nt: "=EF=96=8A"; }

.fa-face-grin-tongue-wink::before, .fa-grin-tongue-wink::before { content: =
"=EF=96=8B"; }

.fa-face-grin-wide::before, .fa-grin-alt::before { content: "=EF=96=81"; }

.fa-face-grin-wink::before, .fa-grin-wink::before { content: "=EF=96=8C"; }

.fa-face-kiss::before, .fa-kiss::before { content: "=EF=96=96"; }

.fa-face-kiss-beam::before, .fa-kiss-beam::before { content: "=EF=96=97"; }

.fa-face-kiss-wink-heart::before, .fa-kiss-wink-heart::before { content: "=
=EF=96=98"; }

.fa-face-laugh::before, .fa-laugh::before { content: "=EF=96=99"; }

.fa-face-laugh-beam::before, .fa-laugh-beam::before { content: "=EF=96=9A";=
 }

.fa-face-laugh-squint::before, .fa-laugh-squint::before { content: "=EF=96=
=9B"; }

.fa-face-laugh-wink::before, .fa-laugh-wink::before { content: "=EF=96=9C";=
 }

.fa-face-meh::before, .fa-meh::before { content: "=EF=84=9A"; }

.fa-face-meh-blank::before, .fa-meh-blank::before { content: "=EF=96=A4"; }

.fa-face-rolling-eyes::before, .fa-meh-rolling-eyes::before { content: "=EF=
=96=A5"; }

.fa-face-sad-cry::before, .fa-sad-cry::before { content: "=EF=96=B3"; }

.fa-face-sad-tear::before, .fa-sad-tear::before { content: "=EF=96=B4"; }

.fa-face-smile::before, .fa-smile::before { content: "=EF=84=98"; }

.fa-face-smile-beam::before, .fa-smile-beam::before { content: "=EF=96=B8";=
 }

.fa-face-smile-wink::before, .fa-smile-wink::before { content: "=EF=93=9A";=
 }

.fa-face-surprise::before, .fa-surprise::before { content: "=EF=97=82"; }

.fa-face-tired::before, .fa-tired::before { content: "=EF=97=88"; }

.fa-fan::before { content: "=EF=A1=A3"; }

.fa-faucet::before { content: "=EE=80=85"; }

.fa-fax::before { content: "=EF=86=AC"; }

.fa-feather::before { content: "=EF=94=AD"; }

.fa-feather-alt::before, .fa-feather-pointed::before { content: "=EF=95=AB"=
; }

.fa-file::before { content: "=EF=85=9B"; }

.fa-file-arrow-down::before, .fa-file-download::before { content: "=EF=95=
=AD"; }

.fa-file-arrow-up::before, .fa-file-upload::before { content: "=EF=95=B4"; =
}

.fa-file-audio::before { content: "=EF=87=87"; }

.fa-file-code::before { content: "=EF=87=89"; }

.fa-file-contract::before { content: "=EF=95=AC"; }

.fa-file-csv::before { content: "=EF=9B=9D"; }

.fa-file-excel::before { content: "=EF=87=83"; }

.fa-arrow-right-from-file::before, .fa-file-export::before { content: "=EF=
=95=AE"; }

.fa-file-image::before { content: "=EF=87=85"; }

.fa-arrow-right-to-file::before, .fa-file-import::before { content: "=EF=95=
=AF"; }

.fa-file-invoice::before { content: "=EF=95=B0"; }

.fa-file-invoice-dollar::before { content: "=EF=95=B1"; }

.fa-file-alt::before, .fa-file-lines::before, .fa-file-text::before { conte=
nt: "=EF=85=9C"; }

.fa-file-medical::before { content: "=EF=91=B7"; }

.fa-file-pdf::before { content: "=EF=87=81"; }

.fa-file-powerpoint::before { content: "=EF=87=84"; }

.fa-file-prescription::before { content: "=EF=95=B2"; }

.fa-file-signature::before { content: "=EF=95=B3"; }

.fa-file-video::before { content: "=EF=87=88"; }

.fa-file-medical-alt::before, .fa-file-waveform::before { content: "=EF=91=
=B8"; }

.fa-file-word::before { content: "=EF=87=82"; }

.fa-file-archive::before, .fa-file-zipper::before { content: "=EF=87=86"; }

.fa-fill::before { content: "=EF=95=B5"; }

.fa-fill-drip::before { content: "=EF=95=B6"; }

.fa-film::before { content: "=EF=80=88"; }

.fa-filter::before { content: "=EF=82=B0"; }

.fa-filter-circle-dollar::before, .fa-funnel-dollar::before { content: "=EF=
=99=A2"; }

.fa-filter-circle-xmark::before { content: "=EE=85=BB"; }

.fa-fingerprint::before { content: "=EF=95=B7"; }

.fa-fire::before { content: "=EF=81=AD"; }

.fa-fire-extinguisher::before { content: "=EF=84=B4"; }

.fa-fire-alt::before, .fa-fire-flame-curved::before { content: "=EF=9F=A4";=
 }

.fa-burn::before, .fa-fire-flame-simple::before { content: "=EF=91=AA"; }

.fa-fish::before { content: "=EF=95=B8"; }

.fa-flag::before { content: "=EF=80=A4"; }

.fa-flag-checkered::before { content: "=EF=84=9E"; }

.fa-flag-usa::before { content: "=EF=9D=8D"; }

.fa-flask::before { content: "=EF=83=83"; }

.fa-floppy-disk::before, .fa-save::before { content: "=EF=83=87"; }

.fa-florin-sign::before { content: "=EE=86=84"; }

.fa-folder::before { content: "=EF=81=BB"; }

.fa-folder-minus::before { content: "=EF=99=9D"; }

.fa-folder-open::before { content: "=EF=81=BC"; }

.fa-folder-plus::before { content: "=EF=99=9E"; }

.fa-folder-tree::before { content: "=EF=A0=82"; }

.fa-font::before { content: "=EF=80=B1"; }

.fa-football-ball::before, .fa-football::before { content: "=EF=91=8E"; }

.fa-forward::before { content: "=EF=81=8E"; }

.fa-fast-forward::before, .fa-forward-fast::before { content: "=EF=81=90"; =
}

.fa-forward-step::before, .fa-step-forward::before { content: "=EF=81=91"; =
}

.fa-franc-sign::before { content: "=EE=86=8F"; }

.fa-frog::before { content: "=EF=94=AE"; }

.fa-futbol-ball::before, .fa-futbol::before, .fa-soccer-ball::before { cont=
ent: "=EF=87=A3"; }

.fa-g::before { content: "G"; }

.fa-gamepad::before { content: "=EF=84=9B"; }

.fa-gas-pump::before { content: "=EF=94=AF"; }

.fa-dashboard::before, .fa-gauge-med::before, .fa-gauge::before, .fa-tachom=
eter-alt-average::before { content: "=EF=98=A4"; }

.fa-gauge-high::before, .fa-tachometer-alt-fast::before, .fa-tachometer-alt=
::before { content: "=EF=98=A5"; }

.fa-gauge-simple-med::before, .fa-gauge-simple::before, .fa-tachometer-aver=
age::before { content: "=EF=98=A9"; }

.fa-gauge-simple-high::before, .fa-tachometer-fast::before, .fa-tachometer:=
:before { content: "=EF=98=AA"; }

.fa-gavel::before, .fa-legal::before { content: "=EF=83=A3"; }

.fa-cog::before, .fa-gear::before { content: "=EF=80=93"; }

.fa-cogs::before, .fa-gears::before { content: "=EF=82=85"; }

.fa-gem::before { content: "=EF=8E=A5"; }

.fa-genderless::before { content: "=EF=88=AD"; }

.fa-ghost::before { content: "=EF=9B=A2"; }

.fa-gift::before { content: "=EF=81=AB"; }

.fa-gifts::before { content: "=EF=9E=9C"; }

.fa-glasses::before { content: "=EF=94=B0"; }

.fa-globe::before { content: "=EF=82=AC"; }

.fa-golf-ball-tee::before, .fa-golf-ball::before { content: "=EF=91=90"; }

.fa-gopuram::before { content: "=EF=99=A4"; }

.fa-graduation-cap::before, .fa-mortar-board::before { content: "=EF=86=9D"=
; }

.fa-greater-than::before { content: ">"; }

.fa-greater-than-equal::before { content: "=EF=94=B2"; }

.fa-grip-horizontal::before, .fa-grip::before { content: "=EF=96=8D"; }

.fa-grip-lines::before { content: "=EF=9E=A4"; }

.fa-grip-lines-vertical::before { content: "=EF=9E=A5"; }

.fa-grip-vertical::before { content: "=EF=96=8E"; }

.fa-guarani-sign::before { content: "=EE=86=9A"; }

.fa-guitar::before { content: "=EF=9E=A6"; }

.fa-gun::before { content: "=EE=86=9B"; }

.fa-h::before { content: "H"; }

.fa-hammer::before { content: "=EF=9B=A3"; }

.fa-hamsa::before { content: "=EF=99=A5"; }

.fa-hand-paper::before, .fa-hand::before { content: "=EF=89=96"; }

.fa-hand-back-fist::before, .fa-hand-rock::before { content: "=EF=89=95"; }

.fa-allergies::before, .fa-hand-dots::before { content: "=EF=91=A1"; }

.fa-fist-raised::before, .fa-hand-fist::before { content: "=EF=9B=9E"; }

.fa-hand-holding::before { content: "=EF=92=BD"; }

.fa-hand-holding-dollar::before, .fa-hand-holding-usd::before { content: "=
=EF=93=80"; }

.fa-hand-holding-droplet::before, .fa-hand-holding-water::before { content:=
 "=EF=93=81"; }

.fa-hand-holding-heart::before { content: "=EF=92=BE"; }

.fa-hand-holding-medical::before { content: "=EE=81=9C"; }

.fa-hand-lizard::before { content: "=EF=89=98"; }

.fa-hand-middle-finger::before { content: "=EF=A0=86"; }

.fa-hand-peace::before { content: "=EF=89=9B"; }

.fa-hand-point-down::before { content: "=EF=82=A7"; }

.fa-hand-point-left::before { content: "=EF=82=A5"; }

.fa-hand-point-right::before { content: "=EF=82=A4"; }

.fa-hand-point-up::before { content: "=EF=82=A6"; }

.fa-hand-pointer::before { content: "=EF=89=9A"; }

.fa-hand-scissors::before { content: "=EF=89=97"; }

.fa-hand-sparkles::before { content: "=EE=81=9D"; }

.fa-hand-spock::before { content: "=EF=89=99"; }

.fa-hands::before, .fa-sign-language::before, .fa-signing::before { content=
: "=EF=8A=A7"; }

.fa-american-sign-language-interpreting::before, .fa-asl-interpreting::befo=
re, .fa-hands-american-sign-language-interpreting::before, .fa-hands-asl-in=
terpreting::before { content: "=EF=8A=A3"; }

.fa-hands-bubbles::before, .fa-hands-wash::before { content: "=EE=81=9E"; }

.fa-hands-clapping::before { content: "=EE=86=A8"; }

.fa-hands-holding::before { content: "=EF=93=82"; }

.fa-hands-praying::before, .fa-praying-hands::before { content: "=EF=9A=84"=
; }

.fa-handshake::before { content: "=EF=8A=B5"; }

.fa-hands-helping::before, .fa-handshake-angle::before { content: "=EF=93=
=84"; }

.fa-handshake-alt-slash::before, .fa-handshake-simple-slash::before { conte=
nt: "=EE=81=9F"; }

.fa-handshake-slash::before { content: "=EE=81=A0"; }

.fa-hanukiah::before { content: "=EF=9B=A6"; }

.fa-hard-drive::before, .fa-hdd::before { content: "=EF=82=A0"; }

.fa-hashtag::before { content: "#"; }

.fa-hat-cowboy::before { content: "=EF=A3=80"; }

.fa-hat-cowboy-side::before { content: "=EF=A3=81"; }

.fa-hat-wizard::before { content: "=EF=9B=A8"; }

.fa-head-side-cough::before { content: "=EE=81=A1"; }

.fa-head-side-cough-slash::before { content: "=EE=81=A2"; }

.fa-head-side-mask::before { content: "=EE=81=A3"; }

.fa-head-side-virus::before { content: "=EE=81=A4"; }

.fa-header::before, .fa-heading::before { content: "=EF=87=9C"; }

.fa-headphones::before { content: "=EF=80=A5"; }

.fa-headphones-alt::before, .fa-headphones-simple::before { content: "=EF=
=96=8F"; }

.fa-headset::before { content: "=EF=96=90"; }

.fa-heart::before { content: "=EF=80=84"; }

.fa-heart-broken::before, .fa-heart-crack::before { content: "=EF=9E=A9"; }

.fa-heart-pulse::before, .fa-heartbeat::before { content: "=EF=88=9E"; }

.fa-helicopter::before { content: "=EF=94=B3"; }

.fa-hard-hat::before, .fa-hat-hard::before, .fa-helmet-safety::before { con=
tent: "=EF=A0=87"; }

.fa-highlighter::before { content: "=EF=96=91"; }

.fa-hippo::before { content: "=EF=9B=AD"; }

.fa-hockey-puck::before { content: "=EF=91=93"; }

.fa-holly-berry::before { content: "=EF=9E=AA"; }

.fa-horse::before { content: "=EF=9B=B0"; }

.fa-horse-head::before { content: "=EF=9E=AB"; }

.fa-hospital-alt::before, .fa-hospital-wide::before, .fa-hospital::before {=
 content: "=EF=83=B8"; }

.fa-hospital-user::before { content: "=EF=A0=8D"; }

.fa-hot-tub-person::before, .fa-hot-tub::before { content: "=EF=96=93"; }

.fa-hotdog::before { content: "=EF=A0=8F"; }

.fa-hotel::before { content: "=EF=96=94"; }

.fa-hourglass-2::before, .fa-hourglass-half::before, .fa-hourglass::before =
{ content: "=EF=89=94"; }

.fa-hourglass-empty::before { content: "=EF=89=92"; }

.fa-hourglass-3::before, .fa-hourglass-end::before { content: "=EF=89=93"; =
}

.fa-hourglass-1::before, .fa-hourglass-start::before { content: "=EF=89=91"=
; }

.fa-home-alt::before, .fa-home-lg-alt::before, .fa-home::before, .fa-house:=
:before { content: "=EF=80=95"; }

.fa-home-lg::before, .fa-house-chimney::before { content: "=EE=8E=AF"; }

.fa-house-chimney-crack::before, .fa-house-damage::before { content: "=EF=
=9B=B1"; }

.fa-clinic-medical::before, .fa-house-chimney-medical::before { content: "=
=EF=9F=B2"; }

.fa-house-chimney-user::before { content: "=EE=81=A5"; }

.fa-house-chimney-window::before { content: "=EE=80=8D"; }

.fa-house-crack::before { content: "=EE=8E=B1"; }

.fa-house-laptop::before, .fa-laptop-house::before { content: "=EE=81=A6"; =
}

.fa-house-medical::before { content: "=EE=8E=B2"; }

.fa-home-user::before, .fa-house-user::before { content: "=EE=86=B0"; }

.fa-hryvnia-sign::before, .fa-hryvnia::before { content: "=EF=9B=B2"; }

.fa-i::before { content: "I"; }

.fa-i-cursor::before { content: "=EF=89=86"; }

.fa-ice-cream::before { content: "=EF=A0=90"; }

.fa-icicles::before { content: "=EF=9E=AD"; }

.fa-heart-music-camera-bolt::before, .fa-icons::before { content: "=EF=A1=
=AD"; }

.fa-id-badge::before { content: "=EF=8B=81"; }

.fa-drivers-license::before, .fa-id-card::before { content: "=EF=8B=82"; }

.fa-id-card-alt::before, .fa-id-card-clip::before { content: "=EF=91=BF"; }

.fa-igloo::before { content: "=EF=9E=AE"; }

.fa-image::before { content: "=EF=80=BE"; }

.fa-image-portrait::before, .fa-portrait::before { content: "=EF=8F=A0"; }

.fa-images::before { content: "=EF=8C=82"; }

.fa-inbox::before { content: "=EF=80=9C"; }

.fa-indent::before { content: "=EF=80=BC"; }

.fa-indian-rupee-sign::before, .fa-indian-rupee::before, .fa-inr::before { =
content: "=EE=86=BC"; }

.fa-industry::before { content: "=EF=89=B5"; }

.fa-infinity::before { content: "=EF=94=B4"; }

.fa-info::before { content: "=EF=84=A9"; }

.fa-italic::before { content: "=EF=80=B3"; }

.fa-j::before { content: "J"; }

.fa-jedi::before { content: "=EF=99=A9"; }

.fa-fighter-jet::before, .fa-jet-fighter::before { content: "=EF=83=BB"; }

.fa-joint::before { content: "=EF=96=95"; }

.fa-k::before { content: "K"; }

.fa-kaaba::before { content: "=EF=99=AB"; }

.fa-key::before { content: "=EF=82=84"; }

.fa-keyboard::before { content: "=EF=84=9C"; }

.fa-khanda::before { content: "=EF=99=AD"; }

.fa-kip-sign::before { content: "=EE=87=84"; }

.fa-first-aid::before, .fa-kit-medical::before { content: "=EF=91=B9"; }

.fa-kiwi-bird::before { content: "=EF=94=B5"; }

.fa-l::before { content: "L"; }

.fa-landmark::before { content: "=EF=99=AF"; }

.fa-language::before { content: "=EF=86=AB"; }

.fa-laptop::before { content: "=EF=84=89"; }

.fa-laptop-code::before { content: "=EF=97=BC"; }

.fa-laptop-medical::before { content: "=EF=A0=92"; }

.fa-lari-sign::before { content: "=EE=87=88"; }

.fa-layer-group::before { content: "=EF=97=BD"; }

.fa-leaf::before { content: "=EF=81=AC"; }

.fa-left-long::before, .fa-long-arrow-alt-left::before { content: "=EF=8C=
=8A"; }

.fa-arrows-alt-h::before, .fa-left-right::before { content: "=EF=8C=B7"; }

.fa-lemon::before { content: "=EF=82=94"; }

.fa-less-than::before { content: "<"; }

.fa-less-than-equal::before { content: "=EF=94=B7"; }

.fa-life-ring::before { content: "=EF=87=8D"; }

.fa-lightbulb::before { content: "=EF=83=AB"; }

.fa-chain::before, .fa-link::before { content: "=EF=83=81"; }

.fa-chain-broken::before, .fa-chain-slash::before, .fa-link-slash::before, =
.fa-unlink::before { content: "=EF=84=A7"; }

.fa-lira-sign::before { content: "=EF=86=95"; }

.fa-list-squares::before, .fa-list::before { content: "=EF=80=BA"; }

.fa-list-check::before, .fa-tasks::before { content: "=EF=82=AE"; }

.fa-list-1-2::before, .fa-list-numeric::before, .fa-list-ol::before { conte=
nt: "=EF=83=8B"; }

.fa-list-dots::before, .fa-list-ul::before { content: "=EF=83=8A"; }

.fa-litecoin-sign::before { content: "=EE=87=93"; }

.fa-location-arrow::before { content: "=EF=84=A4"; }

.fa-location-crosshairs::before, .fa-location::before { content: "=EF=98=81=
"; }

.fa-location-dot::before, .fa-map-marker-alt::before { content: "=EF=8F=85"=
; }

.fa-location-pin::before, .fa-map-marker::before { content: "=EF=81=81"; }

.fa-lock::before { content: "=EF=80=A3"; }

.fa-lock-open::before { content: "=EF=8F=81"; }

.fa-lungs::before { content: "=EF=98=84"; }

.fa-lungs-virus::before { content: "=EE=81=A7"; }

.fa-m::before { content: "M"; }

.fa-magnet::before { content: "=EF=81=B6"; }

.fa-magnifying-glass::before, .fa-search::before { content: "=EF=80=82"; }

.fa-magnifying-glass-dollar::before, .fa-search-dollar::before { content: "=
=EF=9A=88"; }

.fa-magnifying-glass-location::before, .fa-search-location::before { conten=
t: "=EF=9A=89"; }

.fa-magnifying-glass-minus::before, .fa-search-minus::before { content: "=
=EF=80=90"; }

.fa-magnifying-glass-plus::before, .fa-search-plus::before { content: "=EF=
=80=8E"; }

.fa-manat-sign::before { content: "=EE=87=95"; }

.fa-map::before { content: "=EF=89=B9"; }

.fa-map-location::before, .fa-map-marked::before { content: "=EF=96=9F"; }

.fa-map-location-dot::before, .fa-map-marked-alt::before { content: "=EF=96=
=A0"; }

.fa-map-pin::before { content: "=EF=89=B6"; }

.fa-marker::before { content: "=EF=96=A1"; }

.fa-mars::before { content: "=EF=88=A2"; }

.fa-mars-and-venus::before { content: "=EF=88=A4"; }

.fa-mars-double::before { content: "=EF=88=A7"; }

.fa-mars-stroke::before { content: "=EF=88=A9"; }

.fa-mars-stroke-h::before, .fa-mars-stroke-right::before { content: "=EF=88=
=AB"; }

.fa-mars-stroke-up::before, .fa-mars-stroke-v::before { content: "=EF=88=AA=
"; }

.fa-glass-martini-alt::before, .fa-martini-glass::before { content: "=EF=95=
=BB"; }

.fa-cocktail::before, .fa-martini-glass-citrus::before { content: "=EF=95=
=A1"; }

.fa-glass-martini::before, .fa-martini-glass-empty::before { content: "=EF=
=80=80"; }

.fa-mask::before { content: "=EF=9B=BA"; }

.fa-mask-face::before { content: "=EE=87=97"; }

.fa-masks-theater::before, .fa-theater-masks::before { content: "=EF=98=B0"=
; }

.fa-expand-arrows-alt::before, .fa-maximize::before { content: "=EF=8C=9E";=
 }

.fa-medal::before { content: "=EF=96=A2"; }

.fa-memory::before { content: "=EF=94=B8"; }

.fa-menorah::before { content: "=EF=99=B6"; }

.fa-mercury::before { content: "=EF=88=A3"; }

.fa-comment-alt::before, .fa-message::before { content: "=EF=89=BA"; }

.fa-meteor::before { content: "=EF=9D=93"; }

.fa-microchip::before { content: "=EF=8B=9B"; }

.fa-microphone::before { content: "=EF=84=B0"; }

.fa-microphone-alt::before, .fa-microphone-lines::before { content: "=EF=8F=
=89"; }

.fa-microphone-alt-slash::before, .fa-microphone-lines-slash::before { cont=
ent: "=EF=94=B9"; }

.fa-microphone-slash::before { content: "=EF=84=B1"; }

.fa-microscope::before { content: "=EF=98=90"; }

.fa-mill-sign::before { content: "=EE=87=AD"; }

.fa-compress-arrows-alt::before, .fa-minimize::before { content: "=EF=9E=8C=
"; }

.fa-minus::before, .fa-subtract::before { content: "=EF=81=A8"; }

.fa-mitten::before { content: "=EF=9E=B5"; }

.fa-mobile-android::before, .fa-mobile-phone::before, .fa-mobile::before { =
content: "=EF=8F=8E"; }

.fa-mobile-button::before { content: "=EF=84=8B"; }

.fa-mobile-alt::before, .fa-mobile-screen-button::before { content: "=EF=8F=
=8D"; }

.fa-money-bill::before { content: "=EF=83=96"; }

.fa-money-bill-1::before, .fa-money-bill-alt::before { content: "=EF=8F=91"=
; }

.fa-money-bill-1-wave::before, .fa-money-bill-wave-alt::before { content: "=
=EF=94=BB"; }

.fa-money-bill-wave::before { content: "=EF=94=BA"; }

.fa-money-check::before { content: "=EF=94=BC"; }

.fa-money-check-alt::before, .fa-money-check-dollar::before { content: "=EF=
=94=BD"; }

.fa-monument::before { content: "=EF=96=A6"; }

.fa-moon::before { content: "=EF=86=86"; }

.fa-mortar-pestle::before { content: "=EF=96=A7"; }

.fa-mosque::before { content: "=EF=99=B8"; }

.fa-motorcycle::before { content: "=EF=88=9C"; }

.fa-mountain::before { content: "=EF=9B=BC"; }

.fa-mug-hot::before { content: "=EF=9E=B6"; }

.fa-coffee::before, .fa-mug-saucer::before { content: "=EF=83=B4"; }

.fa-music::before { content: "=EF=80=81"; }

.fa-n::before { content: "N"; }

.fa-naira-sign::before { content: "=EE=87=B6"; }

.fa-network-wired::before { content: "=EF=9B=BF"; }

.fa-neuter::before { content: "=EF=88=AC"; }

.fa-newspaper::before { content: "=EF=87=AA"; }

.fa-not-equal::before { content: "=EF=94=BE"; }

.fa-note-sticky::before, .fa-sticky-note::before { content: "=EF=89=89"; }

.fa-notes-medical::before { content: "=EF=92=81"; }

.fa-o::before { content: "O"; }

.fa-object-group::before { content: "=EF=89=87"; }

.fa-object-ungroup::before { content: "=EF=89=88"; }

.fa-oil-can::before { content: "=EF=98=93"; }

.fa-om::before { content: "=EF=99=B9"; }

.fa-otter::before { content: "=EF=9C=80"; }

.fa-dedent::before, .fa-outdent::before { content: "=EF=80=BB"; }

.fa-p::before { content: "P"; }

.fa-pager::before { content: "=EF=A0=95"; }

.fa-paint-roller::before { content: "=EF=96=AA"; }

.fa-paint-brush::before, .fa-paintbrush::before { content: "=EF=87=BC"; }

.fa-palette::before { content: "=EF=94=BF"; }

.fa-pallet::before { content: "=EF=92=82"; }

.fa-panorama::before { content: "=EE=88=89"; }

.fa-paper-plane::before { content: "=EF=87=98"; }

.fa-paperclip::before { content: "=EF=83=86"; }

.fa-parachute-box::before { content: "=EF=93=8D"; }

.fa-paragraph::before { content: "=EF=87=9D"; }

.fa-passport::before { content: "=EF=96=AB"; }

.fa-file-clipboard::before, .fa-paste::before { content: "=EF=83=AA"; }

.fa-pause::before { content: "=EF=81=8C"; }

.fa-paw::before { content: "=EF=86=B0"; }

.fa-peace::before { content: "=EF=99=BC"; }

.fa-pen::before { content: "=EF=8C=84"; }

.fa-pen-alt::before, .fa-pen-clip::before { content: "=EF=8C=85"; }

.fa-pen-fancy::before { content: "=EF=96=AC"; }

.fa-pen-nib::before { content: "=EF=96=AD"; }

.fa-pen-ruler::before, .fa-pencil-ruler::before { content: "=EF=96=AE"; }

.fa-edit::before, .fa-pen-to-square::before { content: "=EF=81=84"; }

.fa-pencil-alt::before, .fa-pencil::before { content: "=EF=8C=83"; }

.fa-people-arrows-left-right::before, .fa-people-arrows::before { content: =
"=EE=81=A8"; }

.fa-people-carry-box::before, .fa-people-carry::before { content: "=EF=93=
=8E"; }

.fa-pepper-hot::before { content: "=EF=A0=96"; }

.fa-percent::before, .fa-percentage::before { content: "%"; }

.fa-male::before, .fa-person::before { content: "=EF=86=83"; }

.fa-biking::before, .fa-person-biking::before { content: "=EF=A1=8A"; }

.fa-person-booth::before { content: "=EF=9D=96"; }

.fa-diagnoses::before, .fa-person-dots-from-line::before { content: "=EF=91=
=B0"; }

.fa-female::before, .fa-person-dress::before { content: "=EF=86=82"; }

.fa-hiking::before, .fa-person-hiking::before { content: "=EF=9B=AC"; }

.fa-person-praying::before, .fa-pray::before { content: "=EF=9A=83"; }

.fa-person-running::before, .fa-running::before { content: "=EF=9C=8C"; }

.fa-person-skating::before, .fa-skating::before { content: "=EF=9F=85"; }

.fa-person-skiing::before, .fa-skiing::before { content: "=EF=9F=89"; }

.fa-person-skiing-nordic::before, .fa-skiing-nordic::before { content: "=EF=
=9F=8A"; }

.fa-person-snowboarding::before, .fa-snowboarding::before { content: "=EF=
=9F=8E"; }

.fa-person-swimming::before, .fa-swimmer::before { content: "=EF=97=84"; }

.fa-person-walking::before, .fa-walking::before { content: "=EF=95=94"; }

.fa-blind::before, .fa-person-walking-with-cane::before { content: "=EF=8A=
=9D"; }

.fa-peseta-sign::before { content: "=EE=88=A1"; }

.fa-peso-sign::before { content: "=EE=88=A2"; }

.fa-phone::before { content: "=EF=82=95"; }

.fa-phone-alt::before, .fa-phone-flip::before { content: "=EF=A1=B9"; }

.fa-phone-slash::before { content: "=EF=8F=9D"; }

.fa-phone-volume::before, .fa-volume-control-phone::before { content: "=EF=
=8A=A0"; }

.fa-photo-film::before, .fa-photo-video::before { content: "=EF=A1=BC"; }

.fa-piggy-bank::before { content: "=EF=93=93"; }

.fa-pills::before { content: "=EF=92=84"; }

.fa-pizza-slice::before { content: "=EF=A0=98"; }

.fa-place-of-worship::before { content: "=EF=99=BF"; }

.fa-plane::before { content: "=EF=81=B2"; }

.fa-plane-arrival::before { content: "=EF=96=AF"; }

.fa-plane-departure::before { content: "=EF=96=B0"; }

.fa-plane-slash::before { content: "=EE=81=A9"; }

.fa-play::before { content: "=EF=81=8B"; }

.fa-plug::before { content: "=EF=87=A6"; }

.fa-add::before, .fa-plus::before { content: "+"; }

.fa-plus-minus::before { content: "=EE=90=BC"; }

.fa-podcast::before { content: "=EF=8B=8E"; }

.fa-poo::before { content: "=EF=8B=BE"; }

.fa-poo-bolt::before, .fa-poo-storm::before { content: "=EF=9D=9A"; }

.fa-poop::before { content: "=EF=98=99"; }

.fa-power-off::before { content: "=EF=80=91"; }

.fa-prescription::before { content: "=EF=96=B1"; }

.fa-prescription-bottle::before { content: "=EF=92=85"; }

.fa-prescription-bottle-alt::before, .fa-prescription-bottle-medical::befor=
e { content: "=EF=92=86"; }

.fa-print::before { content: "=EF=80=AF"; }

.fa-pump-medical::before { content: "=EE=81=AA"; }

.fa-pump-soap::before { content: "=EE=81=AB"; }

.fa-puzzle-piece::before { content: "=EF=84=AE"; }

.fa-q::before { content: "Q"; }

.fa-qrcode::before { content: "=EF=80=A9"; }

.fa-question::before { content: "?"; }

.fa-quote-left-alt::before, .fa-quote-left::before { content: "=EF=84=8D"; =
}

.fa-quote-right-alt::before, .fa-quote-right::before { content: "=EF=84=8E"=
; }

.fa-r::before { content: "R"; }

.fa-radiation::before { content: "=EF=9E=B9"; }

.fa-rainbow::before { content: "=EF=9D=9B"; }

.fa-receipt::before { content: "=EF=95=83"; }

.fa-record-vinyl::before { content: "=EF=A3=99"; }

.fa-ad::before, .fa-rectangle-ad::before { content: "=EF=99=81"; }

.fa-list-alt::before, .fa-rectangle-list::before { content: "=EF=80=A2"; }

.fa-rectangle-times::before, .fa-rectangle-xmark::before, .fa-times-rectang=
le::before, .fa-window-close::before { content: "=EF=90=90"; }

.fa-recycle::before { content: "=EF=86=B8"; }

.fa-registered::before { content: "=EF=89=9D"; }

.fa-repeat::before { content: "=EF=8D=A3"; }

.fa-mail-reply::before, .fa-reply::before { content: "=EF=8F=A5"; }

.fa-mail-reply-all::before, .fa-reply-all::before { content: "=EF=84=A2"; }

.fa-republican::before { content: "=EF=9D=9E"; }

.fa-restroom::before { content: "=EF=9E=BD"; }

.fa-retweet::before { content: "=EF=81=B9"; }

.fa-ribbon::before { content: "=EF=93=96"; }

.fa-right-from-bracket::before, .fa-sign-out-alt::before { content: "=EF=8B=
=B5"; }

.fa-exchange-alt::before, .fa-right-left::before { content: "=EF=8D=A2"; }

.fa-long-arrow-alt-right::before, .fa-right-long::before { content: "=EF=8C=
=8B"; }

.fa-right-to-bracket::before, .fa-sign-in-alt::before { content: "=EF=8B=B6=
"; }

.fa-ring::before { content: "=EF=9C=8B"; }

.fa-road::before { content: "=EF=80=98"; }

.fa-robot::before { content: "=EF=95=84"; }

.fa-rocket::before { content: "=EF=84=B5"; }

.fa-rotate::before, .fa-sync-alt::before { content: "=EF=8B=B1"; }

.fa-rotate-back::before, .fa-rotate-backward::before, .fa-rotate-left::befo=
re, .fa-undo-alt::before { content: "=EF=8B=AA"; }

.fa-redo-alt::before, .fa-rotate-forward::before, .fa-rotate-right::before =
{ content: "=EF=8B=B9"; }

.fa-route::before { content: "=EF=93=97"; }

.fa-feed::before, .fa-rss::before { content: "=EF=82=9E"; }

.fa-rouble::before, .fa-rub::before, .fa-ruble-sign::before, .fa-ruble::bef=
ore { content: "=EF=85=98"; }

.fa-ruler::before { content: "=EF=95=85"; }

.fa-ruler-combined::before { content: "=EF=95=86"; }

.fa-ruler-horizontal::before { content: "=EF=95=87"; }

.fa-ruler-vertical::before { content: "=EF=95=88"; }

.fa-rupee-sign::before, .fa-rupee::before { content: "=EF=85=96"; }

.fa-rupiah-sign::before { content: "=EE=88=BD"; }

.fa-s::before { content: "S"; }

.fa-sailboat::before { content: "=EE=91=85"; }

.fa-satellite::before { content: "=EF=9E=BF"; }

.fa-satellite-dish::before { content: "=EF=9F=80"; }

.fa-balance-scale::before, .fa-scale-balanced::before { content: "=EF=89=8E=
"; }

.fa-balance-scale-left::before, .fa-scale-unbalanced::before { content: "=
=EF=94=95"; }

.fa-balance-scale-right::before, .fa-scale-unbalanced-flip::before { conten=
t: "=EF=94=96"; }

.fa-school::before { content: "=EF=95=89"; }

.fa-cut::before, .fa-scissors::before { content: "=EF=83=84"; }

.fa-screwdriver::before { content: "=EF=95=8A"; }

.fa-screwdriver-wrench::before, .fa-tools::before { content: "=EF=9F=99"; }

.fa-scroll::before { content: "=EF=9C=8E"; }

.fa-scroll-torah::before, .fa-torah::before { content: "=EF=9A=A0"; }

.fa-sd-card::before { content: "=EF=9F=82"; }

.fa-section::before { content: "=EE=91=87"; }

.fa-seedling::before, .fa-sprout::before { content: "=EF=93=98"; }

.fa-server::before { content: "=EF=88=B3"; }

.fa-shapes::before, .fa-triangle-circle-square::before { content: "=EF=98=
=9F"; }

.fa-arrow-turn-right::before, .fa-mail-forward::before, .fa-share::before {=
 content: "=EF=81=A4"; }

.fa-share-from-square::before, .fa-share-square::before { content: "=EF=85=
=8D"; }

.fa-share-alt::before, .fa-share-nodes::before { content: "=EF=87=A0"; }

.fa-ils::before, .fa-shekel-sign::before, .fa-shekel::before, .fa-sheqel-si=
gn::before, .fa-sheqel::before { content: "=EF=88=8B"; }

.fa-shield::before { content: "=EF=84=B2"; }

.fa-shield-alt::before, .fa-shield-blank::before { content: "=EF=8F=AD"; }

.fa-shield-virus::before { content: "=EE=81=AC"; }

.fa-ship::before { content: "=EF=88=9A"; }

.fa-shirt::before, .fa-t-shirt::before, .fa-tshirt::before { content: "=EF=
=95=93"; }

.fa-shoe-prints::before { content: "=EF=95=8B"; }

.fa-shop::before, .fa-store-alt::before { content: "=EF=95=8F"; }

.fa-shop-slash::before, .fa-store-alt-slash::before { content: "=EE=81=B0";=
 }

.fa-shower::before { content: "=EF=8B=8C"; }

.fa-shrimp::before { content: "=EE=91=88"; }

.fa-random::before, .fa-shuffle::before { content: "=EF=81=B4"; }

.fa-shuttle-space::before, .fa-space-shuttle::before { content: "=EF=86=97"=
; }

.fa-sign-hanging::before, .fa-sign::before { content: "=EF=93=99"; }

.fa-signal-5::before, .fa-signal-perfect::before, .fa-signal::before { cont=
ent: "=EF=80=92"; }

.fa-signature::before { content: "=EF=96=B7"; }

.fa-map-signs::before, .fa-signs-post::before { content: "=EF=89=B7"; }

.fa-sim-card::before { content: "=EF=9F=84"; }

.fa-sink::before { content: "=EE=81=AD"; }

.fa-sitemap::before { content: "=EF=83=A8"; }

.fa-skull::before { content: "=EF=95=8C"; }

.fa-skull-crossbones::before { content: "=EF=9C=94"; }

.fa-slash::before { content: "=EF=9C=95"; }

.fa-sleigh::before { content: "=EF=9F=8C"; }

.fa-sliders-h::before, .fa-sliders::before { content: "=EF=87=9E"; }

.fa-smog::before { content: "=EF=9D=9F"; }

.fa-smoking::before { content: "=EF=92=8D"; }

.fa-snowflake::before { content: "=EF=8B=9C"; }

.fa-snowman::before { content: "=EF=9F=90"; }

.fa-snowplow::before { content: "=EF=9F=92"; }

.fa-soap::before { content: "=EE=81=AE"; }

.fa-socks::before { content: "=EF=9A=96"; }

.fa-solar-panel::before { content: "=EF=96=BA"; }

.fa-sort::before, .fa-unsorted::before { content: "=EF=83=9C"; }

.fa-sort-desc::before, .fa-sort-down::before { content: "=EF=83=9D"; }

.fa-sort-asc::before, .fa-sort-up::before { content: "=EF=83=9E"; }

.fa-spa::before { content: "=EF=96=BB"; }

.fa-pastafarianism::before, .fa-spaghetti-monster-flying::before { content:=
 "=EF=99=BB"; }

.fa-spell-check::before { content: "=EF=A2=91"; }

.fa-spider::before { content: "=EF=9C=97"; }

.fa-spinner::before { content: "=EF=84=90"; }

.fa-splotch::before { content: "=EF=96=BC"; }

.fa-spoon::before, .fa-utensil-spoon::before { content: "=EF=8B=A5"; }

.fa-spray-can::before { content: "=EF=96=BD"; }

.fa-air-freshener::before, .fa-spray-can-sparkles::before { content: "=EF=
=97=90"; }

.fa-square::before { content: "=EF=83=88"; }

.fa-external-link-square::before, .fa-square-arrow-up-right::before { conte=
nt: "=EF=85=8C"; }

.fa-caret-square-down::before, .fa-square-caret-down::before { content: "=
=EF=85=90"; }

.fa-caret-square-left::before, .fa-square-caret-left::before { content: "=
=EF=86=91"; }

.fa-caret-square-right::before, .fa-square-caret-right::before { content: "=
=EF=85=92"; }

.fa-caret-square-up::before, .fa-square-caret-up::before { content: "=EF=85=
=91"; }

.fa-check-square::before, .fa-square-check::before { content: "=EF=85=8A"; =
}

.fa-envelope-square::before, .fa-square-envelope::before { content: "=EF=86=
=99"; }

.fa-square-full::before { content: "=EF=91=9C"; }

.fa-h-square::before, .fa-square-h::before { content: "=EF=83=BD"; }

.fa-minus-square::before, .fa-square-minus::before { content: "=EF=85=86"; =
}

.fa-parking::before, .fa-square-parking::before { content: "=EF=95=80"; }

.fa-pen-square::before, .fa-pencil-square::before, .fa-square-pen::before {=
 content: "=EF=85=8B"; }

.fa-phone-square::before, .fa-square-phone::before { content: "=EF=82=98"; =
}

.fa-phone-square-alt::before, .fa-square-phone-flip::before { content: "=EF=
=A1=BB"; }

.fa-plus-square::before, .fa-square-plus::before { content: "=EF=83=BE"; }

.fa-poll-h::before, .fa-square-poll-horizontal::before { content: "=EF=9A=
=82"; }

.fa-poll::before, .fa-square-poll-vertical::before { content: "=EF=9A=81"; =
}

.fa-square-root-alt::before, .fa-square-root-variable::before { content: "=
=EF=9A=98"; }

.fa-rss-square::before, .fa-square-rss::before { content: "=EF=85=83"; }

.fa-share-alt-square::before, .fa-square-share-nodes::before { content: "=
=EF=87=A1"; }

.fa-external-link-square-alt::before, .fa-square-up-right::before { content=
: "=EF=8D=A0"; }

.fa-square-xmark::before, .fa-times-square::before, .fa-xmark-square::befor=
e { content: "=EF=8B=93"; }

.fa-stairs::before { content: "=EE=8A=89"; }

.fa-stamp::before { content: "=EF=96=BF"; }

.fa-star::before { content: "=EF=80=85"; }

.fa-star-and-crescent::before { content: "=EF=9A=99"; }

.fa-star-half::before { content: "=EF=82=89"; }

.fa-star-half-alt::before, .fa-star-half-stroke::before { content: "=EF=97=
=80"; }

.fa-star-of-david::before { content: "=EF=9A=9A"; }

.fa-star-of-life::before { content: "=EF=98=A1"; }

.fa-gbp::before, .fa-pound-sign::before, .fa-sterling-sign::before { conten=
t: "=EF=85=94"; }

.fa-stethoscope::before { content: "=EF=83=B1"; }

.fa-stop::before { content: "=EF=81=8D"; }

.fa-stopwatch::before { content: "=EF=8B=B2"; }

.fa-stopwatch-20::before { content: "=EE=81=AF"; }

.fa-store::before { content: "=EF=95=8E"; }

.fa-store-slash::before { content: "=EE=81=B1"; }

.fa-street-view::before { content: "=EF=88=9D"; }

.fa-strikethrough::before { content: "=EF=83=8C"; }

.fa-stroopwafel::before { content: "=EF=95=91"; }

.fa-subscript::before { content: "=EF=84=AC"; }

.fa-suitcase::before { content: "=EF=83=B2"; }

.fa-medkit::before, .fa-suitcase-medical::before { content: "=EF=83=BA"; }

.fa-suitcase-rolling::before { content: "=EF=97=81"; }

.fa-sun::before { content: "=EF=86=85"; }

.fa-superscript::before { content: "=EF=84=AB"; }

.fa-swatchbook::before { content: "=EF=97=83"; }

.fa-synagogue::before { content: "=EF=9A=9B"; }

.fa-syringe::before { content: "=EF=92=8E"; }

.fa-t::before { content: "T"; }

.fa-table::before { content: "=EF=83=8E"; }

.fa-table-cells::before, .fa-th::before { content: "=EF=80=8A"; }

.fa-table-cells-large::before, .fa-th-large::before { content: "=EF=80=89";=
 }

.fa-columns::before, .fa-table-columns::before { content: "=EF=83=9B"; }

.fa-table-list::before, .fa-th-list::before { content: "=EF=80=8B"; }

.fa-ping-pong-paddle-ball::before, .fa-table-tennis-paddle-ball::before, .f=
a-table-tennis::before { content: "=EF=91=9D"; }

.fa-tablet-android::before, .fa-tablet::before { content: "=EF=8F=BB"; }

.fa-tablet-button::before { content: "=EF=84=8A"; }

.fa-tablet-alt::before, .fa-tablet-screen-button::before { content: "=EF=8F=
=BA"; }

.fa-tablets::before { content: "=EF=92=90"; }

.fa-digital-tachograph::before, .fa-tachograph-digital::before { content: "=
=EF=95=A6"; }

.fa-tag::before { content: "=EF=80=AB"; }

.fa-tags::before { content: "=EF=80=AC"; }

.fa-tape::before { content: "=EF=93=9B"; }

.fa-cab::before, .fa-taxi::before { content: "=EF=86=BA"; }

.fa-teeth::before { content: "=EF=98=AE"; }

.fa-teeth-open::before { content: "=EF=98=AF"; }

.fa-temperature-0::before, .fa-temperature-empty::before, .fa-thermometer-0=
::before, .fa-thermometer-empty::before { content: "=EF=8B=8B"; }

.fa-temperature-4::before, .fa-temperature-full::before, .fa-thermometer-4:=
:before, .fa-thermometer-full::before { content: "=EF=8B=87"; }

.fa-temperature-2::before, .fa-temperature-half::before, .fa-thermometer-2:=
:before, .fa-thermometer-half::before { content: "=EF=8B=89"; }

.fa-temperature-high::before { content: "=EF=9D=A9"; }

.fa-temperature-low::before { content: "=EF=9D=AB"; }

.fa-temperature-1::before, .fa-temperature-quarter::before, .fa-thermometer=
-1::before, .fa-thermometer-quarter::before { content: "=EF=8B=8A"; }

.fa-temperature-3::before, .fa-temperature-three-quarters::before, .fa-ther=
mometer-3::before, .fa-thermometer-three-quarters::before { content: "=EF=
=8B=88"; }

.fa-tenge-sign::before, .fa-tenge::before { content: "=EF=9F=97"; }

.fa-terminal::before { content: "=EF=84=A0"; }

.fa-text-height::before { content: "=EF=80=B4"; }

.fa-remove-format::before, .fa-text-slash::before { content: "=EF=A1=BD"; }

.fa-text-width::before { content: "=EF=80=B5"; }

.fa-thermometer::before { content: "=EF=92=91"; }

.fa-thumbs-down::before { content: "=EF=85=A5"; }

.fa-thumbs-up::before { content: "=EF=85=A4"; }

.fa-thumb-tack::before, .fa-thumbtack::before { content: "=EF=82=8D"; }

.fa-ticket::before { content: "=EF=85=85"; }

.fa-ticket-alt::before, .fa-ticket-simple::before { content: "=EF=8F=BF"; }

.fa-timeline::before { content: "=EE=8A=9C"; }

.fa-toggle-off::before { content: "=EF=88=84"; }

.fa-toggle-on::before { content: "=EF=88=85"; }

.fa-toilet::before { content: "=EF=9F=98"; }

.fa-toilet-paper::before { content: "=EF=9C=9E"; }

.fa-toilet-paper-slash::before { content: "=EE=81=B2"; }

.fa-toolbox::before { content: "=EF=95=92"; }

.fa-tooth::before { content: "=EF=97=89"; }

.fa-torii-gate::before { content: "=EF=9A=A1"; }

.fa-broadcast-tower::before, .fa-tower-broadcast::before { content: "=EF=94=
=99"; }

.fa-tractor::before { content: "=EF=9C=A2"; }

.fa-trademark::before { content: "=EF=89=9C"; }

.fa-traffic-light::before { content: "=EF=98=B7"; }

.fa-trailer::before { content: "=EE=81=81"; }

.fa-train::before { content: "=EF=88=B8"; }

.fa-subway::before, .fa-train-subway::before { content: "=EF=88=B9"; }

.fa-train-tram::before, .fa-tram::before { content: "=EF=9F=9A"; }

.fa-transgender-alt::before, .fa-transgender::before { content: "=EF=88=A5"=
; }

.fa-trash::before { content: "=EF=87=B8"; }

.fa-trash-arrow-up::before, .fa-trash-restore::before { content: "=EF=A0=A9=
"; }

.fa-trash-alt::before, .fa-trash-can::before { content: "=EF=8B=AD"; }

.fa-trash-can-arrow-up::before, .fa-trash-restore-alt::before { content: "=
=EF=A0=AA"; }

.fa-tree::before { content: "=EF=86=BB"; }

.fa-exclamation-triangle::before, .fa-triangle-exclamation::before, .fa-war=
ning::before { content: "=EF=81=B1"; }

.fa-trophy::before { content: "=EF=82=91"; }

.fa-truck::before { content: "=EF=83=91"; }

.fa-shipping-fast::before, .fa-truck-fast::before { content: "=EF=92=8B"; }

.fa-ambulance::before, .fa-truck-medical::before { content: "=EF=83=B9"; }

.fa-truck-monster::before { content: "=EF=98=BB"; }

.fa-truck-moving::before { content: "=EF=93=9F"; }

.fa-truck-pickup::before { content: "=EF=98=BC"; }

.fa-truck-loading::before, .fa-truck-ramp-box::before { content: "=EF=93=9E=
"; }

.fa-teletype::before, .fa-tty::before { content: "=EF=87=A4"; }

.fa-try::before, .fa-turkish-lira-sign::before, .fa-turkish-lira::before { =
content: "=EE=8A=BB"; }

.fa-level-down-alt::before, .fa-turn-down::before { content: "=EF=8E=BE"; }

.fa-level-up-alt::before, .fa-turn-up::before { content: "=EF=8E=BF"; }

.fa-television::before, .fa-tv-alt::before, .fa-tv::before { content: "=EF=
=89=AC"; }

.fa-u::before { content: "U"; }

.fa-umbrella::before { content: "=EF=83=A9"; }

.fa-umbrella-beach::before { content: "=EF=97=8A"; }

.fa-underline::before { content: "=EF=83=8D"; }

.fa-universal-access::before { content: "=EF=8A=9A"; }

.fa-unlock::before { content: "=EF=82=9C"; }

.fa-unlock-alt::before, .fa-unlock-keyhole::before { content: "=EF=84=BE"; =
}

.fa-arrows-alt-v::before, .fa-up-down::before { content: "=EF=8C=B8"; }

.fa-arrows-alt::before, .fa-up-down-left-right::before { content: "=EF=82=
=B2"; }

.fa-long-arrow-alt-up::before, .fa-up-long::before { content: "=EF=8C=8C"; =
}

.fa-expand-alt::before, .fa-up-right-and-down-left-from-center::before { co=
ntent: "=EF=90=A4"; }

.fa-external-link-alt::before, .fa-up-right-from-square::before { content: =
"=EF=8D=9D"; }

.fa-upload::before { content: "=EF=82=93"; }

.fa-user::before { content: "=EF=80=87"; }

.fa-user-astronaut::before { content: "=EF=93=BB"; }

.fa-user-check::before { content: "=EF=93=BC"; }

.fa-user-clock::before { content: "=EF=93=BD"; }

.fa-user-doctor::before, .fa-user-md::before { content: "=EF=83=B0"; }

.fa-user-cog::before, .fa-user-gear::before { content: "=EF=93=BE"; }

.fa-user-graduate::before { content: "=EF=94=81"; }

.fa-user-friends::before, .fa-user-group::before { content: "=EF=94=80"; }

.fa-user-injured::before { content: "=EF=9C=A8"; }

.fa-user-alt::before, .fa-user-large::before { content: "=EF=90=86"; }

.fa-user-alt-slash::before, .fa-user-large-slash::before { content: "=EF=93=
=BA"; }

.fa-user-lock::before { content: "=EF=94=82"; }

.fa-user-minus::before { content: "=EF=94=83"; }

.fa-user-ninja::before { content: "=EF=94=84"; }

.fa-user-nurse::before { content: "=EF=A0=AF"; }

.fa-user-edit::before, .fa-user-pen::before { content: "=EF=93=BF"; }

.fa-user-plus::before { content: "=EF=88=B4"; }

.fa-user-secret::before { content: "=EF=88=9B"; }

.fa-user-shield::before { content: "=EF=94=85"; }

.fa-user-slash::before { content: "=EF=94=86"; }

.fa-user-tag::before { content: "=EF=94=87"; }

.fa-user-tie::before { content: "=EF=94=88"; }

.fa-user-times::before, .fa-user-xmark::before { content: "=EF=88=B5"; }

.fa-users::before { content: "=EF=83=80"; }

.fa-users-cog::before, .fa-users-gear::before { content: "=EF=94=89"; }

.fa-users-slash::before { content: "=EE=81=B3"; }

.fa-cutlery::before, .fa-utensils::before { content: "=EF=8B=A7"; }

.fa-v::before { content: "V"; }

.fa-shuttle-van::before, .fa-van-shuttle::before { content: "=EF=96=B6"; }

.fa-vault::before { content: "=EE=8B=85"; }

.fa-vector-square::before { content: "=EF=97=8B"; }

.fa-venus::before { content: "=EF=88=A1"; }

.fa-venus-double::before { content: "=EF=88=A6"; }

.fa-venus-mars::before { content: "=EF=88=A8"; }

.fa-vest::before { content: "=EE=82=85"; }

.fa-vest-patches::before { content: "=EE=82=86"; }

.fa-vial::before { content: "=EF=92=92"; }

.fa-vials::before { content: "=EF=92=93"; }

.fa-video-camera::before, .fa-video::before { content: "=EF=80=BD"; }

.fa-video-slash::before { content: "=EF=93=A2"; }

.fa-vihara::before { content: "=EF=9A=A7"; }

.fa-virus::before { content: "=EE=81=B4"; }

.fa-virus-covid::before { content: "=EE=92=A8"; }

.fa-virus-covid-slash::before { content: "=EE=92=A9"; }

.fa-virus-slash::before { content: "=EE=81=B5"; }

.fa-viruses::before { content: "=EE=81=B6"; }

.fa-voicemail::before { content: "=EF=A2=97"; }

.fa-volleyball-ball::before, .fa-volleyball::before { content: "=EF=91=9F";=
 }

.fa-volume-high::before, .fa-volume-up::before { content: "=EF=80=A8"; }

.fa-volume-down::before, .fa-volume-low::before { content: "=EF=80=A7"; }

.fa-volume-off::before { content: "=EF=80=A6"; }

.fa-volume-mute::before, .fa-volume-times::before, .fa-volume-xmark::before=
 { content: "=EF=9A=A9"; }

.fa-vr-cardboard::before { content: "=EF=9C=A9"; }

.fa-w::before { content: "W"; }

.fa-wallet::before { content: "=EF=95=95"; }

.fa-magic::before, .fa-wand-magic::before { content: "=EF=83=90"; }

.fa-magic-wand-sparkles::before, .fa-wand-magic-sparkles::before { content:=
 "=EE=8B=8A"; }

.fa-wand-sparkles::before { content: "=EF=9C=AB"; }

.fa-warehouse::before { content: "=EF=92=94"; }

.fa-water::before { content: "=EF=9D=B3"; }

.fa-ladder-water::before, .fa-swimming-pool::before, .fa-water-ladder::befo=
re { content: "=EF=97=85"; }

.fa-wave-square::before { content: "=EF=A0=BE"; }

.fa-weight-hanging::before { content: "=EF=97=8D"; }

.fa-weight-scale::before, .fa-weight::before { content: "=EF=92=96"; }

.fa-wheelchair::before { content: "=EF=86=93"; }

.fa-glass-whiskey::before, .fa-whiskey-glass::before { content: "=EF=9E=A0"=
; }

.fa-wifi-3::before, .fa-wifi-strong::before, .fa-wifi::before { content: "=
=EF=87=AB"; }

.fa-wind::before { content: "=EF=9C=AE"; }

.fa-window-maximize::before { content: "=EF=8B=90"; }

.fa-window-minimize::before { content: "=EF=8B=91"; }

.fa-window-restore::before { content: "=EF=8B=92"; }

.fa-wine-bottle::before { content: "=EF=9C=AF"; }

.fa-wine-glass::before { content: "=EF=93=A3"; }

.fa-wine-glass-alt::before, .fa-wine-glass-empty::before { content: "=EF=97=
=8E"; }

.fa-krw::before, .fa-won-sign::before, .fa-won::before { content: "=EF=85=
=99"; }

.fa-wrench::before { content: "=EF=82=AD"; }

.fa-x::before { content: "X"; }

.fa-x-ray::before { content: "=EF=92=97"; }

.fa-close::before, .fa-multiply::before, .fa-remove::before, .fa-times::bef=
ore, .fa-xmark::before { content: "=EF=80=8D"; }

.fa-y::before { content: "Y"; }

.fa-cny::before, .fa-jpy::before, .fa-rmb::before, .fa-yen-sign::before, .f=
a-yen::before { content: "=EF=85=97"; }

.fa-yin-yang::before { content: "=EF=9A=AD"; }

.fa-z::before { content: "Z"; }

.fa-sr-only, .fa-sr-only-focusable:not(:focus), .sr-only, .sr-only-focusabl=
e:not(:focus) { position: absolute; width: 1px; height: 1px; padding: 0px; =
margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space=
: nowrap; border-width: 0px; }

:host, :root { --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands"; =
}

@font-face { font-family: "Font Awesome 6 Brands"; font-style: normal; font=
-weight: 400; font-display: block; src: url("../webfonts/fa-brands-400.woff=
2") format("woff2"), url("../webfonts/fa-brands-400.ttf") format("truetype"=
); }

.fa-brands, .fab { font-family: "Font Awesome 6 Brands"; font-weight: 400; =
}

.fa-42-group::before, .fa-innosoft::before { content: "=EE=82=80"; }

.fa-500px::before { content: "=EF=89=AE"; }

.fa-accessible-icon::before { content: "=EF=8D=A8"; }

.fa-accusoft::before { content: "=EF=8D=A9"; }

.fa-adn::before { content: "=EF=85=B0"; }

.fa-adversal::before { content: "=EF=8D=AA"; }

.fa-affiliatetheme::before { content: "=EF=8D=AB"; }

.fa-airbnb::before { content: "=EF=A0=B4"; }

.fa-algolia::before { content: "=EF=8D=AC"; }

.fa-alipay::before { content: "=EF=99=82"; }

.fa-amazon::before { content: "=EF=89=B0"; }

.fa-amazon-pay::before { content: "=EF=90=AC"; }

.fa-amilia::before { content: "=EF=8D=AD"; }

.fa-android::before { content: "=EF=85=BB"; }

.fa-angellist::before { content: "=EF=88=89"; }

.fa-angrycreative::before { content: "=EF=8D=AE"; }

.fa-angular::before { content: "=EF=90=A0"; }

.fa-app-store::before { content: "=EF=8D=AF"; }

.fa-app-store-ios::before { content: "=EF=8D=B0"; }

.fa-apper::before { content: "=EF=8D=B1"; }

.fa-apple::before { content: "=EF=85=B9"; }

.fa-apple-pay::before { content: "=EF=90=95"; }

.fa-artstation::before { content: "=EF=9D=BA"; }

.fa-asymmetrik::before { content: "=EF=8D=B2"; }

.fa-atlassian::before { content: "=EF=9D=BB"; }

.fa-audible::before { content: "=EF=8D=B3"; }

.fa-autoprefixer::before { content: "=EF=90=9C"; }

.fa-avianex::before { content: "=EF=8D=B4"; }

.fa-aviato::before { content: "=EF=90=A1"; }

.fa-aws::before { content: "=EF=8D=B5"; }

.fa-bandcamp::before { content: "=EF=8B=95"; }

.fa-battle-net::before { content: "=EF=A0=B5"; }

.fa-behance::before { content: "=EF=86=B4"; }

.fa-behance-square::before { content: "=EF=86=B5"; }

.fa-bilibili::before { content: "=EE=8F=99"; }

.fa-bimobject::before { content: "=EF=8D=B8"; }

.fa-bitbucket::before { content: "=EF=85=B1"; }

.fa-bitcoin::before { content: "=EF=8D=B9"; }

.fa-bity::before { content: "=EF=8D=BA"; }

.fa-black-tie::before { content: "=EF=89=BE"; }

.fa-blackberry::before { content: "=EF=8D=BB"; }

.fa-blogger::before { content: "=EF=8D=BC"; }

.fa-blogger-b::before { content: "=EF=8D=BD"; }

.fa-bluetooth::before { content: "=EF=8A=93"; }

.fa-bluetooth-b::before { content: "=EF=8A=94"; }

.fa-bootstrap::before { content: "=EF=A0=B6"; }

.fa-bots::before { content: "=EE=8D=80"; }

.fa-btc::before { content: "=EF=85=9A"; }

.fa-buffer::before { content: "=EF=A0=B7"; }

.fa-buromobelexperte::before { content: "=EF=8D=BF"; }

.fa-buy-n-large::before { content: "=EF=A2=A6"; }

.fa-buysellads::before { content: "=EF=88=8D"; }

.fa-canadian-maple-leaf::before { content: "=EF=9E=85"; }

.fa-cc-amazon-pay::before { content: "=EF=90=AD"; }

.fa-cc-amex::before { content: "=EF=87=B3"; }

.fa-cc-apple-pay::before { content: "=EF=90=96"; }

.fa-cc-diners-club::before { content: "=EF=89=8C"; }

.fa-cc-discover::before { content: "=EF=87=B2"; }

.fa-cc-jcb::before { content: "=EF=89=8B"; }

.fa-cc-mastercard::before { content: "=EF=87=B1"; }

.fa-cc-paypal::before { content: "=EF=87=B4"; }

.fa-cc-stripe::before { content: "=EF=87=B5"; }

.fa-cc-visa::before { content: "=EF=87=B0"; }

.fa-centercode::before { content: "=EF=8E=80"; }

.fa-centos::before { content: "=EF=9E=89"; }

.fa-chrome::before { content: "=EF=89=A8"; }

.fa-chromecast::before { content: "=EF=A0=B8"; }

.fa-cloudflare::before { content: "=EE=81=BD"; }

.fa-cloudscale::before { content: "=EF=8E=83"; }

.fa-cloudsmith::before { content: "=EF=8E=84"; }

.fa-cloudversify::before { content: "=EF=8E=85"; }

.fa-cmplid::before { content: "=EE=8D=A0"; }

.fa-codepen::before { content: "=EF=87=8B"; }

.fa-codiepie::before { content: "=EF=8A=84"; }

.fa-confluence::before { content: "=EF=9E=8D"; }

.fa-connectdevelop::before { content: "=EF=88=8E"; }

.fa-contao::before { content: "=EF=89=AD"; }

.fa-cotton-bureau::before { content: "=EF=A2=9E"; }

.fa-cpanel::before { content: "=EF=8E=88"; }

.fa-creative-commons::before { content: "=EF=89=9E"; }

.fa-creative-commons-by::before { content: "=EF=93=A7"; }

.fa-creative-commons-nc::before { content: "=EF=93=A8"; }

.fa-creative-commons-nc-eu::before { content: "=EF=93=A9"; }

.fa-creative-commons-nc-jp::before { content: "=EF=93=AA"; }

.fa-creative-commons-nd::before { content: "=EF=93=AB"; }

.fa-creative-commons-pd::before { content: "=EF=93=AC"; }

.fa-creative-commons-pd-alt::before { content: "=EF=93=AD"; }

.fa-creative-commons-remix::before { content: "=EF=93=AE"; }

.fa-creative-commons-sa::before { content: "=EF=93=AF"; }

.fa-creative-commons-sampling::before { content: "=EF=93=B0"; }

.fa-creative-commons-sampling-plus::before { content: "=EF=93=B1"; }

.fa-creative-commons-share::before { content: "=EF=93=B2"; }

.fa-creative-commons-zero::before { content: "=EF=93=B3"; }

.fa-critical-role::before { content: "=EF=9B=89"; }

.fa-css3::before { content: "=EF=84=BC"; }

.fa-css3-alt::before { content: "=EF=8E=8B"; }

.fa-cuttlefish::before { content: "=EF=8E=8C"; }

.fa-d-and-d::before { content: "=EF=8E=8D"; }

.fa-d-and-d-beyond::before { content: "=EF=9B=8A"; }

.fa-dailymotion::before { content: "=EE=81=92"; }

.fa-dashcube::before { content: "=EF=88=90"; }

.fa-deezer::before { content: "=EE=81=B7"; }

.fa-delicious::before { content: "=EF=86=A5"; }

.fa-deploydog::before { content: "=EF=8E=8E"; }

.fa-deskpro::before { content: "=EF=8E=8F"; }

.fa-dev::before { content: "=EF=9B=8C"; }

.fa-deviantart::before { content: "=EF=86=BD"; }

.fa-dhl::before { content: "=EF=9E=90"; }

.fa-diaspora::before { content: "=EF=9E=91"; }

.fa-digg::before { content: "=EF=86=A6"; }

.fa-digital-ocean::before { content: "=EF=8E=91"; }

.fa-discord::before { content: "=EF=8E=92"; }

.fa-discourse::before { content: "=EF=8E=93"; }

.fa-dochub::before { content: "=EF=8E=94"; }

.fa-docker::before { content: "=EF=8E=95"; }

.fa-draft2digital::before { content: "=EF=8E=96"; }

.fa-dribbble::before { content: "=EF=85=BD"; }

.fa-dribbble-square::before { content: "=EF=8E=97"; }

.fa-dropbox::before { content: "=EF=85=AB"; }

.fa-drupal::before { content: "=EF=86=A9"; }

.fa-dyalog::before { content: "=EF=8E=99"; }

.fa-earlybirds::before { content: "=EF=8E=9A"; }

.fa-ebay::before { content: "=EF=93=B4"; }

.fa-edge::before { content: "=EF=8A=82"; }

.fa-edge-legacy::before { content: "=EE=81=B8"; }

.fa-elementor::before { content: "=EF=90=B0"; }

.fa-ello::before { content: "=EF=97=B1"; }

.fa-ember::before { content: "=EF=90=A3"; }

.fa-empire::before { content: "=EF=87=91"; }

.fa-envira::before { content: "=EF=8A=99"; }

.fa-erlang::before { content: "=EF=8E=9D"; }

.fa-ethereum::before { content: "=EF=90=AE"; }

.fa-etsy::before { content: "=EF=8B=97"; }

.fa-evernote::before { content: "=EF=A0=B9"; }

.fa-expeditedssl::before { content: "=EF=88=BE"; }

.fa-facebook::before { content: "=EF=82=9A"; }

.fa-facebook-f::before { content: "=EF=8E=9E"; }

.fa-facebook-messenger::before { content: "=EF=8E=9F"; }

.fa-facebook-square::before { content: "=EF=82=82"; }

.fa-fantasy-flight-games::before { content: "=EF=9B=9C"; }

.fa-fedex::before { content: "=EF=9E=97"; }

.fa-fedora::before { content: "=EF=9E=98"; }

.fa-figma::before { content: "=EF=9E=99"; }

.fa-firefox::before { content: "=EF=89=A9"; }

.fa-firefox-browser::before { content: "=EE=80=87"; }

.fa-first-order::before { content: "=EF=8A=B0"; }

.fa-first-order-alt::before { content: "=EF=94=8A"; }

.fa-firstdraft::before { content: "=EF=8E=A1"; }

.fa-flickr::before { content: "=EF=85=AE"; }

.fa-flipboard::before { content: "=EF=91=8D"; }

.fa-fly::before { content: "=EF=90=97"; }

.fa-font-awesome-flag::before, .fa-font-awesome-logo-full::before, .fa-font=
-awesome::before { content: "=EF=8A=B4"; }

.fa-fonticons::before { content: "=EF=8A=80"; }

.fa-fonticons-fi::before { content: "=EF=8E=A2"; }

.fa-fort-awesome::before { content: "=EF=8A=86"; }

.fa-fort-awesome-alt::before { content: "=EF=8E=A3"; }

.fa-forumbee::before { content: "=EF=88=91"; }

.fa-foursquare::before { content: "=EF=86=80"; }

.fa-free-code-camp::before { content: "=EF=8B=85"; }

.fa-freebsd::before { content: "=EF=8E=A4"; }

.fa-fulcrum::before { content: "=EF=94=8B"; }

.fa-galactic-republic::before { content: "=EF=94=8C"; }

.fa-galactic-senate::before { content: "=EF=94=8D"; }

.fa-get-pocket::before { content: "=EF=89=A5"; }

.fa-gg::before { content: "=EF=89=A0"; }

.fa-gg-circle::before { content: "=EF=89=A1"; }

.fa-git::before { content: "=EF=87=93"; }

.fa-git-alt::before { content: "=EF=A1=81"; }

.fa-git-square::before { content: "=EF=87=92"; }

.fa-github::before { content: "=EF=82=9B"; }

.fa-github-alt::before { content: "=EF=84=93"; }

.fa-github-square::before { content: "=EF=82=92"; }

.fa-gitkraken::before { content: "=EF=8E=A6"; }

.fa-gitlab::before { content: "=EF=8A=96"; }

.fa-gitter::before { content: "=EF=90=A6"; }

.fa-glide::before { content: "=EF=8A=A5"; }

.fa-glide-g::before { content: "=EF=8A=A6"; }

.fa-gofore::before { content: "=EF=8E=A7"; }

.fa-golang::before { content: "=EE=90=8F"; }

.fa-goodreads::before { content: "=EF=8E=A8"; }

.fa-goodreads-g::before { content: "=EF=8E=A9"; }

.fa-google::before { content: "=EF=86=A0"; }

.fa-google-drive::before { content: "=EF=8E=AA"; }

.fa-google-pay::before { content: "=EE=81=B9"; }

.fa-google-play::before { content: "=EF=8E=AB"; }

.fa-google-plus::before { content: "=EF=8A=B3"; }

.fa-google-plus-g::before { content: "=EF=83=95"; }

.fa-google-plus-square::before { content: "=EF=83=94"; }

.fa-google-wallet::before { content: "=EF=87=AE"; }

.fa-gratipay::before { content: "=EF=86=84"; }

.fa-grav::before { content: "=EF=8B=96"; }

.fa-gripfire::before { content: "=EF=8E=AC"; }

.fa-grunt::before { content: "=EF=8E=AD"; }

.fa-guilded::before { content: "=EE=81=BE"; }

.fa-gulp::before { content: "=EF=8E=AE"; }

.fa-hacker-news::before { content: "=EF=87=94"; }

.fa-hacker-news-square::before { content: "=EF=8E=AF"; }

.fa-hackerrank::before { content: "=EF=97=B7"; }

.fa-hashnode::before { content: "=EE=92=99"; }

.fa-hips::before { content: "=EF=91=92"; }

.fa-hire-a-helper::before { content: "=EF=8E=B0"; }

.fa-hive::before { content: "=EE=81=BF"; }

.fa-hooli::before { content: "=EF=90=A7"; }

.fa-hornbill::before { content: "=EF=96=92"; }

.fa-hotjar::before { content: "=EF=8E=B1"; }

.fa-houzz::before { content: "=EF=89=BC"; }

.fa-html5::before { content: "=EF=84=BB"; }

.fa-hubspot::before { content: "=EF=8E=B2"; }

.fa-ideal::before { content: "=EE=80=93"; }

.fa-imdb::before { content: "=EF=8B=98"; }

.fa-instagram::before { content: "=EF=85=AD"; }

.fa-instagram-square::before { content: "=EE=81=95"; }

.fa-instalod::before { content: "=EE=82=81"; }

.fa-intercom::before { content: "=EF=9E=AF"; }

.fa-internet-explorer::before { content: "=EF=89=AB"; }

.fa-invision::before { content: "=EF=9E=B0"; }

.fa-ioxhost::before { content: "=EF=88=88"; }

.fa-itch-io::before { content: "=EF=A0=BA"; }

.fa-itunes::before { content: "=EF=8E=B4"; }

.fa-itunes-note::before { content: "=EF=8E=B5"; }

.fa-java::before { content: "=EF=93=A4"; }

.fa-jedi-order::before { content: "=EF=94=8E"; }

.fa-jenkins::before { content: "=EF=8E=B6"; }

.fa-jira::before { content: "=EF=9E=B1"; }

.fa-joget::before { content: "=EF=8E=B7"; }

.fa-joomla::before { content: "=EF=86=AA"; }

.fa-js::before { content: "=EF=8E=B8"; }

.fa-js-square::before { content: "=EF=8E=B9"; }

.fa-jsfiddle::before { content: "=EF=87=8C"; }

.fa-kaggle::before { content: "=EF=97=BA"; }

.fa-keybase::before { content: "=EF=93=B5"; }

.fa-keycdn::before { content: "=EF=8E=BA"; }

.fa-kickstarter::before { content: "=EF=8E=BB"; }

.fa-kickstarter-k::before { content: "=EF=8E=BC"; }

.fa-korvue::before { content: "=EF=90=AF"; }

.fa-laravel::before { content: "=EF=8E=BD"; }

.fa-lastfm::before { content: "=EF=88=82"; }

.fa-lastfm-square::before { content: "=EF=88=83"; }

.fa-leanpub::before { content: "=EF=88=92"; }

.fa-less::before { content: "=EF=90=9D"; }

.fa-line::before { content: "=EF=8F=80"; }

.fa-linkedin::before { content: "=EF=82=8C"; }

.fa-linkedin-in::before { content: "=EF=83=A1"; }

.fa-linode::before { content: "=EF=8A=B8"; }

.fa-linux::before { content: "=EF=85=BC"; }

.fa-lyft::before { content: "=EF=8F=83"; }

.fa-magento::before { content: "=EF=8F=84"; }

.fa-mailchimp::before { content: "=EF=96=9E"; }

.fa-mandalorian::before { content: "=EF=94=8F"; }

.fa-markdown::before { content: "=EF=98=8F"; }

.fa-mastodon::before { content: "=EF=93=B6"; }

.fa-maxcdn::before { content: "=EF=84=B6"; }

.fa-mdb::before { content: "=EF=A3=8A"; }

.fa-medapps::before { content: "=EF=8F=86"; }

.fa-medium-m::before, .fa-medium::before { content: "=EF=88=BA"; }

.fa-medrt::before { content: "=EF=8F=88"; }

.fa-meetup::before { content: "=EF=8B=A0"; }

.fa-megaport::before { content: "=EF=96=A3"; }

.fa-mendeley::before { content: "=EF=9E=B3"; }

.fa-microblog::before { content: "=EE=80=9A"; }

.fa-microsoft::before { content: "=EF=8F=8A"; }

.fa-mix::before { content: "=EF=8F=8B"; }

.fa-mixcloud::before { content: "=EF=8A=89"; }

.fa-mixer::before { content: "=EE=81=96"; }

.fa-mizuni::before { content: "=EF=8F=8C"; }

.fa-modx::before { content: "=EF=8A=85"; }

.fa-monero::before { content: "=EF=8F=90"; }

.fa-napster::before { content: "=EF=8F=92"; }

.fa-neos::before { content: "=EF=98=92"; }

.fa-nimblr::before { content: "=EF=96=A8"; }

.fa-node::before { content: "=EF=90=99"; }

.fa-node-js::before { content: "=EF=8F=93"; }

.fa-npm::before { content: "=EF=8F=94"; }

.fa-ns8::before { content: "=EF=8F=95"; }

.fa-nutritionix::before { content: "=EF=8F=96"; }

.fa-octopus-deploy::before { content: "=EE=82=82"; }

.fa-odnoklassniki::before { content: "=EF=89=A3"; }

.fa-odnoklassniki-square::before { content: "=EF=89=A4"; }

.fa-old-republic::before { content: "=EF=94=90"; }

.fa-opencart::before { content: "=EF=88=BD"; }

.fa-openid::before { content: "=EF=86=9B"; }

.fa-opera::before { content: "=EF=89=AA"; }

.fa-optin-monster::before { content: "=EF=88=BC"; }

.fa-orcid::before { content: "=EF=A3=92"; }

.fa-osi::before { content: "=EF=90=9A"; }

.fa-padlet::before { content: "=EE=92=A0"; }

.fa-page4::before { content: "=EF=8F=97"; }

.fa-pagelines::before { content: "=EF=86=8C"; }

.fa-palfed::before { content: "=EF=8F=98"; }

.fa-patreon::before { content: "=EF=8F=99"; }

.fa-paypal::before { content: "=EF=87=AD"; }

.fa-perbyte::before { content: "=EE=82=83"; }

.fa-periscope::before { content: "=EF=8F=9A"; }

.fa-phabricator::before { content: "=EF=8F=9B"; }

.fa-phoenix-framework::before { content: "=EF=8F=9C"; }

.fa-phoenix-squadron::before { content: "=EF=94=91"; }

.fa-php::before { content: "=EF=91=97"; }

.fa-pied-piper::before { content: "=EF=8A=AE"; }

.fa-pied-piper-alt::before { content: "=EF=86=A8"; }

.fa-pied-piper-hat::before { content: "=EF=93=A5"; }

.fa-pied-piper-pp::before { content: "=EF=86=A7"; }

.fa-pied-piper-square::before { content: "=EE=80=9E"; }

.fa-pinterest::before { content: "=EF=83=92"; }

.fa-pinterest-p::before { content: "=EF=88=B1"; }

.fa-pinterest-square::before { content: "=EF=83=93"; }

.fa-pix::before { content: "=EE=90=BA"; }

.fa-playstation::before { content: "=EF=8F=9F"; }

.fa-product-hunt::before { content: "=EF=8A=88"; }

.fa-pushed::before { content: "=EF=8F=A1"; }

.fa-python::before { content: "=EF=8F=A2"; }

.fa-qq::before { content: "=EF=87=96"; }

.fa-quinscape::before { content: "=EF=91=99"; }

.fa-quora::before { content: "=EF=8B=84"; }

.fa-r-project::before { content: "=EF=93=B7"; }

.fa-raspberry-pi::before { content: "=EF=9E=BB"; }

.fa-ravelry::before { content: "=EF=8B=99"; }

.fa-react::before { content: "=EF=90=9B"; }

.fa-reacteurope::before { content: "=EF=9D=9D"; }

.fa-readme::before { content: "=EF=93=95"; }

.fa-rebel::before { content: "=EF=87=90"; }

.fa-red-river::before { content: "=EF=8F=A3"; }

.fa-reddit::before { content: "=EF=86=A1"; }

.fa-reddit-alien::before { content: "=EF=8A=81"; }

.fa-reddit-square::before { content: "=EF=86=A2"; }

.fa-redhat::before { content: "=EF=9E=BC"; }

.fa-renren::before { content: "=EF=86=8B"; }

.fa-replyd::before { content: "=EF=8F=A6"; }

.fa-researchgate::before { content: "=EF=93=B8"; }

.fa-resolving::before { content: "=EF=8F=A7"; }

.fa-rev::before { content: "=EF=96=B2"; }

.fa-rocketchat::before { content: "=EF=8F=A8"; }

.fa-rockrms::before { content: "=EF=8F=A9"; }

.fa-rust::before { content: "=EE=81=BA"; }

.fa-safari::before { content: "=EF=89=A7"; }

.fa-salesforce::before { content: "=EF=A0=BB"; }

.fa-sass::before { content: "=EF=90=9E"; }

.fa-schlix::before { content: "=EF=8F=AA"; }

.fa-scribd::before { content: "=EF=8A=8A"; }

.fa-searchengin::before { content: "=EF=8F=AB"; }

.fa-sellcast::before { content: "=EF=8B=9A"; }

.fa-sellsy::before { content: "=EF=88=93"; }

.fa-servicestack::before { content: "=EF=8F=AC"; }

.fa-shirtsinbulk::before { content: "=EF=88=94"; }

.fa-shopify::before { content: "=EE=81=97"; }

.fa-shopware::before { content: "=EF=96=B5"; }

.fa-simplybuilt::before { content: "=EF=88=95"; }

.fa-sistrix::before { content: "=EF=8F=AE"; }

.fa-sith::before { content: "=EF=94=92"; }

.fa-sitrox::before { content: "=EE=91=8A"; }

.fa-sketch::before { content: "=EF=9F=86"; }

.fa-skyatlas::before { content: "=EF=88=96"; }

.fa-skype::before { content: "=EF=85=BE"; }

.fa-slack-hash::before, .fa-slack::before { content: "=EF=86=98"; }

.fa-slideshare::before { content: "=EF=87=A7"; }

.fa-snapchat-ghost::before, .fa-snapchat::before { content: "=EF=8A=AB"; }

.fa-snapchat-square::before { content: "=EF=8A=AD"; }

.fa-soundcloud::before { content: "=EF=86=BE"; }

.fa-sourcetree::before { content: "=EF=9F=93"; }

.fa-speakap::before { content: "=EF=8F=B3"; }

.fa-speaker-deck::before { content: "=EF=A0=BC"; }

.fa-spotify::before { content: "=EF=86=BC"; }

.fa-square-font-awesome::before { content: "=EF=90=A5"; }

.fa-font-awesome-alt::before, .fa-square-font-awesome-stroke::before { cont=
ent: "=EF=8D=9C"; }

.fa-squarespace::before { content: "=EF=96=BE"; }

.fa-stack-exchange::before { content: "=EF=86=8D"; }

.fa-stack-overflow::before { content: "=EF=85=AC"; }

.fa-stackpath::before { content: "=EF=A1=82"; }

.fa-staylinked::before { content: "=EF=8F=B5"; }

.fa-steam::before { content: "=EF=86=B6"; }

.fa-steam-square::before { content: "=EF=86=B7"; }

.fa-steam-symbol::before { content: "=EF=8F=B6"; }

.fa-sticker-mule::before { content: "=EF=8F=B7"; }

.fa-strava::before { content: "=EF=90=A8"; }

.fa-stripe::before { content: "=EF=90=A9"; }

.fa-stripe-s::before { content: "=EF=90=AA"; }

.fa-studiovinari::before { content: "=EF=8F=B8"; }

.fa-stumbleupon::before { content: "=EF=86=A4"; }

.fa-stumbleupon-circle::before { content: "=EF=86=A3"; }

.fa-superpowers::before { content: "=EF=8B=9D"; }

.fa-supple::before { content: "=EF=8F=B9"; }

.fa-suse::before { content: "=EF=9F=96"; }

.fa-swift::before { content: "=EF=A3=A1"; }

.fa-symfony::before { content: "=EF=A0=BD"; }

.fa-teamspeak::before { content: "=EF=93=B9"; }

.fa-telegram-plane::before, .fa-telegram::before { content: "=EF=8B=86"; }

.fa-tencent-weibo::before { content: "=EF=87=95"; }

.fa-the-red-yeti::before { content: "=EF=9A=9D"; }

.fa-themeco::before { content: "=EF=97=86"; }

.fa-themeisle::before { content: "=EF=8A=B2"; }

.fa-think-peaks::before { content: "=EF=9C=B1"; }

.fa-tiktok::before { content: "=EE=81=BB"; }

.fa-trade-federation::before { content: "=EF=94=93"; }

.fa-trello::before { content: "=EF=86=81"; }

.fa-tumblr::before { content: "=EF=85=B3"; }

.fa-tumblr-square::before { content: "=EF=85=B4"; }

.fa-twitch::before { content: "=EF=87=A8"; }

.fa-twitter::before { content: "=EF=82=99"; }

.fa-twitter-square::before { content: "=EF=82=81"; }

.fa-typo3::before { content: "=EF=90=AB"; }

.fa-uber::before { content: "=EF=90=82"; }

.fa-ubuntu::before { content: "=EF=9F=9F"; }

.fa-uikit::before { content: "=EF=90=83"; }

.fa-umbraco::before { content: "=EF=A3=A8"; }

.fa-uncharted::before { content: "=EE=82=84"; }

.fa-uniregistry::before { content: "=EF=90=84"; }

.fa-unity::before { content: "=EE=81=89"; }

.fa-unsplash::before { content: "=EE=81=BC"; }

.fa-untappd::before { content: "=EF=90=85"; }

.fa-ups::before { content: "=EF=9F=A0"; }

.fa-usb::before { content: "=EF=8A=87"; }

.fa-usps::before { content: "=EF=9F=A1"; }

.fa-ussunnah::before { content: "=EF=90=87"; }

.fa-vaadin::before { content: "=EF=90=88"; }

.fa-viacoin::before { content: "=EF=88=B7"; }

.fa-viadeo::before { content: "=EF=8A=A9"; }

.fa-viadeo-square::before { content: "=EF=8A=AA"; }

.fa-viber::before { content: "=EF=90=89"; }

.fa-vimeo::before { content: "=EF=90=8A"; }

.fa-vimeo-square::before { content: "=EF=86=94"; }

.fa-vimeo-v::before { content: "=EF=89=BD"; }

.fa-vine::before { content: "=EF=87=8A"; }

.fa-vk::before { content: "=EF=86=89"; }

.fa-vnv::before { content: "=EF=90=8B"; }

.fa-vuejs::before { content: "=EF=90=9F"; }

.fa-watchman-monitoring::before { content: "=EE=82=87"; }

.fa-waze::before { content: "=EF=A0=BF"; }

.fa-weebly::before { content: "=EF=97=8C"; }

.fa-weibo::before { content: "=EF=86=8A"; }

.fa-weixin::before { content: "=EF=87=97"; }

.fa-whatsapp::before { content: "=EF=88=B2"; }

.fa-whatsapp-square::before { content: "=EF=90=8C"; }

.fa-whmcs::before { content: "=EF=90=8D"; }

.fa-wikipedia-w::before { content: "=EF=89=A6"; }

.fa-windows::before { content: "=EF=85=BA"; }

.fa-wirsindhandwerk::before, .fa-wsh::before { content: "=EE=8B=90"; }

.fa-wix::before { content: "=EF=97=8F"; }

.fa-wizards-of-the-coast::before { content: "=EF=9C=B0"; }

.fa-wodu::before { content: "=EE=82=88"; }

.fa-wolf-pack-battalion::before { content: "=EF=94=94"; }

.fa-wordpress::before { content: "=EF=86=9A"; }

.fa-wordpress-simple::before { content: "=EF=90=91"; }

.fa-wpbeginner::before { content: "=EF=8A=97"; }

.fa-wpexplorer::before { content: "=EF=8B=9E"; }

.fa-wpforms::before { content: "=EF=8A=98"; }

.fa-wpressr::before { content: "=EF=8F=A4"; }

.fa-xbox::before { content: "=EF=90=92"; }

.fa-xing::before { content: "=EF=85=A8"; }

.fa-xing-square::before { content: "=EF=85=A9"; }

.fa-y-combinator::before { content: "=EF=88=BB"; }

.fa-yahoo::before { content: "=EF=86=9E"; }

.fa-yammer::before { content: "=EF=A1=80"; }

.fa-yandex::before { content: "=EF=90=93"; }

.fa-yandex-international::before { content: "=EF=90=94"; }

.fa-yarn::before { content: "=EF=9F=A3"; }

.fa-yelp::before { content: "=EF=87=A9"; }

.fa-yoast::before { content: "=EF=8A=B1"; }

.fa-youtube::before { content: "=EF=85=A7"; }

.fa-youtube-square::before { content: "=EF=90=B1"; }

.fa-zhihu::before { content: "=EF=98=BF"; }

:host, :root { --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free"; }

@font-face { font-family: "Font Awesome 6 Free"; font-style: normal; font-w=
eight: 400; font-display: block; src: url("../webfonts/fa-regular-400.woff2=
") format("woff2"), url("../webfonts/fa-regular-400.ttf") format("truetype"=
); }

.fa-regular, .far { font-family: "Font Awesome 6 Free"; font-weight: 400; }

:host, :root { --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free"; }

@font-face { font-family: "Font Awesome 6 Free"; font-style: normal; font-w=
eight: 900; font-display: block; src: url("../webfonts/fa-solid-900.woff2")=
 format("woff2"), url("../webfonts/fa-solid-900.ttf") format("truetype"); }

.fa-solid, .fas { font-family: "Font Awesome 6 Free"; font-weight: 900; }

@font-face { font-family: "Font Awesome 5 Brands"; font-display: block; fon=
t-weight: 400; src: url("../webfonts/fa-brands-400.woff2") format("woff2"),=
 url("../webfonts/fa-brands-400.ttf") format("truetype"); }

@font-face { font-family: "Font Awesome 5 Free"; font-display: block; font-=
weight: 900; src: url("../webfonts/fa-solid-900.woff2") format("woff2"), ur=
l("../webfonts/fa-solid-900.ttf") format("truetype"); }

@font-face { font-family: "Font Awesome 5 Free"; font-display: block; font-=
weight: 400; src: url("../webfonts/fa-regular-400.woff2") format("woff2"), =
url("../webfonts/fa-regular-400.ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-solid-900.woff2") format("woff2"), url("../webfonts/fa-solid-900.=
ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-brands-400.woff2") format("woff2"), url("../webfonts/fa-brands-40=
0.ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-regular-400.woff2") format("woff2"), url("../webfonts/fa-regular-=
400.ttf") format("truetype"); unicode-range: U+F003, U+F006, U+F014, U+F016=
-F017, U+F01A-F01B, U+F01D, U+F022, U+F03E, U+F044, U+F046, U+F05C-F05D, U+=
F06E, U+F070, U+F087-F088, U+F08A, U+F094, U+F096-F097, U+F09D, U+F0A0, U+F=
0A2, U+F0A4-F0A7, U+F0C5, U+F0C7, U+F0E5-F0E6, U+F0EB, U+F0F6-F0F8, U+F10C,=
 U+F114-F115, U+F118-F11A, U+F11C-F11D, U+F133, U+F147, U+F14E, U+F150-F152=
, U+F185-F186, U+F18E, U+F190-F192, U+F196, U+F1C1-F1C9, U+F1D9, U+F1DB, U+=
F1E3, U+F1EA, U+F1F7, U+F1F9, U+F20A, U+F247-F248, U+F24A, U+F24D, U+F255-F=
25B, U+F25D, U+F271-F274, U+F278, U+F27B, U+F28C, U+F28E, U+F29C, U+F2B5, U=
+F2B7, U+F2BA, U+F2BC, U+F2BE, U+F2C0-F2C1, U+F2C3, U+F2D0, U+F2D2, U+F2D4,=
 U+F2DC; }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-v4compatibility.woff2") format("woff2"), url("../webfonts/fa-v4co=
mpatibility.ttf") format("truetype"); unicode-range: U+F041, U+F047, U+F065=
-F066, U+F07D-F07E, U+F080, U+F08B, U+F08E, U+F090, U+F09A, U+F0AC, U+F0AE,=
 U+F0B2, U+F0D0, U+F0D6, U+F0E4, U+F0EC, U+F10A-F10B, U+F123, U+F13E, U+F14=
8-F149, U+F14C, U+F156, U+F15E, U+F160-F161, U+F163, U+F175-F178, U+F195, U=
+F1F8, U+F219, U+F250, U+F252, U+F27A; }
------MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: http://127.0.0.1:8000/static/bootstrap.min.css

@charset "utf-8";

:root { --bs-blue: #0d6efd; --bs-indigo: #6610f2; --bs-purple: #6f42c1; --b=
s-pink: #d63384; --bs-red: #dc3545; --bs-orange: #fd7e14; --bs-yellow: #ffc=
107; --bs-green: #198754; --bs-teal: #20c997; --bs-cyan: #0dcaf0; --bs-whit=
e: #fff; --bs-gray: #6c757d; --bs-gray-dark: #343a40; --bs-gray-100: #f8f9f=
a; --bs-gray-200: #e9ecef; --bs-gray-300: #dee2e6; --bs-gray-400: #ced4da; =
--bs-gray-500: #adb5bd; --bs-gray-600: #6c757d; --bs-gray-700: #495057; --b=
s-gray-800: #343a40; --bs-gray-900: #212529; --bs-primary: #0d6efd; --bs-se=
condary: #6c757d; --bs-success: #198754; --bs-info: #0dcaf0; --bs-warning: =
#ffc107; --bs-danger: #dc3545; --bs-light: #f8f9fa; --bs-dark: #212529; --b=
s-primary-rgb: 13,110,253; --bs-secondary-rgb: 108,117,125; --bs-success-rg=
b: 25,135,84; --bs-info-rgb: 13,202,240; --bs-warning-rgb: 255,193,7; --bs-=
danger-rgb: 220,53,69; --bs-light-rgb: 248,249,250; --bs-dark-rgb: 33,37,41=
; --bs-white-rgb: 255,255,255; --bs-black-rgb: 0,0,0; --bs-body-color-rgb: =
33,37,41; --bs-body-bg-rgb: 255,255,255; --bs-font-sans-serif: system-ui,-a=
pple-system,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans","Liberatio=
n Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","=
Noto Color Emoji"; --bs-font-monospace: SFMono-Regular,Menlo,Monaco,Consola=
s,"Liberation Mono","Courier New",monospace; --bs-gradient: linear-gradient=
(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --bs-body-font=
-family: var(--bs-font-sans-serif); --bs-body-font-size: 1rem; --bs-body-fo=
nt-weight: 400; --bs-body-line-height: 1.5; --bs-body-color: #212529; --bs-=
body-bg: #fff; }

*, ::after, ::before { box-sizing: border-box; }

@media (prefers-reduced-motion: no-preference) {
  :root { scroll-behavior: smooth; }
}

body { margin: 0px; font-family: var(--bs-body-font-family); font-size: var=
(--bs-body-font-size); font-weight: var(--bs-body-font-weight); line-height=
: var(--bs-body-line-height); color: var(--bs-body-color); text-align: var(=
--bs-body-text-align); background-color: var(--bs-body-bg); text-size-adjus=
t: 100%; -webkit-tap-highlight-color: transparent; }

hr { margin: 1rem 0px; color: inherit; background-color: currentcolor; bord=
er: 0px; opacity: 0.25; }

hr:not([size]) { height: 1px; }

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 { margin-top: 0px; mar=
gin-bottom: 0.5rem; font-weight: 500; line-height: 1.2; }

.h1, h1 { font-size: calc(1.375rem + 1.5vw); }

@media (min-width: 1200px) {
  .h1, h1 { font-size: 2.5rem; }
}

.h2, h2 { font-size: calc(1.325rem + 0.9vw); }

@media (min-width: 1200px) {
  .h2, h2 { font-size: 2rem; }
}

.h3, h3 { font-size: calc(1.3rem + 0.6vw); }

@media (min-width: 1200px) {
  .h3, h3 { font-size: 1.75rem; }
}

.h4, h4 { font-size: calc(1.275rem + 0.3vw); }

@media (min-width: 1200px) {
  .h4, h4 { font-size: 1.5rem; }
}

.h5, h5 { font-size: 1.25rem; }

.h6, h6 { font-size: 1rem; }

p { margin-top: 0px; margin-bottom: 1rem; }

abbr[data-bs-original-title], abbr[title] { text-decoration: underline dott=
ed; cursor: help; text-decoration-skip-ink: none; }

address { margin-bottom: 1rem; font-style: normal; line-height: inherit; }

ol, ul { padding-left: 2rem; }

dl, ol, ul { margin-top: 0px; margin-bottom: 1rem; }

ol ol, ol ul, ul ol, ul ul { margin-bottom: 0px; }

dt { font-weight: 700; }

dd { margin-bottom: 0.5rem; margin-left: 0px; }

blockquote { margin: 0px 0px 1rem; }

b, strong { font-weight: bolder; }

.small, small { font-size: 0.875em; }

.mark, mark { padding: 0.2em; background-color: rgb(252, 248, 227); }

sub, sup { position: relative; font-size: 0.75em; line-height: 0; vertical-=
align: baseline; }

sub { bottom: -0.25em; }

sup { top: -0.5em; }

a { color: rgb(13, 110, 253); text-decoration: underline; }

a:hover { color: rgb(10, 88, 202); }

a:not([href]):not([class]), a:not([href]):not([class]):hover { color: inher=
it; text-decoration: none; }

code, kbd, pre, samp { font-family: var(--bs-font-monospace); font-size: 1e=
m; direction: ltr; unicode-bidi: bidi-override; }

pre { display: block; margin-top: 0px; margin-bottom: 1rem; overflow: auto;=
 font-size: 0.875em; }

pre code { font-size: inherit; color: inherit; word-break: normal; }

code { font-size: 0.875em; color: rgb(214, 51, 132); overflow-wrap: break-w=
ord; }

a > code { color: inherit; }

kbd { padding: 0.2rem 0.4rem; font-size: 0.875em; color: rgb(255, 255, 255)=
; background-color: rgb(33, 37, 41); border-radius: 0.2rem; }

kbd kbd { padding: 0px; font-size: 1em; font-weight: 700; }

figure { margin: 0px 0px 1rem; }

img, svg { vertical-align: middle; }

table { caption-side: bottom; border-collapse: collapse; }

caption { padding-top: 0.5rem; padding-bottom: 0.5rem; color: rgb(108, 117,=
 125); text-align: left; }

th { text-align: -webkit-match-parent; }

tbody, td, tfoot, th, thead, tr { border-color: inherit; border-style: soli=
d; border-width: 0px; }

label { display: inline-block; }

button { border-radius: 0px; }

button:focus:not(:focus-visible) { outline: 0px; }

button, input, optgroup, select, textarea { margin: 0px; font-family: inher=
it; font-size: inherit; line-height: inherit; }

button, select { text-transform: none; }

[role=3D"button"] { cursor: pointer; }

select { overflow-wrap: normal; }

select:disabled { opacity: 1; }

[list]::-webkit-calendar-picker-indicator { display: none; }

[type=3D"button"], [type=3D"reset"], [type=3D"submit"], button { appearance=
: button; }

[type=3D"button"]:not(:disabled), [type=3D"reset"]:not(:disabled), [type=3D=
"submit"]:not(:disabled), button:not(:disabled) { cursor: pointer; }

textarea { resize: vertical; }

fieldset { min-width: 0px; padding: 0px; margin: 0px; border: 0px; }

legend { float: left; width: 100%; padding: 0px; margin-bottom: 0.5rem; fon=
t-size: calc(1.275rem + 0.3vw); line-height: inherit; }

@media (min-width: 1200px) {
  legend { font-size: 1.5rem; }
}

legend + * { clear: left; }

::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-fields-wrapper, =
::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute, ::-webk=
it-datetime-edit-month-field, ::-webkit-datetime-edit-text, ::-webkit-datet=
ime-edit-year-field { padding: 0px; }

::-webkit-inner-spin-button { height: auto; }

[type=3D"search"] { outline-offset: -2px; appearance: textfield; }

::-webkit-search-decoration { appearance: none; }

::-webkit-color-swatch-wrapper { padding: 0px; }

::-webkit-file-upload-button { font: inherit; }

::file-selector-button { font: inherit; }

::-webkit-file-upload-button { font: inherit; appearance: button; }

output { display: inline-block; }

iframe { border: 0px; }

summary { display: list-item; cursor: pointer; }

progress { vertical-align: baseline; }

[hidden] { display: none !important; }

.lead { font-size: 1.25rem; font-weight: 300; }

.display-1 { font-size: calc(1.625rem + 4.5vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-1 { font-size: 5rem; }
}

.display-2 { font-size: calc(1.575rem + 3.9vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-2 { font-size: 4.5rem; }
}

.display-3 { font-size: calc(1.525rem + 3.3vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-3 { font-size: 4rem; }
}

.display-4 { font-size: calc(1.475rem + 2.7vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-4 { font-size: 3.5rem; }
}

.display-5 { font-size: calc(1.425rem + 2.1vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-5 { font-size: 3rem; }
}

.display-6 { font-size: calc(1.375rem + 1.5vw); font-weight: 300; line-heig=
ht: 1.2; }

@media (min-width: 1200px) {
  .display-6 { font-size: 2.5rem; }
}

.list-unstyled { padding-left: 0px; list-style: none; }

.list-inline { padding-left: 0px; list-style: none; }

.list-inline-item { display: inline-block; }

.list-inline-item:not(:last-child) { margin-right: 0.5rem; }

.initialism { font-size: 0.875em; text-transform: uppercase; }

.blockquote { margin-bottom: 1rem; font-size: 1.25rem; }

.blockquote > :last-child { margin-bottom: 0px; }

.blockquote-footer { margin-top: -1rem; margin-bottom: 1rem; font-size: 0.8=
75em; color: rgb(108, 117, 125); }

.blockquote-footer::before { content: "=E2=80=94 "; }

.img-fluid { max-width: 100%; height: auto; }

.img-thumbnail { padding: 0.25rem; background-color: rgb(255, 255, 255); bo=
rder: 1px solid rgb(222, 226, 230); border-radius: 0.25rem; max-width: 100%=
; height: auto; }

.figure { display: inline-block; }

.figure-img { margin-bottom: 0.5rem; line-height: 1; }

.figure-caption { font-size: 0.875em; color: rgb(108, 117, 125); }

.container, .container-fluid, .container-lg, .container-md, .container-sm, =
.container-xl, .container-xxl { width: 100%; padding-right: var(--bs-gutter=
-x,.75rem); padding-left: var(--bs-gutter-x,.75rem); margin-right: auto; ma=
rgin-left: auto; }

@media (min-width: 576px) {
  .container, .container-sm { max-width: 540px; }
}

@media (min-width: 768px) {
  .container, .container-md, .container-sm { max-width: 720px; }
}

@media (min-width: 992px) {
  .container, .container-lg, .container-md, .container-sm { max-width: 960p=
x; }
}

@media (min-width: 1200px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl { =
max-width: 1140px; }
}

@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .=
container-xxl { max-width: 1320px; }
}

.row { --bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: flex; flex-wrap: w=
rap; margin-top: calc(-1 * var(--bs-gutter-y)); margin-right: calc(-.5 * va=
r(--bs-gutter-x)); margin-left: calc(-.5 * var(--bs-gutter-x)); }

.row > * { flex-shrink: 0; width: 100%; max-width: 100%; padding-right: cal=
c(var(--bs-gutter-x) * .5); padding-left: calc(var(--bs-gutter-x) * .5); ma=
rgin-top: var(--bs-gutter-y); }

.col { flex: 1 0 0%; }

.row-cols-auto > * { flex: 0 0 auto; width: auto; }

.row-cols-1 > * { flex: 0 0 auto; width: 100%; }

.row-cols-2 > * { flex: 0 0 auto; width: 50%; }

.row-cols-3 > * { flex: 0 0 auto; width: 33.3333%; }

.row-cols-4 > * { flex: 0 0 auto; width: 25%; }

.row-cols-5 > * { flex: 0 0 auto; width: 20%; }

.row-cols-6 > * { flex: 0 0 auto; width: 16.6667%; }

.col-auto { flex: 0 0 auto; width: auto; }

.col-1 { flex: 0 0 auto; width: 8.33333%; }

.col-2 { flex: 0 0 auto; width: 16.6667%; }

.col-3 { flex: 0 0 auto; width: 25%; }

.col-4 { flex: 0 0 auto; width: 33.3333%; }

.col-5 { flex: 0 0 auto; width: 41.6667%; }

.col-6 { flex: 0 0 auto; width: 50%; }

.col-7 { flex: 0 0 auto; width: 58.3333%; }

.col-8 { flex: 0 0 auto; width: 66.6667%; }

.col-9 { flex: 0 0 auto; width: 75%; }

.col-10 { flex: 0 0 auto; width: 83.3333%; }

.col-11 { flex: 0 0 auto; width: 91.6667%; }

.col-12 { flex: 0 0 auto; width: 100%; }

.offset-1 { margin-left: 8.33333%; }

.offset-2 { margin-left: 16.6667%; }

.offset-3 { margin-left: 25%; }

.offset-4 { margin-left: 33.3333%; }

.offset-5 { margin-left: 41.6667%; }

.offset-6 { margin-left: 50%; }

.offset-7 { margin-left: 58.3333%; }

.offset-8 { margin-left: 66.6667%; }

.offset-9 { margin-left: 75%; }

.offset-10 { margin-left: 83.3333%; }

.offset-11 { margin-left: 91.6667%; }

.g-0, .gx-0 { --bs-gutter-x: 0; }

.g-0, .gy-0 { --bs-gutter-y: 0; }

.g-1, .gx-1 { --bs-gutter-x: 0.25rem; }

.g-1, .gy-1 { --bs-gutter-y: 0.25rem; }

.g-2, .gx-2 { --bs-gutter-x: 0.5rem; }

.g-2, .gy-2 { --bs-gutter-y: 0.5rem; }

.g-3, .gx-3 { --bs-gutter-x: 1rem; }

.g-3, .gy-3 { --bs-gutter-y: 1rem; }

.g-4, .gx-4 { --bs-gutter-x: 1.5rem; }

.g-4, .gy-4 { --bs-gutter-y: 1.5rem; }

.g-5, .gx-5 { --bs-gutter-x: 3rem; }

.g-5, .gy-5 { --bs-gutter-y: 3rem; }

@media (min-width: 576px) {
  .col-sm { flex: 1 0 0%; }
  .row-cols-sm-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-sm-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-sm-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-sm-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-sm-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-sm-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-sm-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-sm-auto { flex: 0 0 auto; width: auto; }
  .col-sm-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-sm-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-sm-3 { flex: 0 0 auto; width: 25%; }
  .col-sm-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-sm-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-sm-6 { flex: 0 0 auto; width: 50%; }
  .col-sm-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-sm-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-sm-9 { flex: 0 0 auto; width: 75%; }
  .col-sm-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-sm-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-sm-12 { flex: 0 0 auto; width: 100%; }
  .offset-sm-0 { margin-left: 0px; }
  .offset-sm-1 { margin-left: 8.33333%; }
  .offset-sm-2 { margin-left: 16.6667%; }
  .offset-sm-3 { margin-left: 25%; }
  .offset-sm-4 { margin-left: 33.3333%; }
  .offset-sm-5 { margin-left: 41.6667%; }
  .offset-sm-6 { margin-left: 50%; }
  .offset-sm-7 { margin-left: 58.3333%; }
  .offset-sm-8 { margin-left: 66.6667%; }
  .offset-sm-9 { margin-left: 75%; }
  .offset-sm-10 { margin-left: 83.3333%; }
  .offset-sm-11 { margin-left: 91.6667%; }
  .g-sm-0, .gx-sm-0 { --bs-gutter-x: 0; }
  .g-sm-0, .gy-sm-0 { --bs-gutter-y: 0; }
  .g-sm-1, .gx-sm-1 { --bs-gutter-x: 0.25rem; }
  .g-sm-1, .gy-sm-1 { --bs-gutter-y: 0.25rem; }
  .g-sm-2, .gx-sm-2 { --bs-gutter-x: 0.5rem; }
  .g-sm-2, .gy-sm-2 { --bs-gutter-y: 0.5rem; }
  .g-sm-3, .gx-sm-3 { --bs-gutter-x: 1rem; }
  .g-sm-3, .gy-sm-3 { --bs-gutter-y: 1rem; }
  .g-sm-4, .gx-sm-4 { --bs-gutter-x: 1.5rem; }
  .g-sm-4, .gy-sm-4 { --bs-gutter-y: 1.5rem; }
  .g-sm-5, .gx-sm-5 { --bs-gutter-x: 3rem; }
  .g-sm-5, .gy-sm-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 768px) {
  .col-md { flex: 1 0 0%; }
  .row-cols-md-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-md-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-md-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-md-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-md-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-md-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-md-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-md-auto { flex: 0 0 auto; width: auto; }
  .col-md-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-md-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-md-3 { flex: 0 0 auto; width: 25%; }
  .col-md-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-md-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-md-6 { flex: 0 0 auto; width: 50%; }
  .col-md-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-md-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-md-9 { flex: 0 0 auto; width: 75%; }
  .col-md-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-md-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-md-12 { flex: 0 0 auto; width: 100%; }
  .offset-md-0 { margin-left: 0px; }
  .offset-md-1 { margin-left: 8.33333%; }
  .offset-md-2 { margin-left: 16.6667%; }
  .offset-md-3 { margin-left: 25%; }
  .offset-md-4 { margin-left: 33.3333%; }
  .offset-md-5 { margin-left: 41.6667%; }
  .offset-md-6 { margin-left: 50%; }
  .offset-md-7 { margin-left: 58.3333%; }
  .offset-md-8 { margin-left: 66.6667%; }
  .offset-md-9 { margin-left: 75%; }
  .offset-md-10 { margin-left: 83.3333%; }
  .offset-md-11 { margin-left: 91.6667%; }
  .g-md-0, .gx-md-0 { --bs-gutter-x: 0; }
  .g-md-0, .gy-md-0 { --bs-gutter-y: 0; }
  .g-md-1, .gx-md-1 { --bs-gutter-x: 0.25rem; }
  .g-md-1, .gy-md-1 { --bs-gutter-y: 0.25rem; }
  .g-md-2, .gx-md-2 { --bs-gutter-x: 0.5rem; }
  .g-md-2, .gy-md-2 { --bs-gutter-y: 0.5rem; }
  .g-md-3, .gx-md-3 { --bs-gutter-x: 1rem; }
  .g-md-3, .gy-md-3 { --bs-gutter-y: 1rem; }
  .g-md-4, .gx-md-4 { --bs-gutter-x: 1.5rem; }
  .g-md-4, .gy-md-4 { --bs-gutter-y: 1.5rem; }
  .g-md-5, .gx-md-5 { --bs-gutter-x: 3rem; }
  .g-md-5, .gy-md-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 992px) {
  .col-lg { flex: 1 0 0%; }
  .row-cols-lg-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-lg-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-lg-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-lg-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-lg-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-lg-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-lg-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-lg-auto { flex: 0 0 auto; width: auto; }
  .col-lg-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-lg-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-lg-3 { flex: 0 0 auto; width: 25%; }
  .col-lg-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-lg-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-lg-6 { flex: 0 0 auto; width: 50%; }
  .col-lg-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-lg-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-lg-9 { flex: 0 0 auto; width: 75%; }
  .col-lg-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-lg-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-lg-12 { flex: 0 0 auto; width: 100%; }
  .offset-lg-0 { margin-left: 0px; }
  .offset-lg-1 { margin-left: 8.33333%; }
  .offset-lg-2 { margin-left: 16.6667%; }
  .offset-lg-3 { margin-left: 25%; }
  .offset-lg-4 { margin-left: 33.3333%; }
  .offset-lg-5 { margin-left: 41.6667%; }
  .offset-lg-6 { margin-left: 50%; }
  .offset-lg-7 { margin-left: 58.3333%; }
  .offset-lg-8 { margin-left: 66.6667%; }
  .offset-lg-9 { margin-left: 75%; }
  .offset-lg-10 { margin-left: 83.3333%; }
  .offset-lg-11 { margin-left: 91.6667%; }
  .g-lg-0, .gx-lg-0 { --bs-gutter-x: 0; }
  .g-lg-0, .gy-lg-0 { --bs-gutter-y: 0; }
  .g-lg-1, .gx-lg-1 { --bs-gutter-x: 0.25rem; }
  .g-lg-1, .gy-lg-1 { --bs-gutter-y: 0.25rem; }
  .g-lg-2, .gx-lg-2 { --bs-gutter-x: 0.5rem; }
  .g-lg-2, .gy-lg-2 { --bs-gutter-y: 0.5rem; }
  .g-lg-3, .gx-lg-3 { --bs-gutter-x: 1rem; }
  .g-lg-3, .gy-lg-3 { --bs-gutter-y: 1rem; }
  .g-lg-4, .gx-lg-4 { --bs-gutter-x: 1.5rem; }
  .g-lg-4, .gy-lg-4 { --bs-gutter-y: 1.5rem; }
  .g-lg-5, .gx-lg-5 { --bs-gutter-x: 3rem; }
  .g-lg-5, .gy-lg-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 1200px) {
  .col-xl { flex: 1 0 0%; }
  .row-cols-xl-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-xl-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-xl-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-xl-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-xl-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-xl-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-xl-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-xl-auto { flex: 0 0 auto; width: auto; }
  .col-xl-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-xl-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-xl-3 { flex: 0 0 auto; width: 25%; }
  .col-xl-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-xl-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-xl-6 { flex: 0 0 auto; width: 50%; }
  .col-xl-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-xl-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-xl-9 { flex: 0 0 auto; width: 75%; }
  .col-xl-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-xl-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-xl-12 { flex: 0 0 auto; width: 100%; }
  .offset-xl-0 { margin-left: 0px; }
  .offset-xl-1 { margin-left: 8.33333%; }
  .offset-xl-2 { margin-left: 16.6667%; }
  .offset-xl-3 { margin-left: 25%; }
  .offset-xl-4 { margin-left: 33.3333%; }
  .offset-xl-5 { margin-left: 41.6667%; }
  .offset-xl-6 { margin-left: 50%; }
  .offset-xl-7 { margin-left: 58.3333%; }
  .offset-xl-8 { margin-left: 66.6667%; }
  .offset-xl-9 { margin-left: 75%; }
  .offset-xl-10 { margin-left: 83.3333%; }
  .offset-xl-11 { margin-left: 91.6667%; }
  .g-xl-0, .gx-xl-0 { --bs-gutter-x: 0; }
  .g-xl-0, .gy-xl-0 { --bs-gutter-y: 0; }
  .g-xl-1, .gx-xl-1 { --bs-gutter-x: 0.25rem; }
  .g-xl-1, .gy-xl-1 { --bs-gutter-y: 0.25rem; }
  .g-xl-2, .gx-xl-2 { --bs-gutter-x: 0.5rem; }
  .g-xl-2, .gy-xl-2 { --bs-gutter-y: 0.5rem; }
  .g-xl-3, .gx-xl-3 { --bs-gutter-x: 1rem; }
  .g-xl-3, .gy-xl-3 { --bs-gutter-y: 1rem; }
  .g-xl-4, .gx-xl-4 { --bs-gutter-x: 1.5rem; }
  .g-xl-4, .gy-xl-4 { --bs-gutter-y: 1.5rem; }
  .g-xl-5, .gx-xl-5 { --bs-gutter-x: 3rem; }
  .g-xl-5, .gy-xl-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 1400px) {
  .col-xxl { flex: 1 0 0%; }
  .row-cols-xxl-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-xxl-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-xxl-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-xxl-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-xxl-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-xxl-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-xxl-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-xxl-auto { flex: 0 0 auto; width: auto; }
  .col-xxl-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-xxl-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-xxl-3 { flex: 0 0 auto; width: 25%; }
  .col-xxl-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-xxl-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-xxl-6 { flex: 0 0 auto; width: 50%; }
  .col-xxl-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-xxl-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-xxl-9 { flex: 0 0 auto; width: 75%; }
  .col-xxl-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-xxl-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-xxl-12 { flex: 0 0 auto; width: 100%; }
  .offset-xxl-0 { margin-left: 0px; }
  .offset-xxl-1 { margin-left: 8.33333%; }
  .offset-xxl-2 { margin-left: 16.6667%; }
  .offset-xxl-3 { margin-left: 25%; }
  .offset-xxl-4 { margin-left: 33.3333%; }
  .offset-xxl-5 { margin-left: 41.6667%; }
  .offset-xxl-6 { margin-left: 50%; }
  .offset-xxl-7 { margin-left: 58.3333%; }
  .offset-xxl-8 { margin-left: 66.6667%; }
  .offset-xxl-9 { margin-left: 75%; }
  .offset-xxl-10 { margin-left: 83.3333%; }
  .offset-xxl-11 { margin-left: 91.6667%; }
  .g-xxl-0, .gx-xxl-0 { --bs-gutter-x: 0; }
  .g-xxl-0, .gy-xxl-0 { --bs-gutter-y: 0; }
  .g-xxl-1, .gx-xxl-1 { --bs-gutter-x: 0.25rem; }
  .g-xxl-1, .gy-xxl-1 { --bs-gutter-y: 0.25rem; }
  .g-xxl-2, .gx-xxl-2 { --bs-gutter-x: 0.5rem; }
  .g-xxl-2, .gy-xxl-2 { --bs-gutter-y: 0.5rem; }
  .g-xxl-3, .gx-xxl-3 { --bs-gutter-x: 1rem; }
  .g-xxl-3, .gy-xxl-3 { --bs-gutter-y: 1rem; }
  .g-xxl-4, .gx-xxl-4 { --bs-gutter-x: 1.5rem; }
  .g-xxl-4, .gy-xxl-4 { --bs-gutter-y: 1.5rem; }
  .g-xxl-5, .gx-xxl-5 { --bs-gutter-x: 3rem; }
  .g-xxl-5, .gy-xxl-5 { --bs-gutter-y: 3rem; }
}

.table { --bs-table-bg: transparent; --bs-table-accent-bg: transparent; --b=
s-table-striped-color: #212529; --bs-table-striped-bg: rgba(0, 0, 0, 0.05);=
 --bs-table-active-color: #212529; --bs-table-active-bg: rgba(0, 0, 0, 0.1)=
; --bs-table-hover-color: #212529; --bs-table-hover-bg: rgba(0, 0, 0, 0.075=
); width: 100%; margin-bottom: 1rem; color: rgb(33, 37, 41); vertical-align=
: top; border-color: rgb(222, 226, 230); }

.table > :not(caption) > * > * { padding: 0.5rem; background-color: var(--b=
s-table-bg); border-bottom-width: 1px; box-shadow: inset 0 0 0 9999px var(-=
-bs-table-accent-bg); }

.table > tbody { vertical-align: inherit; }

.table > thead { vertical-align: bottom; }

.table > :not(:first-child) { border-top: 2px solid currentcolor; }

.caption-top { caption-side: top; }

.table-sm > :not(caption) > * > * { padding: 0.25rem; }

.table-bordered > :not(caption) > * { border-width: 1px 0px; }

.table-bordered > :not(caption) > * > * { border-width: 0px 1px; }

.table-borderless > :not(caption) > * > * { border-bottom-width: 0px; }

.table-borderless > :not(:first-child) { border-top-width: 0px; }

.table-striped > tbody > tr:nth-of-type(2n+1) > * { --bs-table-accent-bg: v=
ar(--bs-table-striped-bg); color: var(--bs-table-striped-color); }

.table-active { --bs-table-accent-bg: var(--bs-table-active-bg); color: var=
(--bs-table-active-color); }

.table-hover > tbody > tr:hover > * { --bs-table-accent-bg: var(--bs-table-=
hover-bg); color: var(--bs-table-hover-color); }

.table-primary { --bs-table-bg: #cfe2ff; --bs-table-striped-bg: #c5d7f2; --=
bs-table-striped-color: #000; --bs-table-active-bg: #bacbe6; --bs-table-act=
ive-color: #000; --bs-table-hover-bg: #bfd1ec; --bs-table-hover-color: #000=
; color: rgb(0, 0, 0); border-color: rgb(186, 203, 230); }

.table-secondary { --bs-table-bg: #e2e3e5; --bs-table-striped-bg: #d7d8da; =
--bs-table-striped-color: #000; --bs-table-active-bg: #cbccce; --bs-table-a=
ctive-color: #000; --bs-table-hover-bg: #d1d2d4; --bs-table-hover-color: #0=
00; color: rgb(0, 0, 0); border-color: rgb(203, 204, 206); }

.table-success { --bs-table-bg: #d1e7dd; --bs-table-striped-bg: #c7dbd2; --=
bs-table-striped-color: #000; --bs-table-active-bg: #bcd0c7; --bs-table-act=
ive-color: #000; --bs-table-hover-bg: #c1d6cc; --bs-table-hover-color: #000=
; color: rgb(0, 0, 0); border-color: rgb(188, 208, 199); }

.table-info { --bs-table-bg: #cff4fc; --bs-table-striped-bg: #c5e8ef; --bs-=
table-striped-color: #000; --bs-table-active-bg: #badce3; --bs-table-active=
-color: #000; --bs-table-hover-bg: #bfe2e9; --bs-table-hover-color: #000; c=
olor: rgb(0, 0, 0); border-color: rgb(186, 220, 227); }

.table-warning { --bs-table-bg: #fff3cd; --bs-table-striped-bg: #f2e7c3; --=
bs-table-striped-color: #000; --bs-table-active-bg: #e6dbb9; --bs-table-act=
ive-color: #000; --bs-table-hover-bg: #ece1be; --bs-table-hover-color: #000=
; color: rgb(0, 0, 0); border-color: rgb(230, 219, 185); }

.table-danger { --bs-table-bg: #f8d7da; --bs-table-striped-bg: #eccccf; --b=
s-table-striped-color: #000; --bs-table-active-bg: #dfc2c4; --bs-table-acti=
ve-color: #000; --bs-table-hover-bg: #e5c7ca; --bs-table-hover-color: #000;=
 color: rgb(0, 0, 0); border-color: rgb(223, 194, 196); }

.table-light { --bs-table-bg: #f8f9fa; --bs-table-striped-bg: #ecedee; --bs=
-table-striped-color: #000; --bs-table-active-bg: #dfe0e1; --bs-table-activ=
e-color: #000; --bs-table-hover-bg: #e5e6e7; --bs-table-hover-color: #000; =
color: rgb(0, 0, 0); border-color: rgb(223, 224, 225); }

.table-dark { --bs-table-bg: #212529; --bs-table-striped-bg: #2c3034; --bs-=
table-striped-color: #fff; --bs-table-active-bg: #373b3e; --bs-table-active=
-color: #fff; --bs-table-hover-bg: #323539; --bs-table-hover-color: #fff; c=
olor: rgb(255, 255, 255); border-color: rgb(55, 59, 62); }

.table-responsive { overflow-x: auto; }

@media (max-width: 575.98px) {
  .table-responsive-sm { overflow-x: auto; }
}

@media (max-width: 767.98px) {
  .table-responsive-md { overflow-x: auto; }
}

@media (max-width: 991.98px) {
  .table-responsive-lg { overflow-x: auto; }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl { overflow-x: auto; }
}

@media (max-width: 1399.98px) {
  .table-responsive-xxl { overflow-x: auto; }
}

.form-label { margin-bottom: 0.5rem; }

.col-form-label { padding-top: calc(1px + 0.375rem); padding-bottom: calc(1=
px + 0.375rem); margin-bottom: 0px; font-size: inherit; line-height: 1.5; }

.col-form-label-lg { padding-top: calc(1px + 0.5rem); padding-bottom: calc(=
1px + 0.5rem); font-size: 1.25rem; }

.col-form-label-sm { padding-top: calc(1px + 0.25rem); padding-bottom: calc=
(1px + 0.25rem); font-size: 0.875rem; }

.form-text { margin-top: 0.25rem; font-size: 0.875em; color: rgb(108, 117, =
125); }

.form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; fon=
t-size: 1rem; font-weight: 400; line-height: 1.5; color: rgb(33, 37, 41); b=
ackground-color: rgb(255, 255, 255); background-clip: padding-box; border: =
1px solid rgb(206, 212, 218); appearance: none; border-radius: 0.25rem; tra=
nsition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .form-control { transition: none; }
}

.form-control[type=3D"file"] { overflow: hidden; }

.form-control[type=3D"file"]:not(:disabled):not([readonly]) { cursor: point=
er; }

.form-control:focus { color: rgb(33, 37, 41); background-color: rgb(255, 25=
5, 255); border-color: rgb(134, 183, 254); outline: 0px; box-shadow: rgba(1=
3, 110, 253, 0.25) 0px 0px 0px 0.25rem; }

.form-control::-webkit-date-and-time-value { height: 1.5em; }

.form-control::placeholder { color: rgb(108, 117, 125); opacity: 1; }

.form-control:disabled, .form-control[readonly] { background-color: rgb(233=
, 236, 239); opacity: 1; }

.form-control::-webkit-file-upload-button { padding: 0.375rem 0.75rem; marg=
in: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: rgb(33, 37, 41);=
 background-color: rgb(233, 236, 239); pointer-events: none; border-color: =
inherit; border-style: solid; border-width: 0px; border-inline-end-width: 1=
px; border-radius: 0px; transition: color 0.15s ease-in-out, background-col=
or 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease=
-in-out; }

.form-control::file-selector-button { padding: 0.375rem 0.75rem; margin: -0=
.375rem -0.75rem; margin-inline-end: 0.75rem; color: rgb(33, 37, 41); backg=
round-color: rgb(233, 236, 239); pointer-events: none; border-color: inheri=
t; border-style: solid; border-width: 0px; border-inline-end-width: 1px; bo=
rder-radius: 0px; transition: color 0.15s ease-in-out, background-color 0.1=
5s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-ou=
t; }

@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button { transition: none; }
  .form-control::file-selector-button { transition: none; }
}

.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-but=
ton { background-color: rgb(221, 224, 227); }

.form-control:hover:not(:disabled):not([readonly])::file-selector-button { =
background-color: rgb(221, 224, 227); }

.form-control::-webkit-file-upload-button { padding: 0.375rem 0.75rem; marg=
in: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: rgb(33, 37, 41);=
 background-color: rgb(233, 236, 239); pointer-events: none; border-color: =
inherit; border-style: solid; border-width: 0px; border-inline-end-width: 1=
px; border-radius: 0px; transition: color 0.15s ease-in-out, background-col=
or 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease=
-in-out; }

@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button { transition: none; }
}

.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-but=
ton { background-color: rgb(221, 224, 227); }

.form-control-plaintext { display: block; width: 100%; padding: 0.375rem 0p=
x; margin-bottom: 0px; line-height: 1.5; color: rgb(33, 37, 41); background=
-color: transparent; border-style: solid; border-color: transparent; border=
-image: initial; border-width: 1px 0px; }

.form-control-plaintext.form-control-lg, .form-control-plaintext.form-contr=
ol-sm { padding-right: 0px; padding-left: 0px; }

.form-control-sm { min-height: calc(1.5em + 2px + 0.5rem); padding: 0.25rem=
 0.5rem; font-size: 0.875rem; border-radius: 0.2rem; }

.form-control-sm::-webkit-file-upload-button { padding: 0.25rem 0.5rem; mar=
gin: -0.25rem -0.5rem; margin-inline-end: 0.5rem; }

.form-control-sm::file-selector-button { padding: 0.25rem 0.5rem; margin: -=
0.25rem -0.5rem; margin-inline-end: 0.5rem; }

.form-control-sm::-webkit-file-upload-button { padding: 0.25rem 0.5rem; mar=
gin: -0.25rem -0.5rem; margin-inline-end: 0.5rem; }

.form-control-lg { min-height: calc(1.5em + 2px + 1rem); padding: 0.5rem 1r=
em; font-size: 1.25rem; border-radius: 0.3rem; }

.form-control-lg::-webkit-file-upload-button { padding: 0.5rem 1rem; margin=
: -0.5rem -1rem; margin-inline-end: 1rem; }

.form-control-lg::file-selector-button { padding: 0.5rem 1rem; margin: -0.5=
rem -1rem; margin-inline-end: 1rem; }

.form-control-lg::-webkit-file-upload-button { padding: 0.5rem 1rem; margin=
: -0.5rem -1rem; margin-inline-end: 1rem; }

textarea.form-control { min-height: calc(1.5em + 2px + 0.75rem); }

textarea.form-control-sm { min-height: calc(1.5em + 2px + 0.5rem); }

textarea.form-control-lg { min-height: calc(1.5em + 2px + 1rem); }

.form-control-color { width: 3rem; height: auto; padding: 0.375rem; }

.form-control-color:not(:disabled):not([readonly]) { cursor: pointer; }

.form-control-color::-webkit-color-swatch { height: 1.5em; border-radius: 0=
.25rem; }

.form-select { display: block; width: 100%; padding: 0.375rem 2.25rem 0.375=
rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: rg=
b(33, 37, 41); background-color: rgb(255, 255, 255); background-image: url(=
"data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'=
0 0 16 16'%3e%3cpath fill=3D'none' stroke=3D'%23343a40' stroke-linecap=3D'r=
ound' stroke-linejoin=3D'round' stroke-width=3D'2' d=3D'M2 5l6 6 6-6'/%3e%3=
c/svg%3e"); background-repeat: no-repeat; background-position: right 0.75re=
m center; background-size: 16px 12px; border: 1px solid rgb(206, 212, 218);=
 border-radius: 0.25rem; transition: border-color 0.15s ease-in-out, box-sh=
adow 0.15s ease-in-out; appearance: none; }

@media (prefers-reduced-motion: reduce) {
  .form-select { transition: none; }
}

.form-select:focus { border-color: rgb(134, 183, 254); outline: 0px; box-sh=
adow: rgba(13, 110, 253, 0.25) 0px 0px 0px 0.25rem; }

.form-select[multiple], .form-select[size]:not([size=3D"1"]) { padding-righ=
t: 0.75rem; background-image: none; }

.form-select:disabled { background-color: rgb(233, 236, 239); }

.form-select-sm { padding-top: 0.25rem; padding-bottom: 0.25rem; padding-le=
ft: 0.5rem; font-size: 0.875rem; border-radius: 0.2rem; }

.form-select-lg { padding-top: 0.5rem; padding-bottom: 0.5rem; padding-left=
: 1rem; font-size: 1.25rem; border-radius: 0.3rem; }

.form-check { display: block; min-height: 1.5rem; padding-left: 1.5em; marg=
in-bottom: 0.125rem; }

.form-check .form-check-input { float: left; margin-left: -1.5em; }

.form-check-input { width: 1em; height: 1em; margin-top: 0.25em; vertical-a=
lign: top; background-color: rgb(255, 255, 255); background-repeat: no-repe=
at; background-position: center center; background-size: contain; border: 1=
px solid rgba(0, 0, 0, 0.25); appearance: none; print-color-adjust: exact; =
}

.form-check-input[type=3D"checkbox"] { border-radius: 0.25em; }

.form-check-input[type=3D"radio"] { border-radius: 50%; }

.form-check-input:active { filter: brightness(90%); }

.form-check-input:focus { border-color: rgb(134, 183, 254); outline: 0px; b=
ox-shadow: rgba(13, 110, 253, 0.25) 0px 0px 0px 0.25rem; }

.form-check-input:checked { background-color: rgb(13, 110, 253); border-col=
or: rgb(13, 110, 253); }

.form-check-input:checked[type=3D"checkbox"] { background-image: url("data:=
image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 20=
 20'%3e%3cpath fill=3D'none' stroke=3D'%23fff' stroke-linecap=3D'round' str=
oke-linejoin=3D'round' stroke-width=3D'3' d=3D'M6 10l3 3l6-6'/%3e%3c/svg%3e=
"); }

.form-check-input:checked[type=3D"radio"] { background-image: url("data:ima=
ge/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'-4 -4 8 8=
'%3e%3ccircle r=3D'2' fill=3D'%23fff'/%3e%3c/svg%3e"); }

.form-check-input[type=3D"checkbox"]:indeterminate { background-color: rgb(=
13, 110, 253); border-color: rgb(13, 110, 253); background-image: url("data=
:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 2=
0 20'%3e%3cpath fill=3D'none' stroke=3D'%23fff' stroke-linecap=3D'round' st=
roke-linejoin=3D'round' stroke-width=3D'3' d=3D'M6 10h8'/%3e%3c/svg%3e"); }

.form-check-input:disabled { pointer-events: none; filter: none; opacity: 0=
.5; }

.form-check-input:disabled ~ .form-check-label, .form-check-input[disabled]=
 ~ .form-check-label { opacity: 0.5; }

.form-switch { padding-left: 2.5em; }

.form-switch .form-check-input { width: 2em; margin-left: -2.5em; backgroun=
d-image: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg=
' viewBox=3D'-4 -4 8 8'%3e%3ccircle r=3D'3' fill=3D'rgba%280, 0, 0, 0.25%29=
'/%3e%3c/svg%3e"); background-position: left center; border-radius: 2em; tr=
ansition: background-position 0.15s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input { transition: none; }
}

.form-switch .form-check-input:focus { background-image: url("data:image/sv=
g+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'-4 -4 8 8'%3e%=
3ccircle r=3D'3' fill=3D'%2386b7fe'/%3e%3c/svg%3e"); }

.form-switch .form-check-input:checked { background-position: right center;=
 background-image: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.or=
g/2000/svg' viewBox=3D'-4 -4 8 8'%3e%3ccircle r=3D'3' fill=3D'%23fff'/%3e%3=
c/svg%3e"); }

.form-check-inline { display: inline-block; margin-right: 1rem; }

.btn-check { position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-ev=
ents: none; }

.btn-check:disabled + .btn, .btn-check[disabled] + .btn { pointer-events: n=
one; filter: none; opacity: 0.65; }

.form-range { width: 100%; height: 1.5rem; padding: 0px; background-color: =
transparent; appearance: none; }

.form-range:focus { outline: 0px; }

.form-range:focus::-webkit-slider-thumb { box-shadow: rgb(255, 255, 255) 0p=
x 0px 0px 1px, rgba(13, 110, 253, 0.25) 0px 0px 0px 0.25rem; }

.form-range::-webkit-slider-thumb { width: 1rem; height: 1rem; margin-top: =
-0.25rem; background-color: rgb(13, 110, 253); border: 0px; border-radius: =
1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ea=
se-in-out, box-shadow 0.15s ease-in-out; appearance: none; }

@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb { transition: none; }
}

.form-range::-webkit-slider-thumb:active { background-color: rgb(182, 212, =
254); }

.form-range::-webkit-slider-runnable-track { width: 100%; height: 0.5rem; c=
olor: transparent; cursor: pointer; background-color: rgb(222, 226, 230); b=
order-color: transparent; border-radius: 1rem; }

@media (prefers-reduced-motion: reduce) {
}

.form-range:disabled { pointer-events: none; }

.form-range:disabled::-webkit-slider-thumb { background-color: rgb(173, 181=
, 189); }

.form-floating { position: relative; }

.form-floating > .form-control, .form-floating > .form-select { height: cal=
c(2px + 3.5rem); line-height: 1.25; }

.form-floating > label { position: absolute; top: 0px; left: 0px; height: 1=
00%; padding: 1rem 0.75rem; pointer-events: none; border: 1px solid transpa=
rent; transform-origin: 0px 0px; transition: opacity 0.1s ease-in-out, tran=
sform 0.1s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .form-floating > label { transition: none; }
}

.form-floating > .form-control { padding: 1rem 0.75rem; }

.form-floating > .form-control::placeholder { color: transparent; }

.form-floating > .form-control:focus, .form-floating > .form-control:not(:p=
laceholder-shown) { padding-top: 1.625rem; padding-bottom: 0.625rem; }

.form-floating > .form-control:-webkit-autofill { padding-top: 1.625rem; pa=
dding-bottom: 0.625rem; }

.form-floating > .form-select { padding-top: 1.625rem; padding-bottom: 0.62=
5rem; }

.form-floating > .form-control:focus ~ label, .form-floating > .form-contro=
l:not(:placeholder-shown) ~ label, .form-floating > .form-select ~ label { =
opacity: 0.65; transform: scale(0.85) translateY(-0.5rem) translateX(0.15re=
m); }

.form-floating > .form-control:-webkit-autofill ~ label { opacity: 0.65; tr=
ansform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); }

.input-group { position: relative; display: flex; flex-wrap: wrap; align-it=
ems: stretch; width: 100%; }

.input-group > .form-control, .input-group > .form-select { position: relat=
ive; flex: 1 1 auto; width: 1%; min-width: 0px; }

.input-group > .form-control:focus, .input-group > .form-select:focus { z-i=
ndex: 3; }

.input-group .btn { position: relative; z-index: 2; }

.input-group .btn:focus { z-index: 3; }

.input-group-text { display: flex; align-items: center; padding: 0.375rem 0=
.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: rgb(33,=
 37, 41); text-align: center; white-space: nowrap; background-color: rgb(23=
3, 236, 239); border: 1px solid rgb(206, 212, 218); border-radius: 0.25rem;=
 }

.input-group-lg > .btn, .input-group-lg > .form-control, .input-group-lg > =
.form-select, .input-group-lg > .input-group-text { padding: 0.5rem 1rem; f=
ont-size: 1.25rem; border-radius: 0.3rem; }

.input-group-sm > .btn, .input-group-sm > .form-control, .input-group-sm > =
.form-select, .input-group-sm > .input-group-text { padding: 0.25rem 0.5rem=
; font-size: 0.875rem; border-radius: 0.2rem; }

.input-group-lg > .form-select, .input-group-sm > .form-select { padding-ri=
ght: 3rem; }

.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3), .=
input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):=
not(.dropdown-menu) { border-top-right-radius: 0px; border-bottom-right-rad=
ius: 0px; }

.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4), .input-=
group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.drop=
down-menu) { border-top-right-radius: 0px; border-bottom-right-radius: 0px;=
 }

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):n=
ot(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) { margin-l=
eft: -1px; border-top-left-radius: 0px; border-bottom-left-radius: 0px; }

.valid-feedback { display: none; width: 100%; margin-top: 0.25rem; font-siz=
e: 0.875em; color: rgb(25, 135, 84); }

.valid-tooltip { position: absolute; top: 100%; z-index: 5; display: none; =
max-width: 100%; padding: 0.25rem 0.5rem; margin-top: 0.1rem; font-size: 0.=
875rem; color: rgb(255, 255, 255); background-color: rgba(25, 135, 84, 0.9)=
; border-radius: 0.25rem; }

.is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip, .was-validated :va=
lid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip { display: bl=
ock; }

.form-control.is-valid, .was-validated .form-control:valid { border-color: =
rgb(25, 135, 84); padding-right: calc(1.5em + 0.75rem); background-image: u=
rl("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=
=3D'0 0 8 8'%3e%3cpath fill=3D'%23198754' d=3D'M2.3 6.73L.6 4.53c-.4-1.04.4=
6-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z=
'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right=
 calc(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem)=
 calc(0.75em + 0.375rem); }

.form-control.is-valid:focus, .was-validated .form-control:valid:focus { bo=
rder-color: rgb(25, 135, 84); box-shadow: rgba(25, 135, 84, 0.25) 0px 0px 0=
px 0.25rem; }

.was-validated textarea.form-control:valid, textarea.form-control.is-valid =
{ padding-right: calc(1.5em + 0.75rem); background-position: right calc(0.3=
75em + 0.1875rem) top calc(0.375em + 0.1875rem); }

.form-select.is-valid, .was-validated .form-select:valid { border-color: rg=
b(25, 135, 84); }

.form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:no=
t([multiple])[size=3D"1"], .was-validated .form-select:valid:not([multiple]=
):not([size]), .was-validated .form-select:valid:not([multiple])[size=3D"1"=
] { padding-right: 4.125rem; background-image: url("data:image/svg+xml,%3cs=
vg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16'%3e%3cpath fil=
l=3D'none' stroke=3D'%23343a40' stroke-linecap=3D'round' stroke-linejoin=3D=
'round' stroke-width=3D'2' d=3D'M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:im=
age/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 8 8'=
%3e%3cpath fill=3D'%23198754' d=3D'M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8=
l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg=
%3e"); background-position: right 0.75rem center, right 2.25rem center; bac=
kground-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }

.form-select.is-valid:focus, .was-validated .form-select:valid:focus { bord=
er-color: rgb(25, 135, 84); box-shadow: rgba(25, 135, 84, 0.25) 0px 0px 0px=
 0.25rem; }

.form-check-input.is-valid, .was-validated .form-check-input:valid { border=
-color: rgb(25, 135, 84); }

.form-check-input.is-valid:checked, .was-validated .form-check-input:valid:=
checked { background-color: rgb(25, 135, 84); }

.form-check-input.is-valid:focus, .was-validated .form-check-input:valid:fo=
cus { box-shadow: rgba(25, 135, 84, 0.25) 0px 0px 0px 0.25rem; }

.form-check-input.is-valid ~ .form-check-label, .was-validated .form-check-=
input:valid ~ .form-check-label { color: rgb(25, 135, 84); }

.form-check-inline .form-check-input ~ .valid-feedback { margin-left: 0.5em=
; }

.input-group .form-control.is-valid, .input-group .form-select.is-valid, .w=
as-validated .input-group .form-control:valid, .was-validated .input-group =
.form-select:valid { z-index: 1; }

.input-group .form-control.is-valid:focus, .input-group .form-select.is-val=
id:focus, .was-validated .input-group .form-control:valid:focus, .was-valid=
ated .input-group .form-select:valid:focus { z-index: 3; }

.invalid-feedback { display: none; width: 100%; margin-top: 0.25rem; font-s=
ize: 0.875em; color: rgb(220, 53, 69); }

.invalid-tooltip { position: absolute; top: 100%; z-index: 5; display: none=
; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: 0.1rem; font-size: =
0.875rem; color: rgb(255, 255, 255); background-color: rgba(220, 53, 69, 0.=
9); border-radius: 0.25rem; }

.is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip, .was-valid=
ated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-toolt=
ip { display: block; }

.form-control.is-invalid, .was-validated .form-control:invalid { border-col=
or: rgb(220, 53, 69); padding-right: calc(1.5em + 0.75rem); background-imag=
e: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' view=
Box=3D'0 0 12 12' width=3D'12' height=3D'12' fill=3D'none' stroke=3D'%23dc3=
545'%3e%3ccircle cx=3D'6' cy=3D'6' r=3D'4.5'/%3e%3cpath stroke-linejoin=3D'=
round' d=3D'M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx=3D'6' cy=3D'8.2' r=3D'.6' f=
ill=3D'%23dc3545' stroke=3D'none'/%3e%3c/svg%3e"); background-repeat: no-re=
peat; background-position: right calc(0.375em + 0.1875rem) center; backgrou=
nd-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }

.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus =
{ border-color: rgb(220, 53, 69); box-shadow: rgba(220, 53, 69, 0.25) 0px 0=
px 0px 0.25rem; }

.was-validated textarea.form-control:invalid, textarea.form-control.is-inva=
lid { padding-right: calc(1.5em + 0.75rem); background-position: right calc=
(0.375em + 0.1875rem) top calc(0.375em + 0.1875rem); }

.form-select.is-invalid, .was-validated .form-select:invalid { border-color=
: rgb(220, 53, 69); }

.form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invali=
d:not([multiple])[size=3D"1"], .was-validated .form-select:invalid:not([mul=
tiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[si=
ze=3D"1"] { padding-right: 4.125rem; background-image: url("data:image/svg+=
xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16'%3e%3c=
path fill=3D'none' stroke=3D'%23343a40' stroke-linecap=3D'round' stroke-lin=
ejoin=3D'round' stroke-width=3D'2' d=3D'M2 5l6 6 6-6'/%3e%3c/svg%3e"), url(=
"data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'=
0 0 12 12' width=3D'12' height=3D'12' fill=3D'none' stroke=3D'%23dc3545'%3e=
%3ccircle cx=3D'6' cy=3D'6' r=3D'4.5'/%3e%3cpath stroke-linejoin=3D'round' =
d=3D'M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx=3D'6' cy=3D'8.2' r=3D'.6' fill=3D'=
%23dc3545' stroke=3D'none'/%3e%3c/svg%3e"); background-position: right 0.75=
rem center, right 2.25rem center; background-size: 16px 12px, calc(0.75em +=
 0.375rem) calc(0.75em + 0.375rem); }

.form-select.is-invalid:focus, .was-validated .form-select:invalid:focus { =
border-color: rgb(220, 53, 69); box-shadow: rgba(220, 53, 69, 0.25) 0px 0px=
 0px 0.25rem; }

.form-check-input.is-invalid, .was-validated .form-check-input:invalid { bo=
rder-color: rgb(220, 53, 69); }

.form-check-input.is-invalid:checked, .was-validated .form-check-input:inva=
lid:checked { background-color: rgb(220, 53, 69); }

.form-check-input.is-invalid:focus, .was-validated .form-check-input:invali=
d:focus { box-shadow: rgba(220, 53, 69, 0.25) 0px 0px 0px 0.25rem; }

.form-check-input.is-invalid ~ .form-check-label, .was-validated .form-chec=
k-input:invalid ~ .form-check-label { color: rgb(220, 53, 69); }

.form-check-inline .form-check-input ~ .invalid-feedback { margin-left: 0.5=
em; }

.input-group .form-control.is-invalid, .input-group .form-select.is-invalid=
, .was-validated .input-group .form-control:invalid, .was-validated .input-=
group .form-select:invalid { z-index: 2; }

.input-group .form-control.is-invalid:focus, .input-group .form-select.is-i=
nvalid:focus, .was-validated .input-group .form-control:invalid:focus, .was=
-validated .input-group .form-select:invalid:focus { z-index: 3; }

.btn { display: inline-block; font-weight: 400; line-height: 1.5; color: rg=
b(33, 37, 41); text-align: center; text-decoration: none; vertical-align: m=
iddle; cursor: pointer; user-select: none; background-color: transparent; b=
order: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 1rem; b=
order-radius: 0.25rem; transition: color 0.15s ease-in-out, background-colo=
r 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-=
in-out; }

@media (prefers-reduced-motion: reduce) {
  .btn { transition: none; }
}

.btn:hover { color: rgb(33, 37, 41); }

.btn-check:focus + .btn, .btn:focus { outline: 0px; box-shadow: rgba(13, 11=
0, 253, 0.25) 0px 0px 0px 0.25rem; }

.btn.disabled, .btn:disabled, fieldset:disabled .btn { pointer-events: none=
; opacity: 0.65; }

.btn-primary { color: rgb(255, 255, 255); background-color: rgb(13, 110, 25=
3); border-color: rgb(13, 110, 253); }

.btn-primary:hover { color: rgb(255, 255, 255); background-color: rgb(11, 9=
4, 215); border-color: rgb(10, 88, 202); }

.btn-check:focus + .btn-primary, .btn-primary:focus { color: rgb(255, 255, =
255); background-color: rgb(11, 94, 215); border-color: rgb(10, 88, 202); b=
ox-shadow: rgba(49, 132, 253, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-primary, .btn-check:checked + .btn-primary, .btn-p=
rimary.active, .btn-primary:active, .show > .btn-primary.dropdown-toggle { =
color: rgb(255, 255, 255); background-color: rgb(10, 88, 202); border-color=
: rgb(10, 83, 190); }

.btn-check:active + .btn-primary:focus, .btn-check:checked + .btn-primary:f=
ocus, .btn-primary.active:focus, .btn-primary:active:focus, .show > .btn-pr=
imary.dropdown-toggle:focus { box-shadow: rgba(49, 132, 253, 0.5) 0px 0px 0=
px 0.25rem; }

.btn-primary.disabled, .btn-primary:disabled { color: rgb(255, 255, 255); b=
ackground-color: rgb(13, 110, 253); border-color: rgb(13, 110, 253); }

.btn-secondary { color: rgb(255, 255, 255); background-color: rgb(108, 117,=
 125); border-color: rgb(108, 117, 125); }

.btn-secondary:hover { color: rgb(255, 255, 255); background-color: rgb(92,=
 99, 106); border-color: rgb(86, 94, 100); }

.btn-check:focus + .btn-secondary, .btn-secondary:focus { color: rgb(255, 2=
55, 255); background-color: rgb(92, 99, 106); border-color: rgb(86, 94, 100=
); box-shadow: rgba(130, 138, 145, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-secondary, .btn-check:checked + .btn-secondary, .b=
tn-secondary.active, .btn-secondary:active, .show > .btn-secondary.dropdown=
-toggle { color: rgb(255, 255, 255); background-color: rgb(86, 94, 100); bo=
rder-color: rgb(81, 88, 94); }

.btn-check:active + .btn-secondary:focus, .btn-check:checked + .btn-seconda=
ry:focus, .btn-secondary.active:focus, .btn-secondary:active:focus, .show >=
 .btn-secondary.dropdown-toggle:focus { box-shadow: rgba(130, 138, 145, 0.5=
) 0px 0px 0px 0.25rem; }

.btn-secondary.disabled, .btn-secondary:disabled { color: rgb(255, 255, 255=
); background-color: rgb(108, 117, 125); border-color: rgb(108, 117, 125); =
}

.btn-success { color: rgb(255, 255, 255); background-color: rgb(25, 135, 84=
); border-color: rgb(25, 135, 84); }

.btn-success:hover { color: rgb(255, 255, 255); background-color: rgb(21, 1=
15, 71); border-color: rgb(20, 108, 67); }

.btn-check:focus + .btn-success, .btn-success:focus { color: rgb(255, 255, =
255); background-color: rgb(21, 115, 71); border-color: rgb(20, 108, 67); b=
ox-shadow: rgba(60, 153, 110, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-success, .btn-check:checked + .btn-success, .btn-s=
uccess.active, .btn-success:active, .show > .btn-success.dropdown-toggle { =
color: rgb(255, 255, 255); background-color: rgb(20, 108, 67); border-color=
: rgb(19, 101, 63); }

.btn-check:active + .btn-success:focus, .btn-check:checked + .btn-success:f=
ocus, .btn-success.active:focus, .btn-success:active:focus, .show > .btn-su=
ccess.dropdown-toggle:focus { box-shadow: rgba(60, 153, 110, 0.5) 0px 0px 0=
px 0.25rem; }

.btn-success.disabled, .btn-success:disabled { color: rgb(255, 255, 255); b=
ackground-color: rgb(25, 135, 84); border-color: rgb(25, 135, 84); }

.btn-info { color: rgb(0, 0, 0); background-color: rgb(13, 202, 240); borde=
r-color: rgb(13, 202, 240); }

.btn-info:hover { color: rgb(0, 0, 0); background-color: rgb(49, 210, 242);=
 border-color: rgb(37, 207, 242); }

.btn-check:focus + .btn-info, .btn-info:focus { color: rgb(0, 0, 0); backgr=
ound-color: rgb(49, 210, 242); border-color: rgb(37, 207, 242); box-shadow:=
 rgba(11, 172, 204, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-info, .btn-check:checked + .btn-info, .btn-info.ac=
tive, .btn-info:active, .show > .btn-info.dropdown-toggle { color: rgb(0, 0=
, 0); background-color: rgb(61, 213, 243); border-color: rgb(37, 207, 242);=
 }

.btn-check:active + .btn-info:focus, .btn-check:checked + .btn-info:focus, =
.btn-info.active:focus, .btn-info:active:focus, .show > .btn-info.dropdown-=
toggle:focus { box-shadow: rgba(11, 172, 204, 0.5) 0px 0px 0px 0.25rem; }

.btn-info.disabled, .btn-info:disabled { color: rgb(0, 0, 0); background-co=
lor: rgb(13, 202, 240); border-color: rgb(13, 202, 240); }

.btn-warning { color: rgb(0, 0, 0); background-color: rgb(255, 193, 7); bor=
der-color: rgb(255, 193, 7); }

.btn-warning:hover { color: rgb(0, 0, 0); background-color: rgb(255, 202, 4=
4); border-color: rgb(255, 199, 32); }

.btn-check:focus + .btn-warning, .btn-warning:focus { color: rgb(0, 0, 0); =
background-color: rgb(255, 202, 44); border-color: rgb(255, 199, 32); box-s=
hadow: rgba(217, 164, 6, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-warning, .btn-check:checked + .btn-warning, .btn-w=
arning.active, .btn-warning:active, .show > .btn-warning.dropdown-toggle { =
color: rgb(0, 0, 0); background-color: rgb(255, 205, 57); border-color: rgb=
(255, 199, 32); }

.btn-check:active + .btn-warning:focus, .btn-check:checked + .btn-warning:f=
ocus, .btn-warning.active:focus, .btn-warning:active:focus, .show > .btn-wa=
rning.dropdown-toggle:focus { box-shadow: rgba(217, 164, 6, 0.5) 0px 0px 0p=
x 0.25rem; }

.btn-warning.disabled, .btn-warning:disabled { color: rgb(0, 0, 0); backgro=
und-color: rgb(255, 193, 7); border-color: rgb(255, 193, 7); }

.btn-danger { color: rgb(255, 255, 255); background-color: rgb(220, 53, 69)=
; border-color: rgb(220, 53, 69); }

.btn-danger:hover { color: rgb(255, 255, 255); background-color: rgb(187, 4=
5, 59); border-color: rgb(176, 42, 55); }

.btn-check:focus + .btn-danger, .btn-danger:focus { color: rgb(255, 255, 25=
5); background-color: rgb(187, 45, 59); border-color: rgb(176, 42, 55); box=
-shadow: rgba(225, 83, 97, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-danger, .btn-check:checked + .btn-danger, .btn-dan=
ger.active, .btn-danger:active, .show > .btn-danger.dropdown-toggle { color=
: rgb(255, 255, 255); background-color: rgb(176, 42, 55); border-color: rgb=
(165, 40, 52); }

.btn-check:active + .btn-danger:focus, .btn-check:checked + .btn-danger:foc=
us, .btn-danger.active:focus, .btn-danger:active:focus, .show > .btn-danger=
.dropdown-toggle:focus { box-shadow: rgba(225, 83, 97, 0.5) 0px 0px 0px 0.2=
5rem; }

.btn-danger.disabled, .btn-danger:disabled { color: rgb(255, 255, 255); bac=
kground-color: rgb(220, 53, 69); border-color: rgb(220, 53, 69); }

.btn-light { color: rgb(0, 0, 0); background-color: rgb(248, 249, 250); bor=
der-color: rgb(248, 249, 250); }

.btn-light:hover { color: rgb(0, 0, 0); background-color: rgb(249, 250, 251=
); border-color: rgb(249, 250, 251); }

.btn-check:focus + .btn-light, .btn-light:focus { color: rgb(0, 0, 0); back=
ground-color: rgb(249, 250, 251); border-color: rgb(249, 250, 251); box-sha=
dow: rgba(211, 212, 213, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-light, .btn-check:checked + .btn-light, .btn-light=
.active, .btn-light:active, .show > .btn-light.dropdown-toggle { color: rgb=
(0, 0, 0); background-color: rgb(249, 250, 251); border-color: rgb(249, 250=
, 251); }

.btn-check:active + .btn-light:focus, .btn-check:checked + .btn-light:focus=
, .btn-light.active:focus, .btn-light:active:focus, .show > .btn-light.drop=
down-toggle:focus { box-shadow: rgba(211, 212, 213, 0.5) 0px 0px 0px 0.25re=
m; }

.btn-light.disabled, .btn-light:disabled { color: rgb(0, 0, 0); background-=
color: rgb(248, 249, 250); border-color: rgb(248, 249, 250); }

.btn-dark { color: rgb(255, 255, 255); background-color: rgb(33, 37, 41); b=
order-color: rgb(33, 37, 41); }

.btn-dark:hover { color: rgb(255, 255, 255); background-color: rgb(28, 31, =
35); border-color: rgb(26, 30, 33); }

.btn-check:focus + .btn-dark, .btn-dark:focus { color: rgb(255, 255, 255); =
background-color: rgb(28, 31, 35); border-color: rgb(26, 30, 33); box-shado=
w: rgba(66, 70, 73, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-dark, .btn-check:checked + .btn-dark, .btn-dark.ac=
tive, .btn-dark:active, .show > .btn-dark.dropdown-toggle { color: rgb(255,=
 255, 255); background-color: rgb(26, 30, 33); border-color: rgb(25, 28, 31=
); }

.btn-check:active + .btn-dark:focus, .btn-check:checked + .btn-dark:focus, =
.btn-dark.active:focus, .btn-dark:active:focus, .show > .btn-dark.dropdown-=
toggle:focus { box-shadow: rgba(66, 70, 73, 0.5) 0px 0px 0px 0.25rem; }

.btn-dark.disabled, .btn-dark:disabled { color: rgb(255, 255, 255); backgro=
und-color: rgb(33, 37, 41); border-color: rgb(33, 37, 41); }

.btn-outline-primary { color: rgb(13, 110, 253); border-color: rgb(13, 110,=
 253); }

.btn-outline-primary:hover { color: rgb(255, 255, 255); background-color: r=
gb(13, 110, 253); border-color: rgb(13, 110, 253); }

.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus { box-s=
hadow: rgba(13, 110, 253, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-primary, .btn-check:checked + .btn-outline=
-primary, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle=
.show, .btn-outline-primary:active { color: rgb(255, 255, 255); background-=
color: rgb(13, 110, 253); border-color: rgb(13, 110, 253); }

.btn-check:active + .btn-outline-primary:focus, .btn-check:checked + .btn-o=
utline-primary:focus, .btn-outline-primary.active:focus, .btn-outline-prima=
ry.dropdown-toggle.show:focus, .btn-outline-primary:active:focus { box-shad=
ow: rgba(13, 110, 253, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-primary.disabled, .btn-outline-primary:disabled { color: rgb(1=
3, 110, 253); background-color: transparent; }

.btn-outline-secondary { color: rgb(108, 117, 125); border-color: rgb(108, =
117, 125); }

.btn-outline-secondary:hover { color: rgb(255, 255, 255); background-color:=
 rgb(108, 117, 125); border-color: rgb(108, 117, 125); }

.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus { b=
ox-shadow: rgba(108, 117, 125, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-secondary, .btn-check:checked + .btn-outli=
ne-secondary, .btn-outline-secondary.active, .btn-outline-secondary.dropdow=
n-toggle.show, .btn-outline-secondary:active { color: rgb(255, 255, 255); b=
ackground-color: rgb(108, 117, 125); border-color: rgb(108, 117, 125); }

.btn-check:active + .btn-outline-secondary:focus, .btn-check:checked + .btn=
-outline-secondary:focus, .btn-outline-secondary.active:focus, .btn-outline=
-secondary.dropdown-toggle.show:focus, .btn-outline-secondary:active:focus =
{ box-shadow: rgba(108, 117, 125, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled { color: r=
gb(108, 117, 125); background-color: transparent; }

.btn-outline-success { color: rgb(25, 135, 84); border-color: rgb(25, 135, =
84); }

.btn-outline-success:hover { color: rgb(255, 255, 255); background-color: r=
gb(25, 135, 84); border-color: rgb(25, 135, 84); }

.btn-check:focus + .btn-outline-success, .btn-outline-success:focus { box-s=
hadow: rgba(25, 135, 84, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-success, .btn-check:checked + .btn-outline=
-success, .btn-outline-success.active, .btn-outline-success.dropdown-toggle=
.show, .btn-outline-success:active { color: rgb(255, 255, 255); background-=
color: rgb(25, 135, 84); border-color: rgb(25, 135, 84); }

.btn-check:active + .btn-outline-success:focus, .btn-check:checked + .btn-o=
utline-success:focus, .btn-outline-success.active:focus, .btn-outline-succe=
ss.dropdown-toggle.show:focus, .btn-outline-success:active:focus { box-shad=
ow: rgba(25, 135, 84, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-success.disabled, .btn-outline-success:disabled { color: rgb(2=
5, 135, 84); background-color: transparent; }

.btn-outline-info { color: rgb(13, 202, 240); border-color: rgb(13, 202, 24=
0); }

.btn-outline-info:hover { color: rgb(0, 0, 0); background-color: rgb(13, 20=
2, 240); border-color: rgb(13, 202, 240); }

.btn-check:focus + .btn-outline-info, .btn-outline-info:focus { box-shadow:=
 rgba(13, 202, 240, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-info, .btn-check:checked + .btn-outline-in=
fo, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show, .btn-=
outline-info:active { color: rgb(0, 0, 0); background-color: rgb(13, 202, 2=
40); border-color: rgb(13, 202, 240); }

.btn-check:active + .btn-outline-info:focus, .btn-check:checked + .btn-outl=
ine-info:focus, .btn-outline-info.active:focus, .btn-outline-info.dropdown-=
toggle.show:focus, .btn-outline-info:active:focus { box-shadow: rgba(13, 20=
2, 240, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-info.disabled, .btn-outline-info:disabled { color: rgb(13, 202=
, 240); background-color: transparent; }

.btn-outline-warning { color: rgb(255, 193, 7); border-color: rgb(255, 193,=
 7); }

.btn-outline-warning:hover { color: rgb(0, 0, 0); background-color: rgb(255=
, 193, 7); border-color: rgb(255, 193, 7); }

.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus { box-s=
hadow: rgba(255, 193, 7, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-warning, .btn-check:checked + .btn-outline=
-warning, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle=
.show, .btn-outline-warning:active { color: rgb(0, 0, 0); background-color:=
 rgb(255, 193, 7); border-color: rgb(255, 193, 7); }

.btn-check:active + .btn-outline-warning:focus, .btn-check:checked + .btn-o=
utline-warning:focus, .btn-outline-warning.active:focus, .btn-outline-warni=
ng.dropdown-toggle.show:focus, .btn-outline-warning:active:focus { box-shad=
ow: rgba(255, 193, 7, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-warning.disabled, .btn-outline-warning:disabled { color: rgb(2=
55, 193, 7); background-color: transparent; }

.btn-outline-danger { color: rgb(220, 53, 69); border-color: rgb(220, 53, 6=
9); }

.btn-outline-danger:hover { color: rgb(255, 255, 255); background-color: rg=
b(220, 53, 69); border-color: rgb(220, 53, 69); }

.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus { box-sha=
dow: rgba(220, 53, 69, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-danger, .btn-check:checked + .btn-outline-=
danger, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.sho=
w, .btn-outline-danger:active { color: rgb(255, 255, 255); background-color=
: rgb(220, 53, 69); border-color: rgb(220, 53, 69); }

.btn-check:active + .btn-outline-danger:focus, .btn-check:checked + .btn-ou=
tline-danger:focus, .btn-outline-danger.active:focus, .btn-outline-danger.d=
ropdown-toggle.show:focus, .btn-outline-danger:active:focus { box-shadow: r=
gba(220, 53, 69, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-danger.disabled, .btn-outline-danger:disabled { color: rgb(220=
, 53, 69); background-color: transparent; }

.btn-outline-light { color: rgb(248, 249, 250); border-color: rgb(248, 249,=
 250); }

.btn-outline-light:hover { color: rgb(0, 0, 0); background-color: rgb(248, =
249, 250); border-color: rgb(248, 249, 250); }

.btn-check:focus + .btn-outline-light, .btn-outline-light:focus { box-shado=
w: rgba(248, 249, 250, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-light, .btn-check:checked + .btn-outline-l=
ight, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show, .=
btn-outline-light:active { color: rgb(0, 0, 0); background-color: rgb(248, =
249, 250); border-color: rgb(248, 249, 250); }

.btn-check:active + .btn-outline-light:focus, .btn-check:checked + .btn-out=
line-light:focus, .btn-outline-light.active:focus, .btn-outline-light.dropd=
own-toggle.show:focus, .btn-outline-light:active:focus { box-shadow: rgba(2=
48, 249, 250, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-light.disabled, .btn-outline-light:disabled { color: rgb(248, =
249, 250); background-color: transparent; }

.btn-outline-dark { color: rgb(33, 37, 41); border-color: rgb(33, 37, 41); =
}

.btn-outline-dark:hover { color: rgb(255, 255, 255); background-color: rgb(=
33, 37, 41); border-color: rgb(33, 37, 41); }

.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus { box-shadow:=
 rgba(33, 37, 41, 0.5) 0px 0px 0px 0.25rem; }

.btn-check:active + .btn-outline-dark, .btn-check:checked + .btn-outline-da=
rk, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show, .btn-=
outline-dark:active { color: rgb(255, 255, 255); background-color: rgb(33, =
37, 41); border-color: rgb(33, 37, 41); }

.btn-check:active + .btn-outline-dark:focus, .btn-check:checked + .btn-outl=
ine-dark:focus, .btn-outline-dark.active:focus, .btn-outline-dark.dropdown-=
toggle.show:focus, .btn-outline-dark:active:focus { box-shadow: rgba(33, 37=
, 41, 0.5) 0px 0px 0px 0.25rem; }

.btn-outline-dark.disabled, .btn-outline-dark:disabled { color: rgb(33, 37,=
 41); background-color: transparent; }

.btn-link { font-weight: 400; color: rgb(13, 110, 253); text-decoration: un=
derline; }

.btn-link:hover { color: rgb(10, 88, 202); }

.btn-link.disabled, .btn-link:disabled { color: rgb(108, 117, 125); }

.btn-group-lg > .btn, .btn-lg { padding: 0.5rem 1rem; font-size: 1.25rem; b=
order-radius: 0.3rem; }

.btn-group-sm > .btn, .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875re=
m; border-radius: 0.2rem; }

.fade { transition: opacity 0.15s linear; }

@media (prefers-reduced-motion: reduce) {
  .fade { transition: none; }
}

.fade:not(.show) { opacity: 0; }

.collapse:not(.show) { display: none; }

.collapsing { height: 0px; overflow: hidden; transition: height 0.35s; }

@media (prefers-reduced-motion: reduce) {
  .collapsing { transition: none; }
}

.collapsing.collapse-horizontal { width: 0px; height: auto; transition: wid=
th 0.35s; }

@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal { transition: none; }
}

.dropdown, .dropend, .dropstart, .dropup { position: relative; }

.dropdown-toggle { white-space: nowrap; }

.dropdown-toggle::after { display: inline-block; margin-left: 0.255em; vert=
ical-align: 0.255em; content: ""; border-width: 0.3em 0.3em 0px; border-top=
-style: solid; border-top-color: initial; border-right-style: solid; border=
-right-color: transparent; border-bottom-style: initial; border-bottom-colo=
r: initial; border-left-style: solid; border-left-color: transparent; }

.dropdown-toggle:empty::after { margin-left: 0px; }

.dropdown-menu { position: absolute; z-index: 1000; display: none; min-widt=
h: 10rem; padding: 0.5rem 0px; margin: 0px; font-size: 1rem; color: rgb(33,=
 37, 41); text-align: left; list-style: none; background-color: rgb(255, 25=
5, 255); background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.15=
); border-radius: 0.25rem; }

.dropdown-menu[data-bs-popper] { top: 100%; left: 0px; margin-top: 0.125rem=
; }

.dropdown-menu-start { --bs-position: start; }

.dropdown-menu-start[data-bs-popper] { right: auto; left: 0px; }

.dropdown-menu-end { --bs-position: end; }

.dropdown-menu-end[data-bs-popper] { right: 0px; left: auto; }

@media (min-width: 576px) {
  .dropdown-menu-sm-start { --bs-position: start; }
  .dropdown-menu-sm-start[data-bs-popper] { right: auto; left: 0px; }
  .dropdown-menu-sm-end { --bs-position: end; }
  .dropdown-menu-sm-end[data-bs-popper] { right: 0px; left: auto; }
}

@media (min-width: 768px) {
  .dropdown-menu-md-start { --bs-position: start; }
  .dropdown-menu-md-start[data-bs-popper] { right: auto; left: 0px; }
  .dropdown-menu-md-end { --bs-position: end; }
  .dropdown-menu-md-end[data-bs-popper] { right: 0px; left: auto; }
}

@media (min-width: 992px) {
  .dropdown-menu-lg-start { --bs-position: start; }
  .dropdown-menu-lg-start[data-bs-popper] { right: auto; left: 0px; }
  .dropdown-menu-lg-end { --bs-position: end; }
  .dropdown-menu-lg-end[data-bs-popper] { right: 0px; left: auto; }
}

@media (min-width: 1200px) {
  .dropdown-menu-xl-start { --bs-position: start; }
  .dropdown-menu-xl-start[data-bs-popper] { right: auto; left: 0px; }
  .dropdown-menu-xl-end { --bs-position: end; }
  .dropdown-menu-xl-end[data-bs-popper] { right: 0px; left: auto; }
}

@media (min-width: 1400px) {
  .dropdown-menu-xxl-start { --bs-position: start; }
  .dropdown-menu-xxl-start[data-bs-popper] { right: auto; left: 0px; }
  .dropdown-menu-xxl-end { --bs-position: end; }
  .dropdown-menu-xxl-end[data-bs-popper] { right: 0px; left: auto; }
}

.dropup .dropdown-menu[data-bs-popper] { top: auto; bottom: 100%; margin-to=
p: 0px; margin-bottom: 0.125rem; }

.dropup .dropdown-toggle::after { display: inline-block; margin-left: 0.255=
em; vertical-align: 0.255em; content: ""; border-width: 0px 0.3em 0.3em; bo=
rder-top-style: initial; border-top-color: initial; border-right-style: sol=
id; border-right-color: transparent; border-bottom-style: solid; border-bot=
tom-color: initial; border-left-style: solid; border-left-color: transparen=
t; }

.dropup .dropdown-toggle:empty::after { margin-left: 0px; }

.dropend .dropdown-menu[data-bs-popper] { top: 0px; right: auto; left: 100%=
; margin-top: 0px; margin-left: 0.125rem; }

.dropend .dropdown-toggle::after { display: inline-block; margin-left: 0.25=
5em; vertical-align: 0.255em; content: ""; border-width: 0.3em 0px 0.3em 0.=
3em; border-top-style: solid; border-top-color: transparent; border-right-s=
tyle: initial; border-right-color: initial; border-bottom-style: solid; bor=
der-bottom-color: transparent; border-left-style: solid; border-left-color:=
 initial; }

.dropend .dropdown-toggle:empty::after { margin-left: 0px; }

.dropend .dropdown-toggle::after { vertical-align: 0px; }

.dropstart .dropdown-menu[data-bs-popper] { top: 0px; right: 100%; left: au=
to; margin-top: 0px; margin-right: 0.125rem; }

.dropstart .dropdown-toggle::after { display: inline-block; margin-left: 0.=
255em; vertical-align: 0.255em; content: ""; }

.dropstart .dropdown-toggle::after { display: none; }

.dropstart .dropdown-toggle::before { display: inline-block; margin-right: =
0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid tran=
sparent; border-right: 0.3em solid; border-bottom: 0.3em solid transparent;=
 }

.dropstart .dropdown-toggle:empty::after { margin-left: 0px; }

.dropstart .dropdown-toggle::before { vertical-align: 0px; }

.dropdown-divider { height: 0px; margin: 0.5rem 0px; overflow: hidden; bord=
er-top: 1px solid rgba(0, 0, 0, 0.15); }

.dropdown-item { display: block; width: 100%; padding: 0.25rem 1rem; clear:=
 both; font-weight: 400; color: rgb(33, 37, 41); text-align: inherit; text-=
decoration: none; white-space: nowrap; background-color: transparent; borde=
r: 0px; }

.dropdown-item:focus, .dropdown-item:hover { color: rgb(30, 33, 37); backgr=
ound-color: rgb(233, 236, 239); }

.dropdown-item.active, .dropdown-item:active { color: rgb(255, 255, 255); t=
ext-decoration: none; background-color: rgb(13, 110, 253); }

.dropdown-item.disabled, .dropdown-item:disabled { color: rgb(173, 181, 189=
); pointer-events: none; background-color: transparent; }

.dropdown-menu.show { display: block; }

.dropdown-header { display: block; padding: 0.5rem 1rem; margin-bottom: 0px=
; font-size: 0.875rem; color: rgb(108, 117, 125); white-space: nowrap; }

.dropdown-item-text { display: block; padding: 0.25rem 1rem; color: rgb(33,=
 37, 41); }

.dropdown-menu-dark { color: rgb(222, 226, 230); background-color: rgb(52, =
58, 64); border-color: rgba(0, 0, 0, 0.15); }

.dropdown-menu-dark .dropdown-item { color: rgb(222, 226, 230); }

.dropdown-menu-dark .dropdown-item:focus, .dropdown-menu-dark .dropdown-ite=
m:hover { color: rgb(255, 255, 255); background-color: rgba(255, 255, 255, =
0.15); }

.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-it=
em:active { color: rgb(255, 255, 255); background-color: rgb(13, 110, 253);=
 }

.dropdown-menu-dark .dropdown-item.disabled, .dropdown-menu-dark .dropdown-=
item:disabled { color: rgb(173, 181, 189); }

.dropdown-menu-dark .dropdown-divider { border-color: rgba(0, 0, 0, 0.15); =
}

.dropdown-menu-dark .dropdown-item-text { color: rgb(222, 226, 230); }

.dropdown-menu-dark .dropdown-header { color: rgb(173, 181, 189); }

.btn-group, .btn-group-vertical { position: relative; display: inline-flex;=
 vertical-align: middle; }

.btn-group-vertical > .btn, .btn-group > .btn { position: relative; flex: 1=
 1 auto; }

.btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn=
-check:focus + .btn, .btn-group-vertical > .btn.active, .btn-group-vertical=
 > .btn:active, .btn-group-vertical > .btn:focus, .btn-group-vertical > .bt=
n:hover, .btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:fo=
cus + .btn, .btn-group > .btn.active, .btn-group > .btn:active, .btn-group =
> .btn:focus, .btn-group > .btn:hover { z-index: 1; }

.btn-toolbar { display: flex; flex-wrap: wrap; justify-content: flex-start;=
 }

.btn-toolbar .input-group { width: auto; }

.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-chi=
ld) { margin-left: -1px; }

.btn-group > .btn-group:not(:last-child) > .btn, .btn-group > .btn:not(:las=
t-child):not(.dropdown-toggle) { border-top-right-radius: 0px; border-botto=
m-right-radius: 0px; }

.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:nth-chi=
ld(n+3), .btn-group > :not(.btn-check) + .btn { border-top-left-radius: 0px=
; border-bottom-left-radius: 0px; }

.dropdown-toggle-split { padding-right: 0.5625rem; padding-left: 0.5625rem;=
 }

.dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after, .dro=
pup .dropdown-toggle-split::after { margin-left: 0px; }

.dropstart .dropdown-toggle-split::before { margin-right: 0px; }

.btn-group-sm > .btn + .dropdown-toggle-split, .btn-sm + .dropdown-toggle-s=
plit { padding-right: 0.375rem; padding-left: 0.375rem; }

.btn-group-lg > .btn + .dropdown-toggle-split, .btn-lg + .dropdown-toggle-s=
plit { padding-right: 0.75rem; padding-left: 0.75rem; }

.btn-group-vertical { flex-direction: column; align-items: flex-start; just=
ify-content: center; }

.btn-group-vertical > .btn, .btn-group-vertical > .btn-group { width: 100%;=
 }

.btn-group-vertical > .btn-group:not(:first-child), .btn-group-vertical > .=
btn:not(:first-child) { margin-top: -1px; }

.btn-group-vertical > .btn-group:not(:last-child) > .btn, .btn-group-vertic=
al > .btn:not(:last-child):not(.dropdown-toggle) { border-bottom-right-radi=
us: 0px; border-bottom-left-radius: 0px; }

.btn-group-vertical > .btn-group:not(:first-child) > .btn, .btn-group-verti=
cal > .btn ~ .btn { border-top-left-radius: 0px; border-top-right-radius: 0=
px; }

.nav { display: flex; flex-wrap: wrap; padding-left: 0px; margin-bottom: 0p=
x; list-style: none; }

.nav-link { display: block; padding: 0.5rem 1rem; color: rgb(13, 110, 253);=
 text-decoration: none; transition: color 0.15s ease-in-out, background-col=
or 0.15s ease-in-out, border-color 0.15s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .nav-link { transition: none; }
}

.nav-link:focus, .nav-link:hover { color: rgb(10, 88, 202); }

.nav-link.disabled { color: rgb(108, 117, 125); pointer-events: none; curso=
r: default; }

.nav-tabs { border-bottom: 1px solid rgb(222, 226, 230); }

.nav-tabs .nav-link { margin-bottom: -1px; background: 0px 0px; border: 1px=
 solid transparent; border-top-left-radius: 0.25rem; border-top-right-radiu=
s: 0.25rem; }

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover { border-color: rgb(23=
3, 236, 239) rgb(233, 236, 239) rgb(222, 226, 230); isolation: isolate; }

.nav-tabs .nav-link.disabled { color: rgb(108, 117, 125); background-color:=
 transparent; border-color: transparent; }

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active { color: rgb=
(73, 80, 87); background-color: rgb(255, 255, 255); border-color: rgb(222, =
226, 230) rgb(222, 226, 230) rgb(255, 255, 255); }

.nav-tabs .dropdown-menu { margin-top: -1px; border-top-left-radius: 0px; b=
order-top-right-radius: 0px; }

.nav-pills .nav-link { background: 0px 0px; border: 0px; border-radius: 0.2=
5rem; }

.nav-pills .nav-link.active, .nav-pills .show > .nav-link { color: rgb(255,=
 255, 255); background-color: rgb(13, 110, 253); }

.nav-fill .nav-item, .nav-fill > .nav-link { flex: 1 1 auto; text-align: ce=
nter; }

.nav-justified .nav-item, .nav-justified > .nav-link { flex-basis: 0px; fle=
x-grow: 1; text-align: center; }

.nav-fill .nav-item .nav-link, .nav-justified .nav-item .nav-link { width: =
100%; }

.tab-content > .tab-pane { display: none; }

.tab-content > .active { display: block; }

.navbar { position: relative; display: flex; flex-wrap: wrap; align-items: =
center; justify-content: space-between; padding-top: 0.5rem; padding-bottom=
: 0.5rem; }

.navbar > .container, .navbar > .container-fluid, .navbar > .container-lg, =
.navbar > .container-md, .navbar > .container-sm, .navbar > .container-xl, =
.navbar > .container-xxl { display: flex; flex-wrap: inherit; align-items: =
center; justify-content: space-between; }

.navbar-brand { padding-top: 0.3125rem; padding-bottom: 0.3125rem; margin-r=
ight: 1rem; font-size: 1.25rem; text-decoration: none; white-space: nowrap;=
 }

.navbar-nav { display: flex; flex-direction: column; padding-left: 0px; mar=
gin-bottom: 0px; list-style: none; }

.navbar-nav .nav-link { padding-right: 0px; padding-left: 0px; }

.navbar-nav .dropdown-menu { position: static; }

.navbar-text { padding-top: 0.5rem; padding-bottom: 0.5rem; }

.navbar-collapse { flex-basis: 100%; flex-grow: 1; align-items: center; }

.navbar-toggler { padding: 0.25rem 0.75rem; font-size: 1.25rem; line-height=
: 1; background-color: transparent; border: 1px solid transparent; border-r=
adius: 0.25rem; transition: box-shadow 0.15s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .navbar-toggler { transition: none; }
}

.navbar-toggler:hover { text-decoration: none; }

.navbar-toggler:focus { text-decoration: none; outline: 0px; box-shadow: 0p=
x 0px 0px 0.25rem; }

.navbar-toggler-icon { display: inline-block; width: 1.5em; height: 1.5em; =
vertical-align: middle; background-repeat: no-repeat; background-position: =
center center; background-size: 100%; }

.navbar-nav-scroll { max-height: var(--bs-scroll-height,75vh); overflow-y: =
auto; }

@media (min-width: 576px) {
  .navbar-expand-sm { flex-wrap: nowrap; justify-content: flex-start; }
  .navbar-expand-sm .navbar-nav { flex-direction: row; }
  .navbar-expand-sm .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-sm .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-sm .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-sm .navbar-collapse { display: flex !important; flex-basis=
: auto; }
  .navbar-expand-sm .navbar-toggler { display: none; }
  .navbar-expand-sm .offcanvas-header { display: none; }
  .navbar-expand-sm .offcanvas { position: inherit; bottom: 0px; z-index: 1=
000; flex-grow: 1; background-color: transparent; border-right: 0px; border=
-left: 0px; transition: none; transform: none; visibility: visible !importa=
nt; }
  .navbar-expand-sm .offcanvas-bottom, .navbar-expand-sm .offcanvas-top { h=
eight: auto; border-top: 0px; border-bottom: 0px; }
  .navbar-expand-sm .offcanvas-body { display: flex; flex-grow: 0; padding:=
 0px; overflow-y: visible; }
}

@media (min-width: 768px) {
  .navbar-expand-md { flex-wrap: nowrap; justify-content: flex-start; }
  .navbar-expand-md .navbar-nav { flex-direction: row; }
  .navbar-expand-md .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-md .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-md .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-md .navbar-collapse { display: flex !important; flex-basis=
: auto; }
  .navbar-expand-md .navbar-toggler { display: none; }
  .navbar-expand-md .offcanvas-header { display: none; }
  .navbar-expand-md .offcanvas { position: inherit; bottom: 0px; z-index: 1=
000; flex-grow: 1; background-color: transparent; border-right: 0px; border=
-left: 0px; transition: none; transform: none; visibility: visible !importa=
nt; }
  .navbar-expand-md .offcanvas-bottom, .navbar-expand-md .offcanvas-top { h=
eight: auto; border-top: 0px; border-bottom: 0px; }
  .navbar-expand-md .offcanvas-body { display: flex; flex-grow: 0; padding:=
 0px; overflow-y: visible; }
}

@media (min-width: 992px) {
  .navbar-expand-lg { flex-wrap: nowrap; justify-content: flex-start; }
  .navbar-expand-lg .navbar-nav { flex-direction: row; }
  .navbar-expand-lg .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-lg .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-lg .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-lg .navbar-collapse { display: flex !important; flex-basis=
: auto; }
  .navbar-expand-lg .navbar-toggler { display: none; }
  .navbar-expand-lg .offcanvas-header { display: none; }
  .navbar-expand-lg .offcanvas { position: inherit; bottom: 0px; z-index: 1=
000; flex-grow: 1; background-color: transparent; border-right: 0px; border=
-left: 0px; transition: none; transform: none; visibility: visible !importa=
nt; }
  .navbar-expand-lg .offcanvas-bottom, .navbar-expand-lg .offcanvas-top { h=
eight: auto; border-top: 0px; border-bottom: 0px; }
  .navbar-expand-lg .offcanvas-body { display: flex; flex-grow: 0; padding:=
 0px; overflow-y: visible; }
}

@media (min-width: 1200px) {
  .navbar-expand-xl { flex-wrap: nowrap; justify-content: flex-start; }
  .navbar-expand-xl .navbar-nav { flex-direction: row; }
  .navbar-expand-xl .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-xl .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-xl .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-xl .navbar-collapse { display: flex !important; flex-basis=
: auto; }
  .navbar-expand-xl .navbar-toggler { display: none; }
  .navbar-expand-xl .offcanvas-header { display: none; }
  .navbar-expand-xl .offcanvas { position: inherit; bottom: 0px; z-index: 1=
000; flex-grow: 1; background-color: transparent; border-right: 0px; border=
-left: 0px; transition: none; transform: none; visibility: visible !importa=
nt; }
  .navbar-expand-xl .offcanvas-bottom, .navbar-expand-xl .offcanvas-top { h=
eight: auto; border-top: 0px; border-bottom: 0px; }
  .navbar-expand-xl .offcanvas-body { display: flex; flex-grow: 0; padding:=
 0px; overflow-y: visible; }
}

@media (min-width: 1400px) {
  .navbar-expand-xxl { flex-wrap: nowrap; justify-content: flex-start; }
  .navbar-expand-xxl .navbar-nav { flex-direction: row; }
  .navbar-expand-xxl .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-xxl .navbar-nav .nav-link { padding-right: 0.5rem; padding=
-left: 0.5rem; }
  .navbar-expand-xxl .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-xxl .navbar-collapse { display: flex !important; flex-basi=
s: auto; }
  .navbar-expand-xxl .navbar-toggler { display: none; }
  .navbar-expand-xxl .offcanvas-header { display: none; }
  .navbar-expand-xxl .offcanvas { position: inherit; bottom: 0px; z-index: =
1000; flex-grow: 1; background-color: transparent; border-right: 0px; borde=
r-left: 0px; transition: none; transform: none; visibility: visible !import=
ant; }
  .navbar-expand-xxl .offcanvas-bottom, .navbar-expand-xxl .offcanvas-top {=
 height: auto; border-top: 0px; border-bottom: 0px; }
  .navbar-expand-xxl .offcanvas-body { display: flex; flex-grow: 0; padding=
: 0px; overflow-y: visible; }
}

.navbar-expand { flex-wrap: nowrap; justify-content: flex-start; }

.navbar-expand .navbar-nav { flex-direction: row; }

.navbar-expand .navbar-nav .dropdown-menu { position: absolute; }

.navbar-expand .navbar-nav .nav-link { padding-right: 0.5rem; padding-left:=
 0.5rem; }

.navbar-expand .navbar-nav-scroll { overflow: visible; }

.navbar-expand .navbar-collapse { display: flex !important; flex-basis: aut=
o; }

.navbar-expand .navbar-toggler { display: none; }

.navbar-expand .offcanvas-header { display: none; }

.navbar-expand .offcanvas { position: inherit; bottom: 0px; z-index: 1000; =
flex-grow: 1; background-color: transparent; border-right: 0px; border-left=
: 0px; transition: none; transform: none; visibility: visible !important; }

.navbar-expand .offcanvas-bottom, .navbar-expand .offcanvas-top { height: a=
uto; border-top: 0px; border-bottom: 0px; }

.navbar-expand .offcanvas-body { display: flex; flex-grow: 0; padding: 0px;=
 overflow-y: visible; }

.navbar-light .navbar-brand { color: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover { colo=
r: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-nav .nav-link { color: rgba(0, 0, 0, 0.55); }

.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-l=
ink:hover { color: rgba(0, 0, 0, 0.7); }

.navbar-light .navbar-nav .nav-link.disabled { color: rgba(0, 0, 0, 0.3); }

.navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .show=
 > .nav-link { color: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-toggler { color: rgba(0, 0, 0, 0.55); border-color: r=
gba(0, 0, 0, 0.1); }

.navbar-light .navbar-toggler-icon { background-image: url("data:image/svg+=
xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 30 30'%3e%3c=
path stroke=3D'rgba%280, 0, 0, 0.55%29' stroke-linecap=3D'round' stroke-mit=
erlimit=3D'10' stroke-width=3D'2' d=3D'M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%=
3e"); }

.navbar-light .navbar-text { color: rgba(0, 0, 0, 0.55); }

.navbar-light .navbar-text a, .navbar-light .navbar-text a:focus, .navbar-l=
ight .navbar-text a:hover { color: rgba(0, 0, 0, 0.9); }

.navbar-dark .navbar-brand { color: rgb(255, 255, 255); }

.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover { color:=
 rgb(255, 255, 255); }

.navbar-dark .navbar-nav .nav-link { color: rgba(255, 255, 255, 0.55); }

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-lin=
k:hover { color: rgba(255, 255, 255, 0.75); }

.navbar-dark .navbar-nav .nav-link.disabled { color: rgba(255, 255, 255, 0.=
25); }

.navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .show >=
 .nav-link { color: rgb(255, 255, 255); }

.navbar-dark .navbar-toggler { color: rgba(255, 255, 255, 0.55); border-col=
or: rgba(255, 255, 255, 0.1); }

.navbar-dark .navbar-toggler-icon { background-image: url("data:image/svg+x=
ml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 30 30'%3e%3cp=
ath stroke=3D'rgba%28255, 255, 255, 0.55%29' stroke-linecap=3D'round' strok=
e-miterlimit=3D'10' stroke-width=3D'2' d=3D'M4 7h22M4 15h22M4 23h22'/%3e%3c=
/svg%3e"); }

.navbar-dark .navbar-text { color: rgba(255, 255, 255, 0.55); }

.navbar-dark .navbar-text a, .navbar-dark .navbar-text a:focus, .navbar-dar=
k .navbar-text a:hover { color: rgb(255, 255, 255); }

.card { position: relative; display: flex; flex-direction: column; min-widt=
h: 0px; overflow-wrap: break-word; background-color: rgb(255, 255, 255); ba=
ckground-clip: border-box; border: 1px solid rgba(0, 0, 0, 0.125); border-r=
adius: 0.25rem; }

.card > hr { margin-right: 0px; margin-left: 0px; }

.card > .list-group { border-top: inherit; border-bottom: inherit; }

.card > .list-group:first-child { border-top-width: 0px; border-top-left-ra=
dius: calc(-1px + 0.25rem); border-top-right-radius: calc(-1px + 0.25rem); =
}

.card > .list-group:last-child { border-bottom-width: 0px; border-bottom-ri=
ght-radius: calc(-1px + 0.25rem); border-bottom-left-radius: calc(-1px + 0.=
25rem); }

.card > .card-header + .list-group, .card > .list-group + .card-footer { bo=
rder-top: 0px; }

.card-body { flex: 1 1 auto; padding: 1rem; }

.card-title { margin-bottom: 0.5rem; }

.card-subtitle { margin-top: -0.25rem; margin-bottom: 0px; }

.card-text:last-child { margin-bottom: 0px; }

.card-link + .card-link { margin-left: 1rem; }

.card-header { padding: 0.5rem 1rem; margin-bottom: 0px; background-color: =
rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.125); }

.card-header:first-child { border-radius: calc(-1px + 0.25rem) calc(-1px + =
0.25rem) 0px 0px; }

.card-footer { padding: 0.5rem 1rem; background-color: rgba(0, 0, 0, 0.03);=
 border-top: 1px solid rgba(0, 0, 0, 0.125); }

.card-footer:last-child { border-radius: 0px 0px calc(-1px + 0.25rem) calc(=
-1px + 0.25rem); }

.card-header-tabs { margin-right: -0.5rem; margin-bottom: -0.5rem; margin-l=
eft: -0.5rem; border-bottom: 0px; }

.card-header-pills { margin-right: -0.5rem; margin-left: -0.5rem; }

.card-img-overlay { position: absolute; inset: 0px; padding: 1rem; border-r=
adius: calc(-1px + 0.25rem); }

.card-img, .card-img-bottom, .card-img-top { width: 100%; }

.card-img, .card-img-top { border-top-left-radius: calc(-1px + 0.25rem); bo=
rder-top-right-radius: calc(-1px + 0.25rem); }

.card-img, .card-img-bottom { border-bottom-right-radius: calc(-1px + 0.25r=
em); border-bottom-left-radius: calc(-1px + 0.25rem); }

.card-group > .card { margin-bottom: 0.75rem; }

@media (min-width: 576px) {
  .card-group { display: flex; flex-flow: wrap; }
  .card-group > .card { flex: 1 0 0%; margin-bottom: 0px; }
  .card-group > .card + .card { margin-left: 0px; border-left: 0px; }
  .card-group > .card:not(:last-child) { border-top-right-radius: 0px; bord=
er-bottom-right-radius: 0px; }
  .card-group > .card:not(:last-child) .card-header, .card-group > .card:no=
t(:last-child) .card-img-top { border-top-right-radius: 0px; }
  .card-group > .card:not(:last-child) .card-footer, .card-group > .card:no=
t(:last-child) .card-img-bottom { border-bottom-right-radius: 0px; }
  .card-group > .card:not(:first-child) { border-top-left-radius: 0px; bord=
er-bottom-left-radius: 0px; }
  .card-group > .card:not(:first-child) .card-header, .card-group > .card:n=
ot(:first-child) .card-img-top { border-top-left-radius: 0px; }
  .card-group > .card:not(:first-child) .card-footer, .card-group > .card:n=
ot(:first-child) .card-img-bottom { border-bottom-left-radius: 0px; }
}

.accordion-button { position: relative; display: flex; align-items: center;=
 width: 100%; padding: 1rem 1.25rem; font-size: 1rem; color: rgb(33, 37, 41=
); text-align: left; background-color: rgb(255, 255, 255); border: 0px; bor=
der-radius: 0px; overflow-anchor: none; transition: color 0.15s ease-in-out=
, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-s=
hadow 0.15s ease-in-out, border-radius 0.15s; }

@media (prefers-reduced-motion: reduce) {
  .accordion-button { transition: none; }
}

.accordion-button:not(.collapsed) { color: rgb(12, 99, 228); background-col=
or: rgb(231, 241, 255); box-shadow: rgba(0, 0, 0, 0.125) 0px -1px 0px inset=
; }

.accordion-button:not(.collapsed)::after { background-image: url("data:imag=
e/svg+xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16'=
 fill=3D'%230c63e4'%3e%3cpath fill-rule=3D'evenodd' d=3D'M1.646 4.646a.5.5 =
0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 =
0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); transform: rotate(-180deg); }

.accordion-button::after { flex-shrink: 0; width: 1.25rem; height: 1.25rem;=
 margin-left: auto; content: ""; background-image: url("data:image/svg+xml,=
%3csvg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16' fill=3D'%=
23212529'%3e%3cpath fill-rule=3D'evenodd' d=3D'M1.646 4.646a.5.5 0 0 1 .708=
 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.=
5 0 0 1 0-.708z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-=
size: 1.25rem; transition: transform 0.2s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .accordion-button::after { transition: none; }
}

.accordion-button:hover { z-index: 2; }

.accordion-button:focus { z-index: 3; border-color: rgb(134, 183, 254); out=
line: 0px; box-shadow: rgba(13, 110, 253, 0.25) 0px 0px 0px 0.25rem; }

.accordion-header { margin-bottom: 0px; }

.accordion-item { background-color: rgb(255, 255, 255); border: 1px solid r=
gba(0, 0, 0, 0.125); }

.accordion-item:first-of-type { border-top-left-radius: 0.25rem; border-top=
-right-radius: 0.25rem; }

.accordion-item:first-of-type .accordion-button { border-top-left-radius: c=
alc(-1px + 0.25rem); border-top-right-radius: calc(-1px + 0.25rem); }

.accordion-item:not(:first-of-type) { border-top: 0px; }

.accordion-item:last-of-type { border-bottom-right-radius: 0.25rem; border-=
bottom-left-radius: 0.25rem; }

.accordion-item:last-of-type .accordion-button.collapsed { border-bottom-ri=
ght-radius: calc(-1px + 0.25rem); border-bottom-left-radius: calc(-1px + 0.=
25rem); }

.accordion-item:last-of-type .accordion-collapse { border-bottom-right-radi=
us: 0.25rem; border-bottom-left-radius: 0.25rem; }

.accordion-body { padding: 1rem 1.25rem; }

.accordion-flush .accordion-collapse { border-width: 0px; }

.accordion-flush .accordion-item { border-right: 0px; border-left: 0px; bor=
der-radius: 0px; }

.accordion-flush .accordion-item:first-child { border-top: 0px; }

.accordion-flush .accordion-item:last-child { border-bottom: 0px; }

.accordion-flush .accordion-item .accordion-button { border-radius: 0px; }

.breadcrumb { display: flex; flex-wrap: wrap; padding: 0px; margin-bottom: =
1rem; list-style: none; }

.breadcrumb-item + .breadcrumb-item { padding-left: 0.5rem; }

.breadcrumb-item + .breadcrumb-item::before { float: left; padding-right: 0=
.5rem; color: rgb(108, 117, 125); content: var(--bs-breadcrumb-divider, "/"=
); }

.breadcrumb-item.active { color: rgb(108, 117, 125); }

.pagination { display: flex; padding-left: 0px; list-style: none; }

.page-link { position: relative; display: block; color: rgb(13, 110, 253); =
text-decoration: none; background-color: rgb(255, 255, 255); border: 1px so=
lid rgb(222, 226, 230); transition: color 0.15s ease-in-out, background-col=
or 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease=
-in-out; }

@media (prefers-reduced-motion: reduce) {
  .page-link { transition: none; }
}

.page-link:hover { z-index: 2; color: rgb(10, 88, 202); background-color: r=
gb(233, 236, 239); border-color: rgb(222, 226, 230); }

.page-link:focus { z-index: 3; color: rgb(10, 88, 202); background-color: r=
gb(233, 236, 239); outline: 0px; box-shadow: rgba(13, 110, 253, 0.25) 0px 0=
px 0px 0.25rem; }

.page-item:not(:first-child) .page-link { margin-left: -1px; }

.page-item.active .page-link { z-index: 3; color: rgb(255, 255, 255); backg=
round-color: rgb(13, 110, 253); border-color: rgb(13, 110, 253); }

.page-item.disabled .page-link { color: rgb(108, 117, 125); pointer-events:=
 none; background-color: rgb(255, 255, 255); border-color: rgb(222, 226, 23=
0); }

.page-link { padding: 0.375rem 0.75rem; }

.page-item:first-child .page-link { border-top-left-radius: 0.25rem; border=
-bottom-left-radius: 0.25rem; }

.page-item:last-child .page-link { border-top-right-radius: 0.25rem; border=
-bottom-right-radius: 0.25rem; }

.pagination-lg .page-link { padding: 0.75rem 1.5rem; font-size: 1.25rem; }

.pagination-lg .page-item:first-child .page-link { border-top-left-radius: =
0.3rem; border-bottom-left-radius: 0.3rem; }

.pagination-lg .page-item:last-child .page-link { border-top-right-radius: =
0.3rem; border-bottom-right-radius: 0.3rem; }

.pagination-sm .page-link { padding: 0.25rem 0.5rem; font-size: 0.875rem; }

.pagination-sm .page-item:first-child .page-link { border-top-left-radius: =
0.2rem; border-bottom-left-radius: 0.2rem; }

.pagination-sm .page-item:last-child .page-link { border-top-right-radius: =
0.2rem; border-bottom-right-radius: 0.2rem; }

.badge { display: inline-block; padding: 0.35em 0.65em; font-size: 0.75em; =
font-weight: 700; line-height: 1; color: rgb(255, 255, 255); text-align: ce=
nter; white-space: nowrap; vertical-align: baseline; border-radius: 0.25rem=
; }

.badge:empty { display: none; }

.btn .badge { position: relative; top: -1px; }

.alert { position: relative; padding: 1rem; margin-bottom: 1rem; border: 1p=
x solid transparent; border-radius: 0.25rem; }

.alert-heading { color: inherit; }

.alert-link { font-weight: 700; }

.alert-dismissible { padding-right: 3rem; }

.alert-dismissible .btn-close { position: absolute; top: 0px; right: 0px; z=
-index: 2; padding: 1.25rem 1rem; }

.alert-primary { color: rgb(8, 66, 152); background-color: rgb(207, 226, 25=
5); border-color: rgb(182, 212, 254); }

.alert-primary .alert-link { color: rgb(6, 53, 122); }

.alert-secondary { color: rgb(65, 70, 75); background-color: rgb(226, 227, =
229); border-color: rgb(211, 214, 216); }

.alert-secondary .alert-link { color: rgb(52, 56, 60); }

.alert-success { color: rgb(15, 81, 50); background-color: rgb(209, 231, 22=
1); border-color: rgb(186, 219, 204); }

.alert-success .alert-link { color: rgb(12, 65, 40); }

.alert-info { color: rgb(5, 81, 96); background-color: rgb(207, 244, 252); =
border-color: rgb(182, 239, 251); }

.alert-info .alert-link { color: rgb(4, 65, 77); }

.alert-warning { color: rgb(102, 77, 3); background-color: rgb(255, 243, 20=
5); border-color: rgb(255, 236, 181); }

.alert-warning .alert-link { color: rgb(82, 62, 2); }

.alert-danger { color: rgb(132, 32, 41); background-color: rgb(248, 215, 21=
8); border-color: rgb(245, 194, 199); }

.alert-danger .alert-link { color: rgb(106, 26, 33); }

.alert-light { color: rgb(99, 100, 100); background-color: rgb(254, 254, 25=
4); border-color: rgb(253, 253, 254); }

.alert-light .alert-link { color: rgb(79, 80, 80); }

.alert-dark { color: rgb(20, 22, 25); background-color: rgb(211, 211, 212);=
 border-color: rgb(188, 190, 191); }

.alert-dark .alert-link { color: rgb(16, 18, 20); }

@-webkit-keyframes progress-bar-stripes {=20
  0% { background-position-x: 1rem; }
}

@keyframes progress-bar-stripes {=20
  0% { background-position-x: 1rem; }
}

.progress { display: flex; height: 1rem; overflow: hidden; font-size: 0.75r=
em; background-color: rgb(233, 236, 239); border-radius: 0.25rem; }

.progress-bar { display: flex; flex-direction: column; justify-content: cen=
ter; overflow: hidden; color: rgb(255, 255, 255); text-align: center; white=
-space: nowrap; background-color: rgb(13, 110, 253); transition: width 0.6s=
; }

@media (prefers-reduced-motion: reduce) {
  .progress-bar { transition: none; }
}

.progress-bar-striped { background-image: linear-gradient(45deg, rgba(255, =
255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, =
0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); ba=
ckground-size: 1rem 1rem; }

.progress-bar-animated { animation: 1s linear 0s infinite normal none runni=
ng progress-bar-stripes; }

@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated { animation: auto ease 0s 1 normal none running no=
ne; }
}

.list-group { display: flex; flex-direction: column; padding-left: 0px; mar=
gin-bottom: 0px; border-radius: 0.25rem; }

.list-group-numbered { list-style-type: none; counter-reset: section 0; }

.list-group-numbered > li::before { content: counters(section, ".") ". "; c=
ounter-increment: section 1; }

.list-group-item-action { width: 100%; color: rgb(73, 80, 87); text-align: =
inherit; }

.list-group-item-action:focus, .list-group-item-action:hover { z-index: 1; =
color: rgb(73, 80, 87); text-decoration: none; background-color: rgb(248, 2=
49, 250); }

.list-group-item-action:active { color: rgb(33, 37, 41); background-color: =
rgb(233, 236, 239); }

.list-group-item { position: relative; display: block; padding: 0.5rem 1rem=
; color: rgb(33, 37, 41); text-decoration: none; background-color: rgb(255,=
 255, 255); border: 1px solid rgba(0, 0, 0, 0.125); }

.list-group-item:first-child { border-top-left-radius: inherit; border-top-=
right-radius: inherit; }

.list-group-item:last-child { border-bottom-right-radius: inherit; border-b=
ottom-left-radius: inherit; }

.list-group-item.disabled, .list-group-item:disabled { color: rgb(108, 117,=
 125); pointer-events: none; background-color: rgb(255, 255, 255); }

.list-group-item.active { z-index: 2; color: rgb(255, 255, 255); background=
-color: rgb(13, 110, 253); border-color: rgb(13, 110, 253); }

.list-group-item + .list-group-item { border-top-width: 0px; }

.list-group-item + .list-group-item.active { margin-top: -1px; border-top-w=
idth: 1px; }

.list-group-horizontal { flex-direction: row; }

.list-group-horizontal > .list-group-item:first-child { border-bottom-left-=
radius: 0.25rem; border-top-right-radius: 0px; }

.list-group-horizontal > .list-group-item:last-child { border-top-right-rad=
ius: 0.25rem; border-bottom-left-radius: 0px; }

.list-group-horizontal > .list-group-item.active { margin-top: 0px; }

.list-group-horizontal > .list-group-item + .list-group-item { border-top-w=
idth: 1px; border-left-width: 0px; }

.list-group-horizontal > .list-group-item + .list-group-item.active { margi=
n-left: -1px; border-left-width: 1px; }

@media (min-width: 576px) {
  .list-group-horizontal-sm { flex-direction: row; }
  .list-group-horizontal-sm > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-sm > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-sm > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-sm > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 768px) {
  .list-group-horizontal-md { flex-direction: row; }
  .list-group-horizontal-md > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-md > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-md > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-md > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 992px) {
  .list-group-horizontal-lg { flex-direction: row; }
  .list-group-horizontal-lg > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-lg > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-lg > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-lg > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 1200px) {
  .list-group-horizontal-xl { flex-direction: row; }
  .list-group-horizontal-xl > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-xl > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-xl > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-xl > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 1400px) {
  .list-group-horizontal-xxl { flex-direction: row; }
  .list-group-horizontal-xxl > .list-group-item:first-child { border-bottom=
-left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-xxl > .list-group-item:last-child { border-top-rig=
ht-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-xxl > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item { border=
-top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {=
 margin-left: -1px; border-left-width: 1px; }
}

.list-group-flush { border-radius: 0px; }

.list-group-flush > .list-group-item { border-width: 0px 0px 1px; }

.list-group-flush > .list-group-item:last-child { border-bottom-width: 0px;=
 }

.list-group-item-primary { color: rgb(8, 66, 152); background-color: rgb(20=
7, 226, 255); }

.list-group-item-primary.list-group-item-action:focus, .list-group-item-pri=
mary.list-group-item-action:hover { color: rgb(8, 66, 152); background-colo=
r: rgb(186, 203, 230); }

.list-group-item-primary.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(8, 66, 152); border-color: rgb(8, 66, 152); =
}

.list-group-item-secondary { color: rgb(65, 70, 75); background-color: rgb(=
226, 227, 229); }

.list-group-item-secondary.list-group-item-action:focus, .list-group-item-s=
econdary.list-group-item-action:hover { color: rgb(65, 70, 75); background-=
color: rgb(203, 204, 206); }

.list-group-item-secondary.list-group-item-action.active { color: rgb(255, =
255, 255); background-color: rgb(65, 70, 75); border-color: rgb(65, 70, 75)=
; }

.list-group-item-success { color: rgb(15, 81, 50); background-color: rgb(20=
9, 231, 221); }

.list-group-item-success.list-group-item-action:focus, .list-group-item-suc=
cess.list-group-item-action:hover { color: rgb(15, 81, 50); background-colo=
r: rgb(188, 208, 199); }

.list-group-item-success.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(15, 81, 50); border-color: rgb(15, 81, 50); =
}

.list-group-item-info { color: rgb(5, 81, 96); background-color: rgb(207, 2=
44, 252); }

.list-group-item-info.list-group-item-action:focus, .list-group-item-info.l=
ist-group-item-action:hover { color: rgb(5, 81, 96); background-color: rgb(=
186, 220, 227); }

.list-group-item-info.list-group-item-action.active { color: rgb(255, 255, =
255); background-color: rgb(5, 81, 96); border-color: rgb(5, 81, 96); }

.list-group-item-warning { color: rgb(102, 77, 3); background-color: rgb(25=
5, 243, 205); }

.list-group-item-warning.list-group-item-action:focus, .list-group-item-war=
ning.list-group-item-action:hover { color: rgb(102, 77, 3); background-colo=
r: rgb(230, 219, 185); }

.list-group-item-warning.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(102, 77, 3); border-color: rgb(102, 77, 3); =
}

.list-group-item-danger { color: rgb(132, 32, 41); background-color: rgb(24=
8, 215, 218); }

.list-group-item-danger.list-group-item-action:focus, .list-group-item-dang=
er.list-group-item-action:hover { color: rgb(132, 32, 41); background-color=
: rgb(223, 194, 196); }

.list-group-item-danger.list-group-item-action.active { color: rgb(255, 255=
, 255); background-color: rgb(132, 32, 41); border-color: rgb(132, 32, 41);=
 }

.list-group-item-light { color: rgb(99, 100, 100); background-color: rgb(25=
4, 254, 254); }

.list-group-item-light.list-group-item-action:focus, .list-group-item-light=
.list-group-item-action:hover { color: rgb(99, 100, 100); background-color:=
 rgb(229, 229, 229); }

.list-group-item-light.list-group-item-action.active { color: rgb(255, 255,=
 255); background-color: rgb(99, 100, 100); border-color: rgb(99, 100, 100)=
; }

.list-group-item-dark { color: rgb(20, 22, 25); background-color: rgb(211, =
211, 212); }

.list-group-item-dark.list-group-item-action:focus, .list-group-item-dark.l=
ist-group-item-action:hover { color: rgb(20, 22, 25); background-color: rgb=
(190, 190, 191); }

.list-group-item-dark.list-group-item-action.active { color: rgb(255, 255, =
255); background-color: rgb(20, 22, 25); border-color: rgb(20, 22, 25); }

.btn-close { box-sizing: content-box; width: 1em; height: 1em; padding: 0.2=
5em; color: rgb(0, 0, 0); background: url("data:image/svg+xml,%3csvg xmlns=
=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16' fill=3D'%23000'%3e%3c=
path d=3D'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L=
9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.41=
4-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center center =
/ 1em no-repeat transparent; border: 0px; border-radius: 0.25rem; opacity: =
0.5; }

.btn-close:hover { color: rgb(0, 0, 0); text-decoration: none; opacity: 0.7=
5; }

.btn-close:focus { outline: 0px; box-shadow: rgba(13, 110, 253, 0.25) 0px 0=
px 0px 0.25rem; opacity: 1; }

.btn-close.disabled, .btn-close:disabled { pointer-events: none; user-selec=
t: none; opacity: 0.25; }

.btn-close-white { filter: invert(1) grayscale(100%) brightness(200%); }

.toast { width: 350px; max-width: 100%; font-size: 0.875rem; pointer-events=
: auto; background-color: rgba(255, 255, 255, 0.85); background-clip: paddi=
ng-box; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: rgba(0, 0, 0, 0.1=
5) 0px 0.5rem 1rem; border-radius: 0.25rem; }

.toast.showing { opacity: 0; }

.toast:not(.show) { display: none; }

.toast-container { width: max-content; max-width: 100%; pointer-events: non=
e; }

.toast-container > :not(:last-child) { margin-bottom: 0.75rem; }

.toast-header { display: flex; align-items: center; padding: 0.5rem 0.75rem=
; color: rgb(108, 117, 125); background-color: rgba(255, 255, 255, 0.85); b=
ackground-clip: padding-box; border-bottom: 1px solid rgba(0, 0, 0, 0.05); =
border-top-left-radius: calc(-1px + 0.25rem); border-top-right-radius: calc=
(-1px + 0.25rem); }

.toast-header .btn-close { margin-right: -0.375rem; margin-left: 0.75rem; }

.toast-body { padding: 0.75rem; overflow-wrap: break-word; }

.modal { position: fixed; top: 0px; left: 0px; z-index: 1055; display: none=
; width: 100%; height: 100%; overflow: hidden auto; outline: 0px; }

.modal-dialog { position: relative; width: auto; margin: 0.5rem; pointer-ev=
ents: none; }

.modal.fade .modal-dialog { transition: transform 0.3s ease-out; transform:=
 translate(0px, -50px); }

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog { transition: none; }
}

.modal.show .modal-dialog { transform: none; }

.modal.modal-static .modal-dialog { transform: scale(1.02); }

.modal-dialog-scrollable { height: calc(100% - 1rem); }

.modal-dialog-scrollable .modal-content { max-height: 100%; overflow: hidde=
n; }

.modal-dialog-scrollable .modal-body { overflow-y: auto; }

.modal-dialog-centered { display: flex; align-items: center; min-height: ca=
lc(100% - 1rem); }

.modal-content { position: relative; display: flex; flex-direction: column;=
 width: 100%; pointer-events: auto; background-color: rgb(255, 255, 255); b=
ackground-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-r=
adius: 0.3rem; outline: 0px; }

.modal-backdrop { position: fixed; top: 0px; left: 0px; z-index: 1050; widt=
h: 100vw; height: 100vh; background-color: rgb(0, 0, 0); }

.modal-backdrop.fade { opacity: 0; }

.modal-backdrop.show { opacity: 0.5; }

.modal-header { display: flex; flex-shrink: 0; align-items: center; justify=
-content: space-between; padding: 1rem; border-bottom: 1px solid rgb(222, 2=
26, 230); border-top-left-radius: calc(-1px + 0.3rem); border-top-right-rad=
ius: calc(-1px + 0.3rem); }

.modal-header .btn-close { padding: 0.5rem; margin: -0.5rem -0.5rem -0.5rem=
 auto; }

.modal-title { margin-bottom: 0px; line-height: 1.5; }

.modal-body { position: relative; flex: 1 1 auto; padding: 1rem; }

.modal-footer { display: flex; flex-wrap: wrap; flex-shrink: 0; align-items=
: center; justify-content: flex-end; padding: 0.75rem; border-top: 1px soli=
d rgb(222, 226, 230); border-bottom-right-radius: calc(-1px + 0.3rem); bord=
er-bottom-left-radius: calc(-1px + 0.3rem); }

.modal-footer > * { margin: 0.25rem; }

@media (min-width: 576px) {
  .modal-dialog { max-width: 500px; margin: 1.75rem auto; }
  .modal-dialog-scrollable { height: calc(100% - 3.5rem); }
  .modal-dialog-centered { min-height: calc(100% - 3.5rem); }
  .modal-sm { max-width: 300px; }
}

@media (min-width: 992px) {
  .modal-lg, .modal-xl { max-width: 800px; }
}

@media (min-width: 1200px) {
  .modal-xl { max-width: 1140px; }
}

.modal-fullscreen { width: 100vw; max-width: none; height: 100%; margin: 0p=
x; }

.modal-fullscreen .modal-content { height: 100%; border: 0px; border-radius=
: 0px; }

.modal-fullscreen .modal-header { border-radius: 0px; }

.modal-fullscreen .modal-body { overflow-y: auto; }

.modal-fullscreen .modal-footer { border-radius: 0px; }

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down { width: 100vw; max-width: none; height: 100%; =
margin: 0px; }
  .modal-fullscreen-sm-down .modal-content { height: 100%; border: 0px; bor=
der-radius: 0px; }
  .modal-fullscreen-sm-down .modal-header { border-radius: 0px; }
  .modal-fullscreen-sm-down .modal-body { overflow-y: auto; }
  .modal-fullscreen-sm-down .modal-footer { border-radius: 0px; }
}

@media (max-width: 767.98px) {
  .modal-fullscreen-md-down { width: 100vw; max-width: none; height: 100%; =
margin: 0px; }
  .modal-fullscreen-md-down .modal-content { height: 100%; border: 0px; bor=
der-radius: 0px; }
  .modal-fullscreen-md-down .modal-header { border-radius: 0px; }
  .modal-fullscreen-md-down .modal-body { overflow-y: auto; }
  .modal-fullscreen-md-down .modal-footer { border-radius: 0px; }
}

@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down { width: 100vw; max-width: none; height: 100%; =
margin: 0px; }
  .modal-fullscreen-lg-down .modal-content { height: 100%; border: 0px; bor=
der-radius: 0px; }
  .modal-fullscreen-lg-down .modal-header { border-radius: 0px; }
  .modal-fullscreen-lg-down .modal-body { overflow-y: auto; }
  .modal-fullscreen-lg-down .modal-footer { border-radius: 0px; }
}

@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down { width: 100vw; max-width: none; height: 100%; =
margin: 0px; }
  .modal-fullscreen-xl-down .modal-content { height: 100%; border: 0px; bor=
der-radius: 0px; }
  .modal-fullscreen-xl-down .modal-header { border-radius: 0px; }
  .modal-fullscreen-xl-down .modal-body { overflow-y: auto; }
  .modal-fullscreen-xl-down .modal-footer { border-radius: 0px; }
}

@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down { width: 100vw; max-width: none; height: 100%;=
 margin: 0px; }
  .modal-fullscreen-xxl-down .modal-content { height: 100%; border: 0px; bo=
rder-radius: 0px; }
  .modal-fullscreen-xxl-down .modal-header { border-radius: 0px; }
  .modal-fullscreen-xxl-down .modal-body { overflow-y: auto; }
  .modal-fullscreen-xxl-down .modal-footer { border-radius: 0px; }
}

.tooltip { position: absolute; z-index: 1080; display: block; margin: 0px; =
font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 40=
0; line-height: 1.5; text-align: start; text-decoration: none; text-shadow:=
 none; text-transform: none; letter-spacing: normal; word-break: normal; wo=
rd-spacing: normal; white-space: normal; line-break: auto; font-size: 0.875=
rem; overflow-wrap: break-word; opacity: 0; }

.tooltip.show { opacity: 0.9; }

.tooltip .tooltip-arrow { position: absolute; display: block; width: 0.8rem=
; height: 0.4rem; }

.tooltip .tooltip-arrow::before { position: absolute; content: ""; border-c=
olor: transparent; border-style: solid; }

.bs-tooltip-auto[data-popper-placement^=3D"top"], .bs-tooltip-top { padding=
: 0.4rem 0px; }

.bs-tooltip-auto[data-popper-placement^=3D"top"] .tooltip-arrow, .bs-toolti=
p-top .tooltip-arrow { bottom: 0px; }

.bs-tooltip-auto[data-popper-placement^=3D"top"] .tooltip-arrow::before, .b=
s-tooltip-top .tooltip-arrow::before { top: -1px; border-width: 0.4rem 0.4r=
em 0px; border-top-color: rgb(0, 0, 0); }

.bs-tooltip-auto[data-popper-placement^=3D"right"], .bs-tooltip-end { paddi=
ng: 0px 0.4rem; }

.bs-tooltip-auto[data-popper-placement^=3D"right"] .tooltip-arrow, .bs-tool=
tip-end .tooltip-arrow { left: 0px; width: 0.4rem; height: 0.8rem; }

.bs-tooltip-auto[data-popper-placement^=3D"right"] .tooltip-arrow::before, =
.bs-tooltip-end .tooltip-arrow::before { right: -1px; border-width: 0.4rem =
0.4rem 0.4rem 0px; border-right-color: rgb(0, 0, 0); }

.bs-tooltip-auto[data-popper-placement^=3D"bottom"], .bs-tooltip-bottom { p=
adding: 0.4rem 0px; }

.bs-tooltip-auto[data-popper-placement^=3D"bottom"] .tooltip-arrow, .bs-too=
ltip-bottom .tooltip-arrow { top: 0px; }

.bs-tooltip-auto[data-popper-placement^=3D"bottom"] .tooltip-arrow::before,=
 .bs-tooltip-bottom .tooltip-arrow::before { bottom: -1px; border-width: 0p=
x 0.4rem 0.4rem; border-bottom-color: rgb(0, 0, 0); }

.bs-tooltip-auto[data-popper-placement^=3D"left"], .bs-tooltip-start { padd=
ing: 0px 0.4rem; }

.bs-tooltip-auto[data-popper-placement^=3D"left"] .tooltip-arrow, .bs-toolt=
ip-start .tooltip-arrow { right: 0px; width: 0.4rem; height: 0.8rem; }

.bs-tooltip-auto[data-popper-placement^=3D"left"] .tooltip-arrow::before, .=
bs-tooltip-start .tooltip-arrow::before { left: -1px; border-width: 0.4rem =
0px 0.4rem 0.4rem; border-left-color: rgb(0, 0, 0); }

.tooltip-inner { max-width: 200px; padding: 0.25rem 0.5rem; color: rgb(255,=
 255, 255); text-align: center; background-color: rgb(0, 0, 0); border-radi=
us: 0.25rem; }

.popover { position: absolute; top: 0px; left: 0px; z-index: 1070; display:=
 block; max-width: 276px; font-family: var(--bs-font-sans-serif); font-styl=
e: normal; font-weight: 400; line-height: 1.5; text-align: start; text-deco=
ration: none; text-shadow: none; text-transform: none; letter-spacing: norm=
al; word-break: normal; word-spacing: normal; white-space: normal; line-bre=
ak: auto; font-size: 0.875rem; overflow-wrap: break-word; background-color:=
 rgb(255, 255, 255); background-clip: padding-box; border: 1px solid rgba(0=
, 0, 0, 0.2); border-radius: 0.3rem; }

.popover .popover-arrow { position: absolute; display: block; width: 1rem; =
height: 0.5rem; }

.popover .popover-arrow::after, .popover .popover-arrow::before { position:=
 absolute; display: block; content: ""; border-color: transparent; border-s=
tyle: solid; }

.bs-popover-auto[data-popper-placement^=3D"top"] > .popover-arrow, .bs-popo=
ver-top > .popover-arrow { bottom: calc(-1px - 0.5rem); }

.bs-popover-auto[data-popper-placement^=3D"top"] > .popover-arrow::before, =
.bs-popover-top > .popover-arrow::before { bottom: 0px; border-width: 0.5re=
m 0.5rem 0px; border-top-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[data-popper-placement^=3D"top"] > .popover-arrow::after, .=
bs-popover-top > .popover-arrow::after { bottom: 1px; border-width: 0.5rem =
0.5rem 0px; border-top-color: rgb(255, 255, 255); }

.bs-popover-auto[data-popper-placement^=3D"right"] > .popover-arrow, .bs-po=
pover-end > .popover-arrow { left: calc(-1px - 0.5rem); width: 0.5rem; heig=
ht: 1rem; }

.bs-popover-auto[data-popper-placement^=3D"right"] > .popover-arrow::before=
, .bs-popover-end > .popover-arrow::before { left: 0px; border-width: 0.5re=
m 0.5rem 0.5rem 0px; border-right-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[data-popper-placement^=3D"right"] > .popover-arrow::after,=
 .bs-popover-end > .popover-arrow::after { left: 1px; border-width: 0.5rem =
0.5rem 0.5rem 0px; border-right-color: rgb(255, 255, 255); }

.bs-popover-auto[data-popper-placement^=3D"bottom"] > .popover-arrow, .bs-p=
opover-bottom > .popover-arrow { top: calc(-1px - 0.5rem); }

.bs-popover-auto[data-popper-placement^=3D"bottom"] > .popover-arrow::befor=
e, .bs-popover-bottom > .popover-arrow::before { top: 0px; border-width: 0p=
x 0.5rem 0.5rem; border-bottom-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[data-popper-placement^=3D"bottom"] > .popover-arrow::after=
, .bs-popover-bottom > .popover-arrow::after { top: 1px; border-width: 0px =
0.5rem 0.5rem; border-bottom-color: rgb(255, 255, 255); }

.bs-popover-auto[data-popper-placement^=3D"bottom"] .popover-header::before=
, .bs-popover-bottom .popover-header::before { position: absolute; top: 0px=
; left: 50%; display: block; width: 1rem; margin-left: -0.5rem; content: ""=
; border-bottom: 1px solid rgb(240, 240, 240); }

.bs-popover-auto[data-popper-placement^=3D"left"] > .popover-arrow, .bs-pop=
over-start > .popover-arrow { right: calc(-1px - 0.5rem); width: 0.5rem; he=
ight: 1rem; }

.bs-popover-auto[data-popper-placement^=3D"left"] > .popover-arrow::before,=
 .bs-popover-start > .popover-arrow::before { right: 0px; border-width: 0.5=
rem 0px 0.5rem 0.5rem; border-left-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[data-popper-placement^=3D"left"] > .popover-arrow::after, =
.bs-popover-start > .popover-arrow::after { right: 1px; border-width: 0.5re=
m 0px 0.5rem 0.5rem; border-left-color: rgb(255, 255, 255); }

.popover-header { padding: 0.5rem 1rem; margin-bottom: 0px; font-size: 1rem=
; background-color: rgb(240, 240, 240); border-bottom: 1px solid rgba(0, 0,=
 0, 0.2); border-top-left-radius: calc(-1px + 0.3rem); border-top-right-rad=
ius: calc(-1px + 0.3rem); }

.popover-header:empty { display: none; }

.popover-body { padding: 1rem; color: rgb(33, 37, 41); }

.carousel { position: relative; }

.carousel.pointer-event { touch-action: pan-y; }

.carousel-inner { position: relative; width: 100%; overflow: hidden; }

.carousel-inner::after { display: block; clear: both; content: ""; }

.carousel-item { position: relative; display: none; float: left; width: 100=
%; margin-right: -100%; backface-visibility: hidden; transition: transform =
0.6s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .carousel-item { transition: none; }
}

.carousel-item-next, .carousel-item-prev, .carousel-item.active { display: =
block; }

.active.carousel-item-end, .carousel-item-next:not(.carousel-item-start) { =
transform: translateX(100%); }

.active.carousel-item-start, .carousel-item-prev:not(.carousel-item-end) { =
transform: translateX(-100%); }

.carousel-fade .carousel-item { opacity: 0; transition-property: opacity; t=
ransform: none; }

.carousel-fade .carousel-item-next.carousel-item-start, .carousel-fade .car=
ousel-item-prev.carousel-item-end, .carousel-fade .carousel-item.active { z=
-index: 1; opacity: 1; }

.carousel-fade .active.carousel-item-end, .carousel-fade .active.carousel-i=
tem-start { z-index: 0; opacity: 0; transition: opacity 0.6s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-end, .carousel-fade .active.carousel=
-item-start { transition: none; }
}

.carousel-control-next, .carousel-control-prev { position: absolute; top: 0=
px; bottom: 0px; z-index: 1; display: flex; align-items: center; justify-co=
ntent: center; width: 15%; padding: 0px; color: rgb(255, 255, 255); text-al=
ign: center; background: 0px 0px; border: 0px; opacity: 0.5; transition: op=
acity 0.15s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-control-next, .carousel-control-prev { transition: none; }
}

.carousel-control-next:focus, .carousel-control-next:hover, .carousel-contr=
ol-prev:focus, .carousel-control-prev:hover { color: rgb(255, 255, 255); te=
xt-decoration: none; outline: 0px; opacity: 0.9; }

.carousel-control-prev { left: 0px; }

.carousel-control-next { right: 0px; }

.carousel-control-next-icon, .carousel-control-prev-icon { display: inline-=
block; width: 2rem; height: 2rem; background-repeat: no-repeat; background-=
position: 50% center; background-size: 100% 100%; }

.carousel-control-prev-icon { background-image: url("data:image/svg+xml,%3c=
svg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16' fill=3D'%23f=
ff'%3e%3cpath d=3D'M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5=
 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3=
e"); }

.carousel-control-next-icon { background-image: url("data:image/svg+xml,%3c=
svg xmlns=3D'http://www.w3.org/2000/svg' viewBox=3D'0 0 16 16' fill=3D'%23f=
ff'%3e%3cpath d=3D'M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 =
6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%=
3e"); }

.carousel-indicators { position: absolute; right: 0px; bottom: 0px; left: 0=
px; z-index: 2; display: flex; justify-content: center; padding: 0px; margi=
n-right: 15%; margin-bottom: 1rem; margin-left: 15%; list-style: none; }

.carousel-indicators [data-bs-target] { box-sizing: content-box; flex: 0 1 =
auto; width: 30px; height: 3px; padding: 0px; margin-right: 3px; margin-lef=
t: 3px; text-indent: -999px; cursor: pointer; background-color: rgb(255, 25=
5, 255); background-clip: padding-box; border-width: 10px 0px; border-right=
-style: initial; border-left-style: initial; border-right-color: initial; b=
order-left-color: initial; border-image: initial; border-top-style: solid; =
border-top-color: transparent; border-bottom-style: solid; border-bottom-co=
lor: transparent; opacity: 0.5; transition: opacity 0.6s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] { transition: none; }
}

.carousel-indicators .active { opacity: 1; }

.carousel-caption { position: absolute; right: 15%; bottom: 1.25rem; left: =
15%; padding-top: 1.25rem; padding-bottom: 1.25rem; color: rgb(255, 255, 25=
5); text-align: center; }

.carousel-dark .carousel-control-next-icon, .carousel-dark .carousel-contro=
l-prev-icon { filter: invert(1) grayscale(1); }

.carousel-dark .carousel-indicators [data-bs-target] { background-color: rg=
b(0, 0, 0); }

.carousel-dark .carousel-caption { color: rgb(0, 0, 0); }

@-webkit-keyframes spinner-border {=20
  100% { transform: rotate(360deg); }
}

@keyframes spinner-border {=20
  100% { transform: rotate(360deg); }
}

.spinner-border { display: inline-block; width: 2rem; height: 2rem; vertica=
l-align: -0.125em; border-width: 0.25em; border-style: solid; border-color:=
 currentcolor transparent currentcolor currentcolor; border-image: initial;=
 border-radius: 50%; animation: 0.75s linear 0s infinite normal none runnin=
g spinner-border; }

.spinner-border-sm { width: 1rem; height: 1rem; border-width: 0.2em; }

@-webkit-keyframes spinner-grow {=20
  0% { transform: scale(0); }
  50% { opacity: 1; transform: none; }
}

@keyframes spinner-grow {=20
  0% { transform: scale(0); }
  50% { opacity: 1; transform: none; }
}

.spinner-grow { display: inline-block; width: 2rem; height: 2rem; vertical-=
align: -0.125em; background-color: currentcolor; border-radius: 50%; opacit=
y: 0; animation: 0.75s linear 0s infinite normal none running spinner-grow;=
 }

.spinner-grow-sm { width: 1rem; height: 1rem; }

@media (prefers-reduced-motion: reduce) {
  .spinner-border, .spinner-grow { animation-duration: 1.5s; }
}

.offcanvas { position: fixed; bottom: 0px; z-index: 1045; display: flex; fl=
ex-direction: column; max-width: 100%; visibility: hidden; background-color=
: rgb(255, 255, 255); background-clip: padding-box; outline: 0px; transitio=
n: transform 0.3s ease-in-out; }

@media (prefers-reduced-motion: reduce) {
  .offcanvas { transition: none; }
}

.offcanvas-backdrop { position: fixed; top: 0px; left: 0px; z-index: 1040; =
width: 100vw; height: 100vh; background-color: rgb(0, 0, 0); }

.offcanvas-backdrop.fade { opacity: 0; }

.offcanvas-backdrop.show { opacity: 0.5; }

.offcanvas-header { display: flex; align-items: center; justify-content: sp=
ace-between; padding: 1rem; }

.offcanvas-header .btn-close { padding: 0.5rem; margin-top: -0.5rem; margin=
-right: -0.5rem; margin-bottom: -0.5rem; }

.offcanvas-title { margin-bottom: 0px; line-height: 1.5; }

.offcanvas-body { flex-grow: 1; padding: 1rem; overflow-y: auto; }

.offcanvas-start { top: 0px; left: 0px; width: 400px; border-right: 1px sol=
id rgba(0, 0, 0, 0.2); transform: translateX(-100%); }

.offcanvas-end { top: 0px; right: 0px; width: 400px; border-left: 1px solid=
 rgba(0, 0, 0, 0.2); transform: translateX(100%); }

.offcanvas-top { top: 0px; right: 0px; left: 0px; height: 30vh; max-height:=
 100%; border-bottom: 1px solid rgba(0, 0, 0, 0.2); transform: translateY(-=
100%); }

.offcanvas-bottom { right: 0px; left: 0px; height: 30vh; max-height: 100%; =
border-top: 1px solid rgba(0, 0, 0, 0.2); transform: translateY(100%); }

.offcanvas.show { transform: none; }

.placeholder { display: inline-block; min-height: 1em; vertical-align: midd=
le; cursor: wait; background-color: currentcolor; opacity: 0.5; }

.placeholder.btn::before { display: inline-block; content: ""; }

.placeholder-xs { min-height: 0.6em; }

.placeholder-sm { min-height: 0.8em; }

.placeholder-lg { min-height: 1.2em; }

.placeholder-glow .placeholder { animation: 2s ease-in-out 0s infinite norm=
al none running placeholder-glow; }

@-webkit-keyframes placeholder-glow {=20
  50% { opacity: 0.2; }
}

@keyframes placeholder-glow {=20
  50% { opacity: 0.2; }
}

.placeholder-wave { mask-image: linear-gradient(130deg, rgb(0, 0, 0) 55%, r=
gba(0, 0, 0, 0.8) 75%, rgb(0, 0, 0) 95%); mask-size: 200% 100%; animation: =
2s linear 0s infinite normal none running placeholder-wave; }

@-webkit-keyframes placeholder-wave {=20
  100% { mask-position: -200% 0%; }
}

@keyframes placeholder-wave {=20
  100% { mask-position: -200% 0%; }
}

.clearfix::after { display: block; clear: both; content: ""; }

.link-primary { color: rgb(13, 110, 253); }

.link-primary:focus, .link-primary:hover { color: rgb(10, 88, 202); }

.link-secondary { color: rgb(108, 117, 125); }

.link-secondary:focus, .link-secondary:hover { color: rgb(86, 94, 100); }

.link-success { color: rgb(25, 135, 84); }

.link-success:focus, .link-success:hover { color: rgb(20, 108, 67); }

.link-info { color: rgb(13, 202, 240); }

.link-info:focus, .link-info:hover { color: rgb(61, 213, 243); }

.link-warning { color: rgb(255, 193, 7); }

.link-warning:focus, .link-warning:hover { color: rgb(255, 205, 57); }

.link-danger { color: rgb(220, 53, 69); }

.link-danger:focus, .link-danger:hover { color: rgb(176, 42, 55); }

.link-light { color: rgb(248, 249, 250); }

.link-light:focus, .link-light:hover { color: rgb(249, 250, 251); }

.link-dark { color: rgb(33, 37, 41); }

.link-dark:focus, .link-dark:hover { color: rgb(26, 30, 33); }

.ratio { position: relative; width: 100%; }

.ratio::before { display: block; padding-top: var(--bs-aspect-ratio); conte=
nt: ""; }

.ratio > * { position: absolute; top: 0px; left: 0px; width: 100%; height: =
100%; }

.ratio-1x1 { --bs-aspect-ratio: 100%; }

.ratio-4x3 { --bs-aspect-ratio: 75%; }

.ratio-16x9 { --bs-aspect-ratio: 56.25%; }

.ratio-21x9 { --bs-aspect-ratio: 42.8571428571%; }

.fixed-top { position: fixed; top: 0px; right: 0px; left: 0px; z-index: 103=
0; }

.fixed-bottom { position: fixed; right: 0px; bottom: 0px; left: 0px; z-inde=
x: 1030; }

.sticky-top { position: sticky; top: 0px; z-index: 1020; }

@media (min-width: 576px) {
  .sticky-sm-top { position: sticky; top: 0px; z-index: 1020; }
}

@media (min-width: 768px) {
  .sticky-md-top { position: sticky; top: 0px; z-index: 1020; }
}

@media (min-width: 992px) {
  .sticky-lg-top { position: sticky; top: 0px; z-index: 1020; }
}

@media (min-width: 1200px) {
  .sticky-xl-top { position: sticky; top: 0px; z-index: 1020; }
}

@media (min-width: 1400px) {
  .sticky-xxl-top { position: sticky; top: 0px; z-index: 1020; }
}

.hstack { display: flex; flex-direction: row; align-items: center; align-se=
lf: stretch; }

.vstack { display: flex; flex: 1 1 auto; flex-direction: column; align-self=
: stretch; }

.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within)=
 { position: absolute !important; width: 1px !important; height: 1px !impor=
tant; padding: 0px !important; margin: -1px !important; overflow: hidden !i=
mportant; clip: rect(0px, 0px, 0px, 0px) !important; white-space: nowrap !i=
mportant; border: 0px !important; }

.stretched-link::after { position: absolute; inset: 0px; z-index: 1; conten=
t: ""; }

.text-truncate { overflow: hidden; text-overflow: ellipsis; white-space: no=
wrap; }

.vr { display: inline-block; align-self: stretch; width: 1px; min-height: 1=
em; background-color: currentcolor; opacity: 0.25; }

.align-baseline { vertical-align: baseline !important; }

.align-top { vertical-align: top !important; }

.align-middle { vertical-align: middle !important; }

.align-bottom { vertical-align: bottom !important; }

.align-text-bottom { vertical-align: text-bottom !important; }

.align-text-top { vertical-align: text-top !important; }

.float-start { float: left !important; }

.float-end { float: right !important; }

.float-none { float: none !important; }

.opacity-0 { opacity: 0 !important; }

.opacity-25 { opacity: 0.25 !important; }

.opacity-50 { opacity: 0.5 !important; }

.opacity-75 { opacity: 0.75 !important; }

.opacity-100 { opacity: 1 !important; }

.overflow-auto { overflow: auto !important; }

.overflow-hidden { overflow: hidden !important; }

.overflow-visible { overflow: visible !important; }

.overflow-scroll { overflow: scroll !important; }

.d-inline { display: inline !important; }

.d-inline-block { display: inline-block !important; }

.d-block { display: block !important; }

.d-grid { display: grid !important; }

.d-table { display: table !important; }

.d-table-row { display: table-row !important; }

.d-table-cell { display: table-cell !important; }

.d-flex { display: flex !important; }

.d-inline-flex { display: inline-flex !important; }

.d-none { display: none !important; }

.shadow { box-shadow: rgba(0, 0, 0, 0.15) 0px 0.5rem 1rem !important; }

.shadow-sm { box-shadow: rgba(0, 0, 0, 0.075) 0px 0.125rem 0.25rem !importa=
nt; }

.shadow-lg { box-shadow: rgba(0, 0, 0, 0.176) 0px 1rem 3rem !important; }

.shadow-none { box-shadow: none !important; }

.position-static { position: static !important; }

.position-relative { position: relative !important; }

.position-absolute { position: absolute !important; }

.position-fixed { position: fixed !important; }

.position-sticky { position: sticky !important; }

.top-0 { top: 0px !important; }

.top-50 { top: 50% !important; }

.top-100 { top: 100% !important; }

.bottom-0 { bottom: 0px !important; }

.bottom-50 { bottom: 50% !important; }

.bottom-100 { bottom: 100% !important; }

.start-0 { left: 0px !important; }

.start-50 { left: 50% !important; }

.start-100 { left: 100% !important; }

.end-0 { right: 0px !important; }

.end-50 { right: 50% !important; }

.end-100 { right: 100% !important; }

.translate-middle { transform: translate(-50%, -50%) !important; }

.translate-middle-x { transform: translateX(-50%) !important; }

.translate-middle-y { transform: translateY(-50%) !important; }

.border { border: 1px solid rgb(222, 226, 230) !important; }

.border-0 { border: 0px !important; }

.border-top { border-top: 1px solid rgb(222, 226, 230) !important; }

.border-top-0 { border-top: 0px !important; }

.border-end { border-right: 1px solid rgb(222, 226, 230) !important; }

.border-end-0 { border-right: 0px !important; }

.border-bottom { border-bottom: 1px solid rgb(222, 226, 230) !important; }

.border-bottom-0 { border-bottom: 0px !important; }

.border-start { border-left: 1px solid rgb(222, 226, 230) !important; }

.border-start-0 { border-left: 0px !important; }

.border-primary { border-color: rgb(13, 110, 253) !important; }

.border-secondary { border-color: rgb(108, 117, 125) !important; }

.border-success { border-color: rgb(25, 135, 84) !important; }

.border-info { border-color: rgb(13, 202, 240) !important; }

.border-warning { border-color: rgb(255, 193, 7) !important; }

.border-danger { border-color: rgb(220, 53, 69) !important; }

.border-light { border-color: rgb(248, 249, 250) !important; }

.border-dark { border-color: rgb(33, 37, 41) !important; }

.border-white { border-color: rgb(255, 255, 255) !important; }

.border-1 { border-width: 1px !important; }

.border-2 { border-width: 2px !important; }

.border-3 { border-width: 3px !important; }

.border-4 { border-width: 4px !important; }

.border-5 { border-width: 5px !important; }

.w-25 { width: 25% !important; }

.w-50 { width: 50% !important; }

.w-75 { width: 75% !important; }

.w-100 { width: 100% !important; }

.w-auto { width: auto !important; }

.mw-100 { max-width: 100% !important; }

.vw-100 { width: 100vw !important; }

.min-vw-100 { min-width: 100vw !important; }

.h-25 { height: 25% !important; }

.h-50 { height: 50% !important; }

.h-75 { height: 75% !important; }

.h-100 { height: 100% !important; }

.h-auto { height: auto !important; }

.mh-100 { max-height: 100% !important; }

.vh-100 { height: 100vh !important; }

.min-vh-100 { min-height: 100vh !important; }

.flex-fill { flex: 1 1 auto !important; }

.flex-row { flex-direction: row !important; }

.flex-column { flex-direction: column !important; }

.flex-row-reverse { flex-direction: row-reverse !important; }

.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-grow-0 { flex-grow: 0 !important; }

.flex-grow-1 { flex-grow: 1 !important; }

.flex-shrink-0 { flex-shrink: 0 !important; }

.flex-shrink-1 { flex-shrink: 1 !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-nowrap { flex-wrap: nowrap !important; }

.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.gap-0 { gap: 0px !important; }

.gap-1 { gap: 0.25rem !important; }

.gap-2 { gap: 0.5rem !important; }

.gap-3 { gap: 1rem !important; }

.gap-4 { gap: 1.5rem !important; }

.gap-5 { gap: 3rem !important; }

.justify-content-start { justify-content: flex-start !important; }

.justify-content-end { justify-content: flex-end !important; }

.justify-content-center { justify-content: center !important; }

.justify-content-between { justify-content: space-between !important; }

.justify-content-around { justify-content: space-around !important; }

.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }

.align-items-end { align-items: flex-end !important; }

.align-items-center { align-items: center !important; }

.align-items-baseline { align-items: baseline !important; }

.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }

.align-content-end { align-content: flex-end !important; }

.align-content-center { align-content: center !important; }

.align-content-between { align-content: space-between !important; }

.align-content-around { align-content: space-around !important; }

.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }

.align-self-start { align-self: flex-start !important; }

.align-self-end { align-self: flex-end !important; }

.align-self-center { align-self: center !important; }

.align-self-baseline { align-self: baseline !important; }

.align-self-stretch { align-self: stretch !important; }

.order-first { order: -1 !important; }

.order-0 { order: 0 !important; }

.order-1 { order: 1 !important; }

.order-2 { order: 2 !important; }

.order-3 { order: 3 !important; }

.order-4 { order: 4 !important; }

.order-5 { order: 5 !important; }

.order-last { order: 6 !important; }

.m-0 { margin: 0px !important; }

.m-1 { margin: 0.25rem !important; }

.m-2 { margin: 0.5rem !important; }

.m-3 { margin: 1rem !important; }

.m-4 { margin: 1.5rem !important; }

.m-5 { margin: 3rem !important; }

.m-auto { margin: auto !important; }

.mx-0 { margin-right: 0px !important; margin-left: 0px !important; }

.mx-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !important; =
}

.mx-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !important; }

.mx-3 { margin-right: 1rem !important; margin-left: 1rem !important; }

.mx-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !important; }

.mx-5 { margin-right: 3rem !important; margin-left: 3rem !important; }

.mx-auto { margin-right: auto !important; margin-left: auto !important; }

.my-0 { margin-top: 0px !important; margin-bottom: 0px !important; }

.my-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !important; =
}

.my-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }

.my-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }

.my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }

.my-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }

.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

.mt-0 { margin-top: 0px !important; }

.mt-1 { margin-top: 0.25rem !important; }

.mt-2 { margin-top: 0.5rem !important; }

.mt-3 { margin-top: 1rem !important; }

.mt-4 { margin-top: 1.5rem !important; }

.mt-5 { margin-top: 3rem !important; }

.mt-auto { margin-top: auto !important; }

.me-0 { margin-right: 0px !important; }

.me-1 { margin-right: 0.25rem !important; }

.me-2 { margin-right: 0.5rem !important; }

.me-3 { margin-right: 1rem !important; }

.me-4 { margin-right: 1.5rem !important; }

.me-5 { margin-right: 3rem !important; }

.me-auto { margin-right: auto !important; }

.mb-0 { margin-bottom: 0px !important; }

.mb-1 { margin-bottom: 0.25rem !important; }

.mb-2 { margin-bottom: 0.5rem !important; }

.mb-3 { margin-bottom: 1rem !important; }

.mb-4 { margin-bottom: 1.5rem !important; }

.mb-5 { margin-bottom: 3rem !important; }

.mb-auto { margin-bottom: auto !important; }

.ms-0 { margin-left: 0px !important; }

.ms-1 { margin-left: 0.25rem !important; }

.ms-2 { margin-left: 0.5rem !important; }

.ms-3 { margin-left: 1rem !important; }

.ms-4 { margin-left: 1.5rem !important; }

.ms-5 { margin-left: 3rem !important; }

.ms-auto { margin-left: auto !important; }

.p-0 { padding: 0px !important; }

.p-1 { padding: 0.25rem !important; }

.p-2 { padding: 0.5rem !important; }

.p-3 { padding: 1rem !important; }

.p-4 { padding: 1.5rem !important; }

.p-5 { padding: 3rem !important; }

.px-0 { padding-right: 0px !important; padding-left: 0px !important; }

.px-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !important=
; }

.px-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !important; =
}

.px-3 { padding-right: 1rem !important; padding-left: 1rem !important; }

.px-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !important; =
}

.px-5 { padding-right: 3rem !important; padding-left: 3rem !important; }

.py-0 { padding-top: 0px !important; padding-bottom: 0px !important; }

.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important=
; }

.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; =
}

.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }

.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; =
}

.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

.pt-0 { padding-top: 0px !important; }

.pt-1 { padding-top: 0.25rem !important; }

.pt-2 { padding-top: 0.5rem !important; }

.pt-3 { padding-top: 1rem !important; }

.pt-4 { padding-top: 1.5rem !important; }

.pt-5 { padding-top: 3rem !important; }

.pe-0 { padding-right: 0px !important; }

.pe-1 { padding-right: 0.25rem !important; }

.pe-2 { padding-right: 0.5rem !important; }

.pe-3 { padding-right: 1rem !important; }

.pe-4 { padding-right: 1.5rem !important; }

.pe-5 { padding-right: 3rem !important; }

.pb-0 { padding-bottom: 0px !important; }

.pb-1 { padding-bottom: 0.25rem !important; }

.pb-2 { padding-bottom: 0.5rem !important; }

.pb-3 { padding-bottom: 1rem !important; }

.pb-4 { padding-bottom: 1.5rem !important; }

.pb-5 { padding-bottom: 3rem !important; }

.ps-0 { padding-left: 0px !important; }

.ps-1 { padding-left: 0.25rem !important; }

.ps-2 { padding-left: 0.5rem !important; }

.ps-3 { padding-left: 1rem !important; }

.ps-4 { padding-left: 1.5rem !important; }

.ps-5 { padding-left: 3rem !important; }

.font-monospace { font-family: var(--bs-font-monospace) !important; }

.fs-1 { font-size: calc(1.375rem + 1.5vw) !important; }

.fs-2 { font-size: calc(1.325rem + 0.9vw) !important; }

.fs-3 { font-size: calc(1.3rem + 0.6vw) !important; }

.fs-4 { font-size: calc(1.275rem + 0.3vw) !important; }

.fs-5 { font-size: 1.25rem !important; }

.fs-6 { font-size: 1rem !important; }

.fst-italic { font-style: italic !important; }

.fst-normal { font-style: normal !important; }

.fw-light { font-weight: 300 !important; }

.fw-lighter { font-weight: lighter !important; }

.fw-normal { font-weight: 400 !important; }

.fw-bold { font-weight: 700 !important; }

.fw-bolder { font-weight: bolder !important; }

.lh-1 { line-height: 1 !important; }

.lh-sm { line-height: 1.25 !important; }

.lh-base { line-height: 1.5 !important; }

.lh-lg { line-height: 2 !important; }

.text-start { text-align: left !important; }

.text-end { text-align: right !important; }

.text-center { text-align: center !important; }

.text-decoration-none { text-decoration: none !important; }

.text-decoration-underline { text-decoration: underline !important; }

.text-decoration-line-through { text-decoration: line-through !important; }

.text-lowercase { text-transform: lowercase !important; }

.text-uppercase { text-transform: uppercase !important; }

.text-capitalize { text-transform: capitalize !important; }

.text-wrap { white-space: normal !important; }

.text-nowrap { white-space: nowrap !important; }

.text-break { overflow-wrap: break-word !important; word-break: break-word =
!important; }

.text-primary { --bs-text-opacity: 1; color: rgba(var(--bs-primary-rgb),var=
(--bs-text-opacity)) !important; }

.text-secondary { --bs-text-opacity: 1; color: rgba(var(--bs-secondary-rgb)=
,var(--bs-text-opacity)) !important; }

.text-success { --bs-text-opacity: 1; color: rgba(var(--bs-success-rgb),var=
(--bs-text-opacity)) !important; }

.text-info { --bs-text-opacity: 1; color: rgba(var(--bs-info-rgb),var(--bs-=
text-opacity)) !important; }

.text-warning { --bs-text-opacity: 1; color: rgba(var(--bs-warning-rgb),var=
(--bs-text-opacity)) !important; }

.text-danger { --bs-text-opacity: 1; color: rgba(var(--bs-danger-rgb),var(-=
-bs-text-opacity)) !important; }

.text-light { --bs-text-opacity: 1; color: rgba(var(--bs-light-rgb),var(--b=
s-text-opacity)) !important; }

.text-dark { --bs-text-opacity: 1; color: rgba(var(--bs-dark-rgb),var(--bs-=
text-opacity)) !important; }

.text-black { --bs-text-opacity: 1; color: rgba(var(--bs-black-rgb),var(--b=
s-text-opacity)) !important; }

.text-white { --bs-text-opacity: 1; color: rgba(var(--bs-white-rgb),var(--b=
s-text-opacity)) !important; }

.text-body { --bs-text-opacity: 1; color: rgba(var(--bs-body-color-rgb),var=
(--bs-text-opacity)) !important; }

.text-muted { --bs-text-opacity: 1; color: rgb(108, 117, 125) !important; }

.text-black-50 { --bs-text-opacity: 1; color: rgba(0, 0, 0, 0.5) !important=
; }

.text-white-50 { --bs-text-opacity: 1; color: rgba(255, 255, 255, 0.5) !imp=
ortant; }

.text-reset { --bs-text-opacity: 1; color: inherit !important; }

.text-opacity-25 { --bs-text-opacity: 0.25; }

.text-opacity-50 { --bs-text-opacity: 0.5; }

.text-opacity-75 { --bs-text-opacity: 0.75; }

.text-opacity-100 { --bs-text-opacity: 1; }

.bg-primary { --bs-bg-opacity: 1; background-color: rgba(var(--bs-primary-r=
gb),var(--bs-bg-opacity)) !important; }

.bg-secondary { --bs-bg-opacity: 1; background-color: rgba(var(--bs-seconda=
ry-rgb),var(--bs-bg-opacity)) !important; }

.bg-success { --bs-bg-opacity: 1; background-color: rgba(var(--bs-success-r=
gb),var(--bs-bg-opacity)) !important; }

.bg-info { --bs-bg-opacity: 1; background-color: rgba(var(--bs-info-rgb),va=
r(--bs-bg-opacity)) !important; }

.bg-warning { --bs-bg-opacity: 1; background-color: rgba(var(--bs-warning-r=
gb),var(--bs-bg-opacity)) !important; }

.bg-danger { --bs-bg-opacity: 1; background-color: rgba(var(--bs-danger-rgb=
),var(--bs-bg-opacity)) !important; }

.bg-light { --bs-bg-opacity: 1; background-color: rgba(var(--bs-light-rgb),=
var(--bs-bg-opacity)) !important; }

.bg-dark { --bs-bg-opacity: 1; background-color: rgba(var(--bs-dark-rgb),va=
r(--bs-bg-opacity)) !important; }

.bg-black { --bs-bg-opacity: 1; background-color: rgba(var(--bs-black-rgb),=
var(--bs-bg-opacity)) !important; }

.bg-white { --bs-bg-opacity: 1; background-color: rgba(var(--bs-white-rgb),=
var(--bs-bg-opacity)) !important; }

.bg-body { --bs-bg-opacity: 1; background-color: rgba(var(--bs-body-bg-rgb)=
,var(--bs-bg-opacity)) !important; }

.bg-transparent { --bs-bg-opacity: 1; background-color: transparent !import=
ant; }

.bg-opacity-10 { --bs-bg-opacity: 0.1; }

.bg-opacity-25 { --bs-bg-opacity: 0.25; }

.bg-opacity-50 { --bs-bg-opacity: 0.5; }

.bg-opacity-75 { --bs-bg-opacity: 0.75; }

.bg-opacity-100 { --bs-bg-opacity: 1; }

.bg-gradient { background-image: var(--bs-gradient) !important; }

.user-select-all { user-select: all !important; }

.user-select-auto { user-select: auto !important; }

.user-select-none { user-select: none !important; }

.pe-none { pointer-events: none !important; }

.pe-auto { pointer-events: auto !important; }

.rounded { border-radius: 0.25rem !important; }

.rounded-0 { border-radius: 0px !important; }

.rounded-1 { border-radius: 0.2rem !important; }

.rounded-2 { border-radius: 0.25rem !important; }

.rounded-3 { border-radius: 0.3rem !important; }

.rounded-circle { border-radius: 50% !important; }

.rounded-pill { border-radius: 50rem !important; }

.rounded-top { border-top-left-radius: 0.25rem !important; border-top-right=
-radius: 0.25rem !important; }

.rounded-end { border-top-right-radius: 0.25rem !important; border-bottom-r=
ight-radius: 0.25rem !important; }

.rounded-bottom { border-bottom-right-radius: 0.25rem !important; border-bo=
ttom-left-radius: 0.25rem !important; }

.rounded-start { border-bottom-left-radius: 0.25rem !important; border-top-=
left-radius: 0.25rem !important; }

.visible { visibility: visible !important; }

.invisible { visibility: hidden !important; }

@media (min-width: 576px) {
  .float-sm-start { float: left !important; }
  .float-sm-end { float: right !important; }
  .float-sm-none { float: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-grid { display: grid !important; }
  .d-sm-table { display: table !important; }
  .d-sm-table-row { display: table-row !important; }
  .d-sm-table-cell { display: table-cell !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
  .d-sm-none { display: none !important; }
  .flex-sm-fill { flex: 1 1 auto !important; }
  .flex-sm-row { flex-direction: row !important; }
  .flex-sm-column { flex-direction: column !important; }
  .flex-sm-row-reverse { flex-direction: row-reverse !important; }
  .flex-sm-column-reverse { flex-direction: column-reverse !important; }
  .flex-sm-grow-0 { flex-grow: 0 !important; }
  .flex-sm-grow-1 { flex-grow: 1 !important; }
  .flex-sm-shrink-0 { flex-shrink: 0 !important; }
  .flex-sm-shrink-1 { flex-shrink: 1 !important; }
  .flex-sm-wrap { flex-wrap: wrap !important; }
  .flex-sm-nowrap { flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .gap-sm-0 { gap: 0px !important; }
  .gap-sm-1 { gap: 0.25rem !important; }
  .gap-sm-2 { gap: 0.5rem !important; }
  .gap-sm-3 { gap: 1rem !important; }
  .gap-sm-4 { gap: 1.5rem !important; }
  .gap-sm-5 { gap: 3rem !important; }
  .justify-content-sm-start { justify-content: flex-start !important; }
  .justify-content-sm-end { justify-content: flex-end !important; }
  .justify-content-sm-center { justify-content: center !important; }
  .justify-content-sm-between { justify-content: space-between !important; =
}
  .justify-content-sm-around { justify-content: space-around !important; }
  .justify-content-sm-evenly { justify-content: space-evenly !important; }
  .align-items-sm-start { align-items: flex-start !important; }
  .align-items-sm-end { align-items: flex-end !important; }
  .align-items-sm-center { align-items: center !important; }
  .align-items-sm-baseline { align-items: baseline !important; }
  .align-items-sm-stretch { align-items: stretch !important; }
  .align-content-sm-start { align-content: flex-start !important; }
  .align-content-sm-end { align-content: flex-end !important; }
  .align-content-sm-center { align-content: center !important; }
  .align-content-sm-between { align-content: space-between !important; }
  .align-content-sm-around { align-content: space-around !important; }
  .align-content-sm-stretch { align-content: stretch !important; }
  .align-self-sm-auto { align-self: auto !important; }
  .align-self-sm-start { align-self: flex-start !important; }
  .align-self-sm-end { align-self: flex-end !important; }
  .align-self-sm-center { align-self: center !important; }
  .align-self-sm-baseline { align-self: baseline !important; }
  .align-self-sm-stretch { align-self: stretch !important; }
  .order-sm-first { order: -1 !important; }
  .order-sm-0 { order: 0 !important; }
  .order-sm-1 { order: 1 !important; }
  .order-sm-2 { order: 2 !important; }
  .order-sm-3 { order: 3 !important; }
  .order-sm-4 { order: 4 !important; }
  .order-sm-5 { order: 5 !important; }
  .order-sm-last { order: 6 !important; }
  .m-sm-0 { margin: 0px !important; }
  .m-sm-1 { margin: 0.25rem !important; }
  .m-sm-2 { margin: 0.5rem !important; }
  .m-sm-3 { margin: 1rem !important; }
  .m-sm-4 { margin: 1.5rem !important; }
  .m-sm-5 { margin: 3rem !important; }
  .m-sm-auto { margin: auto !important; }
  .mx-sm-0 { margin-right: 0px !important; margin-left: 0px !important; }
  .mx-sm-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !import=
ant; }
  .mx-sm-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !importan=
t; }
  .mx-sm-3 { margin-right: 1rem !important; margin-left: 1rem !important; }
  .mx-sm-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !importan=
t; }
  .mx-sm-5 { margin-right: 3rem !important; margin-left: 3rem !important; }
  .mx-sm-auto { margin-right: auto !important; margin-left: auto !important=
; }
  .my-sm-0 { margin-top: 0px !important; margin-bottom: 0px !important; }
  .my-sm-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !import=
ant; }
  .my-sm-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !importan=
t; }
  .my-sm-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
  .my-sm-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !importan=
t; }
  .my-sm-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
  .my-sm-auto { margin-top: auto !important; margin-bottom: auto !important=
; }
  .mt-sm-0 { margin-top: 0px !important; }
  .mt-sm-1 { margin-top: 0.25rem !important; }
  .mt-sm-2 { margin-top: 0.5rem !important; }
  .mt-sm-3 { margin-top: 1rem !important; }
  .mt-sm-4 { margin-top: 1.5rem !important; }
  .mt-sm-5 { margin-top: 3rem !important; }
  .mt-sm-auto { margin-top: auto !important; }
  .me-sm-0 { margin-right: 0px !important; }
  .me-sm-1 { margin-right: 0.25rem !important; }
  .me-sm-2 { margin-right: 0.5rem !important; }
  .me-sm-3 { margin-right: 1rem !important; }
  .me-sm-4 { margin-right: 1.5rem !important; }
  .me-sm-5 { margin-right: 3rem !important; }
  .me-sm-auto { margin-right: auto !important; }
  .mb-sm-0 { margin-bottom: 0px !important; }
  .mb-sm-1 { margin-bottom: 0.25rem !important; }
  .mb-sm-2 { margin-bottom: 0.5rem !important; }
  .mb-sm-3 { margin-bottom: 1rem !important; }
  .mb-sm-4 { margin-bottom: 1.5rem !important; }
  .mb-sm-5 { margin-bottom: 3rem !important; }
  .mb-sm-auto { margin-bottom: auto !important; }
  .ms-sm-0 { margin-left: 0px !important; }
  .ms-sm-1 { margin-left: 0.25rem !important; }
  .ms-sm-2 { margin-left: 0.5rem !important; }
  .ms-sm-3 { margin-left: 1rem !important; }
  .ms-sm-4 { margin-left: 1.5rem !important; }
  .ms-sm-5 { margin-left: 3rem !important; }
  .ms-sm-auto { margin-left: auto !important; }
  .p-sm-0 { padding: 0px !important; }
  .p-sm-1 { padding: 0.25rem !important; }
  .p-sm-2 { padding: 0.5rem !important; }
  .p-sm-3 { padding: 1rem !important; }
  .p-sm-4 { padding: 1.5rem !important; }
  .p-sm-5 { padding: 3rem !important; }
  .px-sm-0 { padding-right: 0px !important; padding-left: 0px !important; }
  .px-sm-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !impo=
rtant; }
  .px-sm-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !import=
ant; }
  .px-sm-3 { padding-right: 1rem !important; padding-left: 1rem !important;=
 }
  .px-sm-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !import=
ant; }
  .px-sm-5 { padding-right: 3rem !important; padding-left: 3rem !important;=
 }
  .py-sm-0 { padding-top: 0px !important; padding-bottom: 0px !important; }
  .py-sm-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !impo=
rtant; }
  .py-sm-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !import=
ant; }
  .py-sm-3 { padding-top: 1rem !important; padding-bottom: 1rem !important;=
 }
  .py-sm-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !import=
ant; }
  .py-sm-5 { padding-top: 3rem !important; padding-bottom: 3rem !important;=
 }
  .pt-sm-0 { padding-top: 0px !important; }
  .pt-sm-1 { padding-top: 0.25rem !important; }
  .pt-sm-2 { padding-top: 0.5rem !important; }
  .pt-sm-3 { padding-top: 1rem !important; }
  .pt-sm-4 { padding-top: 1.5rem !important; }
  .pt-sm-5 { padding-top: 3rem !important; }
  .pe-sm-0 { padding-right: 0px !important; }
  .pe-sm-1 { padding-right: 0.25rem !important; }
  .pe-sm-2 { padding-right: 0.5rem !important; }
  .pe-sm-3 { padding-right: 1rem !important; }
  .pe-sm-4 { padding-right: 1.5rem !important; }
  .pe-sm-5 { padding-right: 3rem !important; }
  .pb-sm-0 { padding-bottom: 0px !important; }
  .pb-sm-1 { padding-bottom: 0.25rem !important; }
  .pb-sm-2 { padding-bottom: 0.5rem !important; }
  .pb-sm-3 { padding-bottom: 1rem !important; }
  .pb-sm-4 { padding-bottom: 1.5rem !important; }
  .pb-sm-5 { padding-bottom: 3rem !important; }
  .ps-sm-0 { padding-left: 0px !important; }
  .ps-sm-1 { padding-left: 0.25rem !important; }
  .ps-sm-2 { padding-left: 0.5rem !important; }
  .ps-sm-3 { padding-left: 1rem !important; }
  .ps-sm-4 { padding-left: 1.5rem !important; }
  .ps-sm-5 { padding-left: 3rem !important; }
  .text-sm-start { text-align: left !important; }
  .text-sm-end { text-align: right !important; }
  .text-sm-center { text-align: center !important; }
}

@media (min-width: 768px) {
  .float-md-start { float: left !important; }
  .float-md-end { float: right !important; }
  .float-md-none { float: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-grid { display: grid !important; }
  .d-md-table { display: table !important; }
  .d-md-table-row { display: table-row !important; }
  .d-md-table-cell { display: table-cell !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
  .d-md-none { display: none !important; }
  .flex-md-fill { flex: 1 1 auto !important; }
  .flex-md-row { flex-direction: row !important; }
  .flex-md-column { flex-direction: column !important; }
  .flex-md-row-reverse { flex-direction: row-reverse !important; }
  .flex-md-column-reverse { flex-direction: column-reverse !important; }
  .flex-md-grow-0 { flex-grow: 0 !important; }
  .flex-md-grow-1 { flex-grow: 1 !important; }
  .flex-md-shrink-0 { flex-shrink: 0 !important; }
  .flex-md-shrink-1 { flex-shrink: 1 !important; }
  .flex-md-wrap { flex-wrap: wrap !important; }
  .flex-md-nowrap { flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .gap-md-0 { gap: 0px !important; }
  .gap-md-1 { gap: 0.25rem !important; }
  .gap-md-2 { gap: 0.5rem !important; }
  .gap-md-3 { gap: 1rem !important; }
  .gap-md-4 { gap: 1.5rem !important; }
  .gap-md-5 { gap: 3rem !important; }
  .justify-content-md-start { justify-content: flex-start !important; }
  .justify-content-md-end { justify-content: flex-end !important; }
  .justify-content-md-center { justify-content: center !important; }
  .justify-content-md-between { justify-content: space-between !important; =
}
  .justify-content-md-around { justify-content: space-around !important; }
  .justify-content-md-evenly { justify-content: space-evenly !important; }
  .align-items-md-start { align-items: flex-start !important; }
  .align-items-md-end { align-items: flex-end !important; }
  .align-items-md-center { align-items: center !important; }
  .align-items-md-baseline { align-items: baseline !important; }
  .align-items-md-stretch { align-items: stretch !important; }
  .align-content-md-start { align-content: flex-start !important; }
  .align-content-md-end { align-content: flex-end !important; }
  .align-content-md-center { align-content: center !important; }
  .align-content-md-between { align-content: space-between !important; }
  .align-content-md-around { align-content: space-around !important; }
  .align-content-md-stretch { align-content: stretch !important; }
  .align-self-md-auto { align-self: auto !important; }
  .align-self-md-start { align-self: flex-start !important; }
  .align-self-md-end { align-self: flex-end !important; }
  .align-self-md-center { align-self: center !important; }
  .align-self-md-baseline { align-self: baseline !important; }
  .align-self-md-stretch { align-self: stretch !important; }
  .order-md-first { order: -1 !important; }
  .order-md-0 { order: 0 !important; }
  .order-md-1 { order: 1 !important; }
  .order-md-2 { order: 2 !important; }
  .order-md-3 { order: 3 !important; }
  .order-md-4 { order: 4 !important; }
  .order-md-5 { order: 5 !important; }
  .order-md-last { order: 6 !important; }
  .m-md-0 { margin: 0px !important; }
  .m-md-1 { margin: 0.25rem !important; }
  .m-md-2 { margin: 0.5rem !important; }
  .m-md-3 { margin: 1rem !important; }
  .m-md-4 { margin: 1.5rem !important; }
  .m-md-5 { margin: 3rem !important; }
  .m-md-auto { margin: auto !important; }
  .mx-md-0 { margin-right: 0px !important; margin-left: 0px !important; }
  .mx-md-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !import=
ant; }
  .mx-md-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !importan=
t; }
  .mx-md-3 { margin-right: 1rem !important; margin-left: 1rem !important; }
  .mx-md-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !importan=
t; }
  .mx-md-5 { margin-right: 3rem !important; margin-left: 3rem !important; }
  .mx-md-auto { margin-right: auto !important; margin-left: auto !important=
; }
  .my-md-0 { margin-top: 0px !important; margin-bottom: 0px !important; }
  .my-md-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !import=
ant; }
  .my-md-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !importan=
t; }
  .my-md-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
  .my-md-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !importan=
t; }
  .my-md-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
  .my-md-auto { margin-top: auto !important; margin-bottom: auto !important=
; }
  .mt-md-0 { margin-top: 0px !important; }
  .mt-md-1 { margin-top: 0.25rem !important; }
  .mt-md-2 { margin-top: 0.5rem !important; }
  .mt-md-3 { margin-top: 1rem !important; }
  .mt-md-4 { margin-top: 1.5rem !important; }
  .mt-md-5 { margin-top: 3rem !important; }
  .mt-md-auto { margin-top: auto !important; }
  .me-md-0 { margin-right: 0px !important; }
  .me-md-1 { margin-right: 0.25rem !important; }
  .me-md-2 { margin-right: 0.5rem !important; }
  .me-md-3 { margin-right: 1rem !important; }
  .me-md-4 { margin-right: 1.5rem !important; }
  .me-md-5 { margin-right: 3rem !important; }
  .me-md-auto { margin-right: auto !important; }
  .mb-md-0 { margin-bottom: 0px !important; }
  .mb-md-1 { margin-bottom: 0.25rem !important; }
  .mb-md-2 { margin-bottom: 0.5rem !important; }
  .mb-md-3 { margin-bottom: 1rem !important; }
  .mb-md-4 { margin-bottom: 1.5rem !important; }
  .mb-md-5 { margin-bottom: 3rem !important; }
  .mb-md-auto { margin-bottom: auto !important; }
  .ms-md-0 { margin-left: 0px !important; }
  .ms-md-1 { margin-left: 0.25rem !important; }
  .ms-md-2 { margin-left: 0.5rem !important; }
  .ms-md-3 { margin-left: 1rem !important; }
  .ms-md-4 { margin-left: 1.5rem !important; }
  .ms-md-5 { margin-left: 3rem !important; }
  .ms-md-auto { margin-left: auto !important; }
  .p-md-0 { padding: 0px !important; }
  .p-md-1 { padding: 0.25rem !important; }
  .p-md-2 { padding: 0.5rem !important; }
  .p-md-3 { padding: 1rem !important; }
  .p-md-4 { padding: 1.5rem !important; }
  .p-md-5 { padding: 3rem !important; }
  .px-md-0 { padding-right: 0px !important; padding-left: 0px !important; }
  .px-md-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !impo=
rtant; }
  .px-md-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !import=
ant; }
  .px-md-3 { padding-right: 1rem !important; padding-left: 1rem !important;=
 }
  .px-md-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !import=
ant; }
  .px-md-5 { padding-right: 3rem !important; padding-left: 3rem !important;=
 }
  .py-md-0 { padding-top: 0px !important; padding-bottom: 0px !important; }
  .py-md-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !impo=
rtant; }
  .py-md-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !import=
ant; }
  .py-md-3 { padding-top: 1rem !important; padding-bottom: 1rem !important;=
 }
  .py-md-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !import=
ant; }
  .py-md-5 { padding-top: 3rem !important; padding-bottom: 3rem !important;=
 }
  .pt-md-0 { padding-top: 0px !important; }
  .pt-md-1 { padding-top: 0.25rem !important; }
  .pt-md-2 { padding-top: 0.5rem !important; }
  .pt-md-3 { padding-top: 1rem !important; }
  .pt-md-4 { padding-top: 1.5rem !important; }
  .pt-md-5 { padding-top: 3rem !important; }
  .pe-md-0 { padding-right: 0px !important; }
  .pe-md-1 { padding-right: 0.25rem !important; }
  .pe-md-2 { padding-right: 0.5rem !important; }
  .pe-md-3 { padding-right: 1rem !important; }
  .pe-md-4 { padding-right: 1.5rem !important; }
  .pe-md-5 { padding-right: 3rem !important; }
  .pb-md-0 { padding-bottom: 0px !important; }
  .pb-md-1 { padding-bottom: 0.25rem !important; }
  .pb-md-2 { padding-bottom: 0.5rem !important; }
  .pb-md-3 { padding-bottom: 1rem !important; }
  .pb-md-4 { padding-bottom: 1.5rem !important; }
  .pb-md-5 { padding-bottom: 3rem !important; }
  .ps-md-0 { padding-left: 0px !important; }
  .ps-md-1 { padding-left: 0.25rem !important; }
  .ps-md-2 { padding-left: 0.5rem !important; }
  .ps-md-3 { padding-left: 1rem !important; }
  .ps-md-4 { padding-left: 1.5rem !important; }
  .ps-md-5 { padding-left: 3rem !important; }
  .text-md-start { text-align: left !important; }
  .text-md-end { text-align: right !important; }
  .text-md-center { text-align: center !important; }
}

@media (min-width: 992px) {
  .float-lg-start { float: left !important; }
  .float-lg-end { float: right !important; }
  .float-lg-none { float: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-grid { display: grid !important; }
  .d-lg-table { display: table !important; }
  .d-lg-table-row { display: table-row !important; }
  .d-lg-table-cell { display: table-cell !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
  .d-lg-none { display: none !important; }
  .flex-lg-fill { flex: 1 1 auto !important; }
  .flex-lg-row { flex-direction: row !important; }
  .flex-lg-column { flex-direction: column !important; }
  .flex-lg-row-reverse { flex-direction: row-reverse !important; }
  .flex-lg-column-reverse { flex-direction: column-reverse !important; }
  .flex-lg-grow-0 { flex-grow: 0 !important; }
  .flex-lg-grow-1 { flex-grow: 1 !important; }
  .flex-lg-shrink-0 { flex-shrink: 0 !important; }
  .flex-lg-shrink-1 { flex-shrink: 1 !important; }
  .flex-lg-wrap { flex-wrap: wrap !important; }
  .flex-lg-nowrap { flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .gap-lg-0 { gap: 0px !important; }
  .gap-lg-1 { gap: 0.25rem !important; }
  .gap-lg-2 { gap: 0.5rem !important; }
  .gap-lg-3 { gap: 1rem !important; }
  .gap-lg-4 { gap: 1.5rem !important; }
  .gap-lg-5 { gap: 3rem !important; }
  .justify-content-lg-start { justify-content: flex-start !important; }
  .justify-content-lg-end { justify-content: flex-end !important; }
  .justify-content-lg-center { justify-content: center !important; }
  .justify-content-lg-between { justify-content: space-between !important; =
}
  .justify-content-lg-around { justify-content: space-around !important; }
  .justify-content-lg-evenly { justify-content: space-evenly !important; }
  .align-items-lg-start { align-items: flex-start !important; }
  .align-items-lg-end { align-items: flex-end !important; }
  .align-items-lg-center { align-items: center !important; }
  .align-items-lg-baseline { align-items: baseline !important; }
  .align-items-lg-stretch { align-items: stretch !important; }
  .align-content-lg-start { align-content: flex-start !important; }
  .align-content-lg-end { align-content: flex-end !important; }
  .align-content-lg-center { align-content: center !important; }
  .align-content-lg-between { align-content: space-between !important; }
  .align-content-lg-around { align-content: space-around !important; }
  .align-content-lg-stretch { align-content: stretch !important; }
  .align-self-lg-auto { align-self: auto !important; }
  .align-self-lg-start { align-self: flex-start !important; }
  .align-self-lg-end { align-self: flex-end !important; }
  .align-self-lg-center { align-self: center !important; }
  .align-self-lg-baseline { align-self: baseline !important; }
  .align-self-lg-stretch { align-self: stretch !important; }
  .order-lg-first { order: -1 !important; }
  .order-lg-0 { order: 0 !important; }
  .order-lg-1 { order: 1 !important; }
  .order-lg-2 { order: 2 !important; }
  .order-lg-3 { order: 3 !important; }
  .order-lg-4 { order: 4 !important; }
  .order-lg-5 { order: 5 !important; }
  .order-lg-last { order: 6 !important; }
  .m-lg-0 { margin: 0px !important; }
  .m-lg-1 { margin: 0.25rem !important; }
  .m-lg-2 { margin: 0.5rem !important; }
  .m-lg-3 { margin: 1rem !important; }
  .m-lg-4 { margin: 1.5rem !important; }
  .m-lg-5 { margin: 3rem !important; }
  .m-lg-auto { margin: auto !important; }
  .mx-lg-0 { margin-right: 0px !important; margin-left: 0px !important; }
  .mx-lg-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !import=
ant; }
  .mx-lg-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !importan=
t; }
  .mx-lg-3 { margin-right: 1rem !important; margin-left: 1rem !important; }
  .mx-lg-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !importan=
t; }
  .mx-lg-5 { margin-right: 3rem !important; margin-left: 3rem !important; }
  .mx-lg-auto { margin-right: auto !important; margin-left: auto !important=
; }
  .my-lg-0 { margin-top: 0px !important; margin-bottom: 0px !important; }
  .my-lg-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !import=
ant; }
  .my-lg-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !importan=
t; }
  .my-lg-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
  .my-lg-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !importan=
t; }
  .my-lg-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
  .my-lg-auto { margin-top: auto !important; margin-bottom: auto !important=
; }
  .mt-lg-0 { margin-top: 0px !important; }
  .mt-lg-1 { margin-top: 0.25rem !important; }
  .mt-lg-2 { margin-top: 0.5rem !important; }
  .mt-lg-3 { margin-top: 1rem !important; }
  .mt-lg-4 { margin-top: 1.5rem !important; }
  .mt-lg-5 { margin-top: 3rem !important; }
  .mt-lg-auto { margin-top: auto !important; }
  .me-lg-0 { margin-right: 0px !important; }
  .me-lg-1 { margin-right: 0.25rem !important; }
  .me-lg-2 { margin-right: 0.5rem !important; }
  .me-lg-3 { margin-right: 1rem !important; }
  .me-lg-4 { margin-right: 1.5rem !important; }
  .me-lg-5 { margin-right: 3rem !important; }
  .me-lg-auto { margin-right: auto !important; }
  .mb-lg-0 { margin-bottom: 0px !important; }
  .mb-lg-1 { margin-bottom: 0.25rem !important; }
  .mb-lg-2 { margin-bottom: 0.5rem !important; }
  .mb-lg-3 { margin-bottom: 1rem !important; }
  .mb-lg-4 { margin-bottom: 1.5rem !important; }
  .mb-lg-5 { margin-bottom: 3rem !important; }
  .mb-lg-auto { margin-bottom: auto !important; }
  .ms-lg-0 { margin-left: 0px !important; }
  .ms-lg-1 { margin-left: 0.25rem !important; }
  .ms-lg-2 { margin-left: 0.5rem !important; }
  .ms-lg-3 { margin-left: 1rem !important; }
  .ms-lg-4 { margin-left: 1.5rem !important; }
  .ms-lg-5 { margin-left: 3rem !important; }
  .ms-lg-auto { margin-left: auto !important; }
  .p-lg-0 { padding: 0px !important; }
  .p-lg-1 { padding: 0.25rem !important; }
  .p-lg-2 { padding: 0.5rem !important; }
  .p-lg-3 { padding: 1rem !important; }
  .p-lg-4 { padding: 1.5rem !important; }
  .p-lg-5 { padding: 3rem !important; }
  .px-lg-0 { padding-right: 0px !important; padding-left: 0px !important; }
  .px-lg-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !impo=
rtant; }
  .px-lg-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !import=
ant; }
  .px-lg-3 { padding-right: 1rem !important; padding-left: 1rem !important;=
 }
  .px-lg-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !import=
ant; }
  .px-lg-5 { padding-right: 3rem !important; padding-left: 3rem !important;=
 }
  .py-lg-0 { padding-top: 0px !important; padding-bottom: 0px !important; }
  .py-lg-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !impo=
rtant; }
  .py-lg-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !import=
ant; }
  .py-lg-3 { padding-top: 1rem !important; padding-bottom: 1rem !important;=
 }
  .py-lg-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !import=
ant; }
  .py-lg-5 { padding-top: 3rem !important; padding-bottom: 3rem !important;=
 }
  .pt-lg-0 { padding-top: 0px !important; }
  .pt-lg-1 { padding-top: 0.25rem !important; }
  .pt-lg-2 { padding-top: 0.5rem !important; }
  .pt-lg-3 { padding-top: 1rem !important; }
  .pt-lg-4 { padding-top: 1.5rem !important; }
  .pt-lg-5 { padding-top: 3rem !important; }
  .pe-lg-0 { padding-right: 0px !important; }
  .pe-lg-1 { padding-right: 0.25rem !important; }
  .pe-lg-2 { padding-right: 0.5rem !important; }
  .pe-lg-3 { padding-right: 1rem !important; }
  .pe-lg-4 { padding-right: 1.5rem !important; }
  .pe-lg-5 { padding-right: 3rem !important; }
  .pb-lg-0 { padding-bottom: 0px !important; }
  .pb-lg-1 { padding-bottom: 0.25rem !important; }
  .pb-lg-2 { padding-bottom: 0.5rem !important; }
  .pb-lg-3 { padding-bottom: 1rem !important; }
  .pb-lg-4 { padding-bottom: 1.5rem !important; }
  .pb-lg-5 { padding-bottom: 3rem !important; }
  .ps-lg-0 { padding-left: 0px !important; }
  .ps-lg-1 { padding-left: 0.25rem !important; }
  .ps-lg-2 { padding-left: 0.5rem !important; }
  .ps-lg-3 { padding-left: 1rem !important; }
  .ps-lg-4 { padding-left: 1.5rem !important; }
  .ps-lg-5 { padding-left: 3rem !important; }
  .text-lg-start { text-align: left !important; }
  .text-lg-end { text-align: right !important; }
  .text-lg-center { text-align: center !important; }
}

@media (min-width: 1200px) {
  .float-xl-start { float: left !important; }
  .float-xl-end { float: right !important; }
  .float-xl-none { float: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-grid { display: grid !important; }
  .d-xl-table { display: table !important; }
  .d-xl-table-row { display: table-row !important; }
  .d-xl-table-cell { display: table-cell !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
  .d-xl-none { display: none !important; }
  .flex-xl-fill { flex: 1 1 auto !important; }
  .flex-xl-row { flex-direction: row !important; }
  .flex-xl-column { flex-direction: column !important; }
  .flex-xl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xl-column-reverse { flex-direction: column-reverse !important; }
  .flex-xl-grow-0 { flex-grow: 0 !important; }
  .flex-xl-grow-1 { flex-grow: 1 !important; }
  .flex-xl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xl-shrink-1 { flex-shrink: 1 !important; }
  .flex-xl-wrap { flex-wrap: wrap !important; }
  .flex-xl-nowrap { flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .gap-xl-0 { gap: 0px !important; }
  .gap-xl-1 { gap: 0.25rem !important; }
  .gap-xl-2 { gap: 0.5rem !important; }
  .gap-xl-3 { gap: 1rem !important; }
  .gap-xl-4 { gap: 1.5rem !important; }
  .gap-xl-5 { gap: 3rem !important; }
  .justify-content-xl-start { justify-content: flex-start !important; }
  .justify-content-xl-end { justify-content: flex-end !important; }
  .justify-content-xl-center { justify-content: center !important; }
  .justify-content-xl-between { justify-content: space-between !important; =
}
  .justify-content-xl-around { justify-content: space-around !important; }
  .justify-content-xl-evenly { justify-content: space-evenly !important; }
  .align-items-xl-start { align-items: flex-start !important; }
  .align-items-xl-end { align-items: flex-end !important; }
  .align-items-xl-center { align-items: center !important; }
  .align-items-xl-baseline { align-items: baseline !important; }
  .align-items-xl-stretch { align-items: stretch !important; }
  .align-content-xl-start { align-content: flex-start !important; }
  .align-content-xl-end { align-content: flex-end !important; }
  .align-content-xl-center { align-content: center !important; }
  .align-content-xl-between { align-content: space-between !important; }
  .align-content-xl-around { align-content: space-around !important; }
  .align-content-xl-stretch { align-content: stretch !important; }
  .align-self-xl-auto { align-self: auto !important; }
  .align-self-xl-start { align-self: flex-start !important; }
  .align-self-xl-end { align-self: flex-end !important; }
  .align-self-xl-center { align-self: center !important; }
  .align-self-xl-baseline { align-self: baseline !important; }
  .align-self-xl-stretch { align-self: stretch !important; }
  .order-xl-first { order: -1 !important; }
  .order-xl-0 { order: 0 !important; }
  .order-xl-1 { order: 1 !important; }
  .order-xl-2 { order: 2 !important; }
  .order-xl-3 { order: 3 !important; }
  .order-xl-4 { order: 4 !important; }
  .order-xl-5 { order: 5 !important; }
  .order-xl-last { order: 6 !important; }
  .m-xl-0 { margin: 0px !important; }
  .m-xl-1 { margin: 0.25rem !important; }
  .m-xl-2 { margin: 0.5rem !important; }
  .m-xl-3 { margin: 1rem !important; }
  .m-xl-4 { margin: 1.5rem !important; }
  .m-xl-5 { margin: 3rem !important; }
  .m-xl-auto { margin: auto !important; }
  .mx-xl-0 { margin-right: 0px !important; margin-left: 0px !important; }
  .mx-xl-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !import=
ant; }
  .mx-xl-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !importan=
t; }
  .mx-xl-3 { margin-right: 1rem !important; margin-left: 1rem !important; }
  .mx-xl-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !importan=
t; }
  .mx-xl-5 { margin-right: 3rem !important; margin-left: 3rem !important; }
  .mx-xl-auto { margin-right: auto !important; margin-left: auto !important=
; }
  .my-xl-0 { margin-top: 0px !important; margin-bottom: 0px !important; }
  .my-xl-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !import=
ant; }
  .my-xl-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !importan=
t; }
  .my-xl-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
  .my-xl-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !importan=
t; }
  .my-xl-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
  .my-xl-auto { margin-top: auto !important; margin-bottom: auto !important=
; }
  .mt-xl-0 { margin-top: 0px !important; }
  .mt-xl-1 { margin-top: 0.25rem !important; }
  .mt-xl-2 { margin-top: 0.5rem !important; }
  .mt-xl-3 { margin-top: 1rem !important; }
  .mt-xl-4 { margin-top: 1.5rem !important; }
  .mt-xl-5 { margin-top: 3rem !important; }
  .mt-xl-auto { margin-top: auto !important; }
  .me-xl-0 { margin-right: 0px !important; }
  .me-xl-1 { margin-right: 0.25rem !important; }
  .me-xl-2 { margin-right: 0.5rem !important; }
  .me-xl-3 { margin-right: 1rem !important; }
  .me-xl-4 { margin-right: 1.5rem !important; }
  .me-xl-5 { margin-right: 3rem !important; }
  .me-xl-auto { margin-right: auto !important; }
  .mb-xl-0 { margin-bottom: 0px !important; }
  .mb-xl-1 { margin-bottom: 0.25rem !important; }
  .mb-xl-2 { margin-bottom: 0.5rem !important; }
  .mb-xl-3 { margin-bottom: 1rem !important; }
  .mb-xl-4 { margin-bottom: 1.5rem !important; }
  .mb-xl-5 { margin-bottom: 3rem !important; }
  .mb-xl-auto { margin-bottom: auto !important; }
  .ms-xl-0 { margin-left: 0px !important; }
  .ms-xl-1 { margin-left: 0.25rem !important; }
  .ms-xl-2 { margin-left: 0.5rem !important; }
  .ms-xl-3 { margin-left: 1rem !important; }
  .ms-xl-4 { margin-left: 1.5rem !important; }
  .ms-xl-5 { margin-left: 3rem !important; }
  .ms-xl-auto { margin-left: auto !important; }
  .p-xl-0 { padding: 0px !important; }
  .p-xl-1 { padding: 0.25rem !important; }
  .p-xl-2 { padding: 0.5rem !important; }
  .p-xl-3 { padding: 1rem !important; }
  .p-xl-4 { padding: 1.5rem !important; }
  .p-xl-5 { padding: 3rem !important; }
  .px-xl-0 { padding-right: 0px !important; padding-left: 0px !important; }
  .px-xl-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !impo=
rtant; }
  .px-xl-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !import=
ant; }
  .px-xl-3 { padding-right: 1rem !important; padding-left: 1rem !important;=
 }
  .px-xl-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !import=
ant; }
  .px-xl-5 { padding-right: 3rem !important; padding-left: 3rem !important;=
 }
  .py-xl-0 { padding-top: 0px !important; padding-bottom: 0px !important; }
  .py-xl-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !impo=
rtant; }
  .py-xl-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !import=
ant; }
  .py-xl-3 { padding-top: 1rem !important; padding-bottom: 1rem !important;=
 }
  .py-xl-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !import=
ant; }
  .py-xl-5 { padding-top: 3rem !important; padding-bottom: 3rem !important;=
 }
  .pt-xl-0 { padding-top: 0px !important; }
  .pt-xl-1 { padding-top: 0.25rem !important; }
  .pt-xl-2 { padding-top: 0.5rem !important; }
  .pt-xl-3 { padding-top: 1rem !important; }
  .pt-xl-4 { padding-top: 1.5rem !important; }
  .pt-xl-5 { padding-top: 3rem !important; }
  .pe-xl-0 { padding-right: 0px !important; }
  .pe-xl-1 { padding-right: 0.25rem !important; }
  .pe-xl-2 { padding-right: 0.5rem !important; }
  .pe-xl-3 { padding-right: 1rem !important; }
  .pe-xl-4 { padding-right: 1.5rem !important; }
  .pe-xl-5 { padding-right: 3rem !important; }
  .pb-xl-0 { padding-bottom: 0px !important; }
  .pb-xl-1 { padding-bottom: 0.25rem !important; }
  .pb-xl-2 { padding-bottom: 0.5rem !important; }
  .pb-xl-3 { padding-bottom: 1rem !important; }
  .pb-xl-4 { padding-bottom: 1.5rem !important; }
  .pb-xl-5 { padding-bottom: 3rem !important; }
  .ps-xl-0 { padding-left: 0px !important; }
  .ps-xl-1 { padding-left: 0.25rem !important; }
  .ps-xl-2 { padding-left: 0.5rem !important; }
  .ps-xl-3 { padding-left: 1rem !important; }
  .ps-xl-4 { padding-left: 1.5rem !important; }
  .ps-xl-5 { padding-left: 3rem !important; }
  .text-xl-start { text-align: left !important; }
  .text-xl-end { text-align: right !important; }
  .text-xl-center { text-align: center !important; }
}

@media (min-width: 1400px) {
  .float-xxl-start { float: left !important; }
  .float-xxl-end { float: right !important; }
  .float-xxl-none { float: none !important; }
  .d-xxl-inline { display: inline !important; }
  .d-xxl-inline-block { display: inline-block !important; }
  .d-xxl-block { display: block !important; }
  .d-xxl-grid { display: grid !important; }
  .d-xxl-table { display: table !important; }
  .d-xxl-table-row { display: table-row !important; }
  .d-xxl-table-cell { display: table-cell !important; }
  .d-xxl-flex { display: flex !important; }
  .d-xxl-inline-flex { display: inline-flex !important; }
  .d-xxl-none { display: none !important; }
  .flex-xxl-fill { flex: 1 1 auto !important; }
  .flex-xxl-row { flex-direction: row !important; }
  .flex-xxl-column { flex-direction: column !important; }
  .flex-xxl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xxl-column-reverse { flex-direction: column-reverse !important; }
  .flex-xxl-grow-0 { flex-grow: 0 !important; }
  .flex-xxl-grow-1 { flex-grow: 1 !important; }
  .flex-xxl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xxl-shrink-1 { flex-shrink: 1 !important; }
  .flex-xxl-wrap { flex-wrap: wrap !important; }
  .flex-xxl-nowrap { flex-wrap: nowrap !important; }
  .flex-xxl-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .gap-xxl-0 { gap: 0px !important; }
  .gap-xxl-1 { gap: 0.25rem !important; }
  .gap-xxl-2 { gap: 0.5rem !important; }
  .gap-xxl-3 { gap: 1rem !important; }
  .gap-xxl-4 { gap: 1.5rem !important; }
  .gap-xxl-5 { gap: 3rem !important; }
  .justify-content-xxl-start { justify-content: flex-start !important; }
  .justify-content-xxl-end { justify-content: flex-end !important; }
  .justify-content-xxl-center { justify-content: center !important; }
  .justify-content-xxl-between { justify-content: space-between !important;=
 }
  .justify-content-xxl-around { justify-content: space-around !important; }
  .justify-content-xxl-evenly { justify-content: space-evenly !important; }
  .align-items-xxl-start { align-items: flex-start !important; }
  .align-items-xxl-end { align-items: flex-end !important; }
  .align-items-xxl-center { align-items: center !important; }
  .align-items-xxl-baseline { align-items: baseline !important; }
  .align-items-xxl-stretch { align-items: stretch !important; }
  .align-content-xxl-start { align-content: flex-start !important; }
  .align-content-xxl-end { align-content: flex-end !important; }
  .align-content-xxl-center { align-content: center !important; }
  .align-content-xxl-between { align-content: space-between !important; }
  .align-content-xxl-around { align-content: space-around !important; }
  .align-content-xxl-stretch { align-content: stretch !important; }
  .align-self-xxl-auto { align-self: auto !important; }
  .align-self-xxl-start { align-self: flex-start !important; }
  .align-self-xxl-end { align-self: flex-end !important; }
  .align-self-xxl-center { align-self: center !important; }
  .align-self-xxl-baseline { align-self: baseline !important; }
  .align-self-xxl-stretch { align-self: stretch !important; }
  .order-xxl-first { order: -1 !important; }
  .order-xxl-0 { order: 0 !important; }
  .order-xxl-1 { order: 1 !important; }
  .order-xxl-2 { order: 2 !important; }
  .order-xxl-3 { order: 3 !important; }
  .order-xxl-4 { order: 4 !important; }
  .order-xxl-5 { order: 5 !important; }
  .order-xxl-last { order: 6 !important; }
  .m-xxl-0 { margin: 0px !important; }
  .m-xxl-1 { margin: 0.25rem !important; }
  .m-xxl-2 { margin: 0.5rem !important; }
  .m-xxl-3 { margin: 1rem !important; }
  .m-xxl-4 { margin: 1.5rem !important; }
  .m-xxl-5 { margin: 3rem !important; }
  .m-xxl-auto { margin: auto !important; }
  .mx-xxl-0 { margin-right: 0px !important; margin-left: 0px !important; }
  .mx-xxl-1 { margin-right: 0.25rem !important; margin-left: 0.25rem !impor=
tant; }
  .mx-xxl-2 { margin-right: 0.5rem !important; margin-left: 0.5rem !importa=
nt; }
  .mx-xxl-3 { margin-right: 1rem !important; margin-left: 1rem !important; =
}
  .mx-xxl-4 { margin-right: 1.5rem !important; margin-left: 1.5rem !importa=
nt; }
  .mx-xxl-5 { margin-right: 3rem !important; margin-left: 3rem !important; =
}
  .mx-xxl-auto { margin-right: auto !important; margin-left: auto !importan=
t; }
  .my-xxl-0 { margin-top: 0px !important; margin-bottom: 0px !important; }
  .my-xxl-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !impor=
tant; }
  .my-xxl-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !importa=
nt; }
  .my-xxl-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; =
}
  .my-xxl-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !importa=
nt; }
  .my-xxl-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; =
}
  .my-xxl-auto { margin-top: auto !important; margin-bottom: auto !importan=
t; }
  .mt-xxl-0 { margin-top: 0px !important; }
  .mt-xxl-1 { margin-top: 0.25rem !important; }
  .mt-xxl-2 { margin-top: 0.5rem !important; }
  .mt-xxl-3 { margin-top: 1rem !important; }
  .mt-xxl-4 { margin-top: 1.5rem !important; }
  .mt-xxl-5 { margin-top: 3rem !important; }
  .mt-xxl-auto { margin-top: auto !important; }
  .me-xxl-0 { margin-right: 0px !important; }
  .me-xxl-1 { margin-right: 0.25rem !important; }
  .me-xxl-2 { margin-right: 0.5rem !important; }
  .me-xxl-3 { margin-right: 1rem !important; }
  .me-xxl-4 { margin-right: 1.5rem !important; }
  .me-xxl-5 { margin-right: 3rem !important; }
  .me-xxl-auto { margin-right: auto !important; }
  .mb-xxl-0 { margin-bottom: 0px !important; }
  .mb-xxl-1 { margin-bottom: 0.25rem !important; }
  .mb-xxl-2 { margin-bottom: 0.5rem !important; }
  .mb-xxl-3 { margin-bottom: 1rem !important; }
  .mb-xxl-4 { margin-bottom: 1.5rem !important; }
  .mb-xxl-5 { margin-bottom: 3rem !important; }
  .mb-xxl-auto { margin-bottom: auto !important; }
  .ms-xxl-0 { margin-left: 0px !important; }
  .ms-xxl-1 { margin-left: 0.25rem !important; }
  .ms-xxl-2 { margin-left: 0.5rem !important; }
  .ms-xxl-3 { margin-left: 1rem !important; }
  .ms-xxl-4 { margin-left: 1.5rem !important; }
  .ms-xxl-5 { margin-left: 3rem !important; }
  .ms-xxl-auto { margin-left: auto !important; }
  .p-xxl-0 { padding: 0px !important; }
  .p-xxl-1 { padding: 0.25rem !important; }
  .p-xxl-2 { padding: 0.5rem !important; }
  .p-xxl-3 { padding: 1rem !important; }
  .p-xxl-4 { padding: 1.5rem !important; }
  .p-xxl-5 { padding: 3rem !important; }
  .px-xxl-0 { padding-right: 0px !important; padding-left: 0px !important; =
}
  .px-xxl-1 { padding-right: 0.25rem !important; padding-left: 0.25rem !imp=
ortant; }
  .px-xxl-2 { padding-right: 0.5rem !important; padding-left: 0.5rem !impor=
tant; }
  .px-xxl-3 { padding-right: 1rem !important; padding-left: 1rem !important=
; }
  .px-xxl-4 { padding-right: 1.5rem !important; padding-left: 1.5rem !impor=
tant; }
  .px-xxl-5 { padding-right: 3rem !important; padding-left: 3rem !important=
; }
  .py-xxl-0 { padding-top: 0px !important; padding-bottom: 0px !important; =
}
  .py-xxl-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !imp=
ortant; }
  .py-xxl-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !impor=
tant; }
  .py-xxl-3 { padding-top: 1rem !important; padding-bottom: 1rem !important=
; }
  .py-xxl-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !impor=
tant; }
  .py-xxl-5 { padding-top: 3rem !important; padding-bottom: 3rem !important=
; }
  .pt-xxl-0 { padding-top: 0px !important; }
  .pt-xxl-1 { padding-top: 0.25rem !important; }
  .pt-xxl-2 { padding-top: 0.5rem !important; }
  .pt-xxl-3 { padding-top: 1rem !important; }
  .pt-xxl-4 { padding-top: 1.5rem !important; }
  .pt-xxl-5 { padding-top: 3rem !important; }
  .pe-xxl-0 { padding-right: 0px !important; }
  .pe-xxl-1 { padding-right: 0.25rem !important; }
  .pe-xxl-2 { padding-right: 0.5rem !important; }
  .pe-xxl-3 { padding-right: 1rem !important; }
  .pe-xxl-4 { padding-right: 1.5rem !important; }
  .pe-xxl-5 { padding-right: 3rem !important; }
  .pb-xxl-0 { padding-bottom: 0px !important; }
  .pb-xxl-1 { padding-bottom: 0.25rem !important; }
  .pb-xxl-2 { padding-bottom: 0.5rem !important; }
  .pb-xxl-3 { padding-bottom: 1rem !important; }
  .pb-xxl-4 { padding-bottom: 1.5rem !important; }
  .pb-xxl-5 { padding-bottom: 3rem !important; }
  .ps-xxl-0 { padding-left: 0px !important; }
  .ps-xxl-1 { padding-left: 0.25rem !important; }
  .ps-xxl-2 { padding-left: 0.5rem !important; }
  .ps-xxl-3 { padding-left: 1rem !important; }
  .ps-xxl-4 { padding-left: 1.5rem !important; }
  .ps-xxl-5 { padding-left: 3rem !important; }
  .text-xxl-start { text-align: left !important; }
  .text-xxl-end { text-align: right !important; }
  .text-xxl-center { text-align: center !important; }
}

@media (min-width: 1200px) {
  .fs-1 { font-size: 2.5rem !important; }
  .fs-2 { font-size: 2rem !important; }
  .fs-3 { font-size: 1.75rem !important; }
  .fs-4 { font-size: 1.5rem !important; }
}

@media print {
  .d-print-inline { display: inline !important; }
  .d-print-inline-block { display: inline-block !important; }
  .d-print-block { display: block !important; }
  .d-print-grid { display: grid !important; }
  .d-print-table { display: table !important; }
  .d-print-table-row { display: table-row !important; }
  .d-print-table-cell { display: table-cell !important; }
  .d-print-flex { display: flex !important; }
  .d-print-inline-flex { display: inline-flex !important; }
  .d-print-none { display: none !important; }
}
------MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.loading { display: none; text-align: center; margin: 20px 0px; }

.result-table { margin-top: 20px; }

.criterion-card { margin-bottom: 20px; border: 1px solid rgb(222, 226, 230)=
; border-radius: 8px; }

.criterion-header { background-color: rgb(248, 249, 250); padding: 15px; bo=
rder-bottom: 1px solid rgb(222, 226, 230); }

.criterion-content { padding: 15px; }

.result-badge { font-size: 0.9em; padding: 5px 10px; }

.result-=E7=AC=A6=E5=90=88 { background-color: rgb(212, 237, 218); color: r=
gb(21, 87, 36); }

.result-=E5=9F=BA=E6=9C=AC=E7=AC=A6=E5=90=88 { background-color: rgb(255, 2=
43, 205); color: rgb(133, 100, 4); }

.result-=E4=B8=8D=E7=AC=A6=E5=90=88 { background-color: rgb(248, 215, 218);=
 color: rgb(114, 28, 36); }

.result-=E4=B8=8D=E9=80=82=E7=94=A8 { background-color: rgb(226, 227, 229);=
 color: rgb(56, 61, 65); }

.section-detail { font-size: 0.9em; margin-top: 10px; }

.statistics-card { background-color: rgb(248, 249, 250); border-radius: 8px=
; padding: 20px; margin-bottom: 20px; }

.debug-panel { background-color: rgb(248, 249, 250); border: 1px solid rgb(=
222, 226, 230); border-radius: 8px; padding: 15px; margin-bottom: 20px; max=
-height: 400px; overflow-y: auto; }

.debug-log { font-family: "Courier New", monospace; font-size: 0.85em; whit=
e-space: pre-wrap; margin: 0px; }

.progress-item { padding: 5px 0px; border-bottom: 1px solid rgb(238, 238, 2=
38); }

.progress-item:last-child { border-bottom: none; }

.timestamp { color: rgb(108, 117, 125); font-size: 0.8em; }
------MultipartBoundary--4Cgn9iuIjWZm7xEJZSoWk5MElU8f08ClNy78ijarO1------
