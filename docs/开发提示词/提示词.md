# 分章节评审的一致性问题
我要实现报告评审助手，根据审查细则用大模型服务对可信性研究报告进行评审，评审标准相关文档有：可研报告的审查指南、可研报告的编制大纲。 由于大模型上下文有限，而报告内容很长，因此采用分章节方式进行评审,即每次把1个章节的内容和所有评审相关文档发给大模型服务进行评审。但是章节之间的内容或数据一致性如何检查？比如第1章提到损耗率=0.2， 第5章写的是0.3，分章节调用大模型如何检查这种一致性？

# 报告专题管理功能
增加一个报告专题管理功能，要求:
1.每个报告专题包括：审查指南、审查细则、可研编制大纲。
2.上传报告时，可以指定一个报告专题。
3.对可研报告评审时，根据所属的报告专题，系统自动加载该专题的审查指南、审查细则、可研编制大纲。
4.前端UI优化：增加菜单管理，包括报告专题管理菜单、可研报告管理
  4.1 报告专题管理菜单：可以添加、修改、删除报告专题
  4.2 报告管理菜单：可以查看、修改、删除报告，可以指定所属的报告专题，查询过滤报告专题下的报告。
  4.3 报告评审菜单：可以选择报告进行评审，可研查看、修改、删除报告评审记录。
5.报告评审结果暂时以json格式保存, 报告可选择重新评审。
6.专题管理的元数据也暂时用文件系统json格式存储到当前data/目录，后续考虑用数据库存储。
## 优化1
1. 报告专题增加日期元数据
2. 界面首页显示最新专题的所有报告评审的汇总报告，并支持下拉框选择其他报告专题，并切换为该报告专题的报告评审汇总报告。
3. 每个专题的可研报告评审汇总结果也是通过大模型服务分析：同样按照每个评审细则，总结每个报告的评审结果，并输出总体评审报告。
4. 如果专题还有报告未评审，可提示用户，是否继续。
5. 每个专题的总体报告可以重新生成报告。
## 优化2
1. 上传报告时，报告名称可以由文件名称自动提取
2. 报告管理界面，报告列表中显示报告的专题名称
3. 评审管理界面，可选择多个报告进行批量评审
4. 评审管理界面，报告专题的下拉过滤框在前
## 问题 
1.解决汇总分析生成失败: 'ModelService' object has no attribute 'call_model'
2.汇总分析由大模型对多个报告的评审结果总结分析:按评审细则，展示每个细则各个报告该细则的评审总结，测试模式可模拟输出总结报告.
3.评审管理界面的多报告评审，不使用“选择多个报告进行批量评审”这个展示区，可以在下面的各报告列表加选择框，选择多个报告进行批量评审。
