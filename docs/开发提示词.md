# 分章节评审的一致性问题
我要实现报告评审助手，根据审查细则用大模型服务对可信性研究报告进行评审，评审标准相关文档有：可研报告的审查指南、可研报告的编制大纲。 由于大模型上下文有限，而报告内容很长，因此采用分章节方式进行评审,即每次把1个章节的内容和所有评审相关文档发给大模型服务进行评审。但是章节之间的内容或数据一致性如何检查？比如第1章提到损耗率=0.2， 第5章写的是0.3，分章节调用大模型如何检查这种一致性？

# 报告专题管理功能
增加一个报告专题管理功能，要求:
1.每个报告专题包括：审查指南、审查细则、可研编制大纲。
2.上传报告时，可以指定一个报告专题。
3.对可研报告评审时，根据所属的报告专题，系统自动加载该专题的审查指南、审查细则、可研编制大纲。
4.前端UI优化：增加菜单管理，包括报告专题管理菜单、可研报告管理
  4.1 报告专题管理菜单：可以添加、修改、删除报告专题
  4.2 报告管理菜单：可以查看、修改、删除报告，可以指定所属的报告专题，查询过滤报告专题下的报告。
  4.3 报告评审菜单：可以选择报告进行评审，可研查看、修改、删除报告评审记录。
5.报告评审结果暂时以json格式保存, 报告可选择重新评审。
6.专题管理的元数据也暂时用文件系统json格式存储到当前data/目录，后续考虑用数据库存储。
## 优化1
1. 报告专题增加日期元数据
2. 界面首页显示最新专题的所有报告评审的汇总报告，并支持下拉框选择其他报告专题，并切换为该报告专题的报告评审汇总报告。
3. 每个专题的可研报告评审汇总结果也是通过大模型服务分析：同样按照每个评审细则，总结每个报告的评审结果，并输出总体评审报告。
4. 如果专题还有报告未评审，可提示用户，是否继续。
5. 每个专题的总体报告可以重新生成报告。
## 优化2
1. 上传报告时，报告名称可以由文件名称自动提取
2. 报告管理界面，报告列表中显示报告的专题名称
3. 评审管理界面，可选择多个报告进行批量评审
4. 评审管理界面，报告专题的下拉过滤框在前
## 问题 
1.解决汇总分析生成失败: 'ModelService' object has no attribute 'call_model'
2.汇总分析由大模型对多个报告的评审结果总结分析:按评审细则，展示每个细则各个报告该细则的评审总结，测试模式可模拟输出总结报告.
3.评审管理界面的多报告评审，不使用“选择多个报告进行批量评审”这个展示区，可以在下面的各报告列表加选择框，选择多个报告进行批量评审。
## 优化3
1.将“报告专题下拉框”做为全局过滤条件，并放置在顶端菜单栏的最右侧。各菜单下的报告都是按照这个全局专题过滤（专题管理菜单除外）。
2.第一个菜单“报告评审”修改为“汇总报告”
3."汇总报告"先用二维表格，分细则展示各报告的评审结果。如下：
 | 编号 | 评审细则 | 报告简称 |
 | 1.1  |  35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。 | 基本符合 |
 | 1.2  |  建设内容是否涉及用户工程、迁改工程等用户出资内容。 | 未提供可研汇总表 | 
 最后再展示所有报告的总体分析情况。
 报告简称可以提取自报告中的县市的名称，如：1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告，提取的简称为：横州市。
 如果报告是某个具体的项目则提取项目名做为简称，如：1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市第二发电厂）可行性研究报告，提取的简称为：横州市第二发电厂。

 ## 优化4
 1. 前端界面报告管理和评审管理菜单下，去掉单独的“专题过滤”，而是使用菜单栏的专题下拉框做为过滤条件。
 2. "评审管理"菜单界面，去掉单个报告的“开始新评审”区域，而是可以直接在下方报告列表的每个报告处，通过“开始评审”按钮开始报告评审。并且当报告已经评审过，“开始评审”按钮可以变为“重新评审”（如现在所示）。

# 报告内容管理优化
1.报告内容在第一次解析之后，以json格式存储，后期使用直接从json文件读取。删除报告时同时删除解析的报告json文件。报告内容的json格式：{"report_name": "报告名称","short_name":"简称",
 "report_date": "报告日期","size":"报告大小（字数）","sections":[{"section_name": "章节名称", "section_content": "章节内容"}] }
2.报告编制大纲也同样在解析之后，以json 格式存储，后期使用直接从json文件读取。
3.审查指南文档同样在解析之后，存储为json格式，后期使用直接从json文件读取。
4.审查细则 文档同样在解析之后，存储为json格式，后期使用直接从json文件读取。

# 评审管理优化
1./review刷新页面时，已经评审过的报告会显示为”未评审“的状态，但是选择专题过滤后，报告列表中的报告又是已评审状态。解决此问题。
2.在每个报告的评审结果详情页，以二维表格形式展现该报告各评审细则的审查情况，如下：
 | 编号 | 审查范畴  | 评审细则 | 审查情况 | 复核情况
 | 1.1  |资料齐备性 |  35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。 | 基本符合 | 符合
 | 1.2  |项目来源合规性 |  建设内容是否涉及用户工程、迁改工程等用户出资内容。 | 未提供可研汇总表 | 符合

 其中审查情况为第一次评审的结果，复核情况为重新评审的结果，多次重新评审只记录最后一次。

 # 优化
 1.增加逻辑判定：当某个专题下有报告时，不能删除专题。
 2.删除报告时，同时删除其对应的评审报告。
 3.删除主题时，同时删除该主题对应的汇总报告
 4.在“报告管理”界面，每个报告项增加报告内容预览功能，可按章节预览报告的内容。
 5.在“专题管理”界面，增加可研报告编制大纲预览功能，可按章节预览大纲的内容。
 6.在“专题管理”界面，增加审查指南预览功能，可预览审查指南的内容。
 7.在“专题管理”界面，增加审查细则预览功能，可以二维表格预览审查细则的内容。
 8.界面/reviews链接，下拉框默认显示第一个专题，报告列表也按照这个专题过滤，并取消“报告过滤”的下拉框。
 # 问题
 1. 前端界面的审查细则预览功能，审查范畴是undefined。
 2. 报告预览功能的，各章节内容是"该章节内容为空"
 3. 刚切换到评审管理界面时，报告列表是空的，只用下拉框选择之后才会显示出报告。
 4. 评审结果详情页面，没有以二维表格形式展现该报告各评审细则的审查情况，请优化我如下样式：
 | 编号 | 审查范畴  | 评审细则 | 审查情况 | 复核情况
 | 1.1  |资料齐备性 |  35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。 | 基本符合 | 符合
 | 1.2  |项目来源合规性 |  建设内容是否涉及用户工程、迁改工程等用户出资内容。 | 未提供可研汇总表 | 符合
 # 问题
 继续解决报告预览的章节内容为空的问题
 # 优化
 1.报告预览时如果报告还未解析，可调用：document_parser.py的parse_pdf解析pdf文档，并保存为json格式。
 2.报告评审的方法def analyze(self, pdf_path: str, debug_callback=None)优化为：提取报告的json格式数据，如果没有则解析pdf。
 3.注意：报告预览和报告评审功能都涉及pdf解析后存储为json格式，需要优化为一个“提取报告内容”的方法，该方法可查询json内容，没有则解析pdf并保存。
 # 优化
 优化单个报告评审结果的界面展示：1.不需要考虑数据结构兼容情况，按照review.json的结构展示 2.审查情况部分参考图片的展示，先给出overall_assessment结果值（不同颜色区分），然后是全文分析、改进建议。该审查细则的各章节详细评审情况也是如图所示，默认折叠，点击展开。3.适当调整审查结果的宽度，审查细则宽度可缩短，复核情况展示：overall_assessment和全文分析部分，宽度适当调整。

# 问题(人工解决)
1 document_parser.py的章节匹配方法 _identify_chapter_title优化: 如果是以1个或多个#开始，并且文字内容在self.standard_outline中完全匹配，则认为是章节标题。

# 优化
1. "评审结果汇总表"界面，分为2个tab显示：一个是当前的各审查细则各县市的审查情况，另一个是"专题下拉框"过滤的所有可研报告审查情况的总结报告。
2. "评审结果汇总表"的各县市的列结果显示最后一次评审的结果，如果是“不符合”应显示该审查细则的全文分析情况及建议。
3. "总结报告"同样由大模型辅助生成（可优化TopicSummaryService的提示词），输入请求是该专题各个可研报告评审结果的comprehensive_analysis部分、overall_assessment、key_findings、recommendations及summary内容。
4. "总结报告"支持重新生成。

## 新的问题
1. “不符合”项，点击“详情”未展开
2. “总结报告"的内容是markdown格式，需转换为视觉优化的格式，如html。

# 重构  
  document_parser.py的self.standard_outline根据传入的编制大纲加载，不同专题有不同的编制大纲。
  report_analyzer.py 也重构为根据专题的编制大纲、审查细则加载

 # 省份管理
