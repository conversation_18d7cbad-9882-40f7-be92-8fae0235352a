<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评审管理 - 可研报告评审助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .review-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .review-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .review-content {
            padding: 15px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .stats-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .review-result-cell {
            font-size: 0.9em;
        }
        .review-result-cell .badge {
            font-size: 0.8em;
            margin-bottom: 5px;
        }
        .review-result-cell strong {
            font-size: 0.85em;
            color: #495057;
        }
        .section-detail-table {
            background-color: #f8f9fa;
        }
        .collapse-button {
            transition: all 0.3s ease;
        }
        .collapse-button:hover {
            background-color: #e9ecef;
        }
        .section-detail-row {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">汇总报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/reviews">评审管理</a>
                    </li>
                </ul>
                <!-- 全局专题过滤器 -->
                <div class="d-flex align-items-center">
                    <label class="text-white me-2" style="font-size: 0.9em;">专题:</label>
                    <select class="form-select form-select-sm" id="globalTopicFilter" onchange="onGlobalTopicChange()" style="width: 200px;">
                        <option value="">所有专题</option>
                    </select>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>评审管理</h1>
        </div>



        <!-- 加载动画 -->
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">评审中...</span>
            </div>
            <p class="mt-2">正在评审报告，请稍候...</p>
        </div>

        <!-- 报告列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">报告列表</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="selectAllReports()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllReports()">
                        <i class="fas fa-square"></i> 清空
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="startBatchReview()">
                        <i class="fas fa-tasks"></i> 批量评审
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="generateMultiReportSummary()">
                        <i class="fas fa-chart-line"></i> 汇总分析
                    </button>
                    <span id="selectedCount" class="ms-3 text-muted">已选择 0 个报告</span>
                </div>
            </div>
            <div class="card-body">
                <div id="reportsList">
                    <!-- 报告卡片将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 评审结果详情模态框 -->
    <div class="modal fade" id="reviewDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">评审结果详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reviewDetailContent">
                        <!-- 评审详情内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allReviews = [];
        let allReports = [];
        let allTopics = [];
        let selectedReports = new Set(); // 存储选中的报告ID

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTopics().then(() => {
                loadReports();
                loadReviews();
            });
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    allTopics = result.data;
                    populateGlobalTopicFilter();
                    return true;
                }
            } catch (error) {
                console.error('加载专题列表失败:', error);
            }
            return false;
        }

        // 填充全局专题过滤器
        function populateGlobalTopicFilter() {
            const select = document.getElementById('globalTopicFilter');
            select.innerHTML = '<option value="">所有专题</option>';

            allTopics.forEach(topic => {
                const option = document.createElement('option');
                option.value = topic.id;
                option.textContent = topic.name;
                select.appendChild(option);
            });

            // 默认选择第一个专题
            if (allTopics.length > 0) {
                select.value = allTopics[0].id;
                onGlobalTopicChange();
            }
        }

        // 全局专题变化处理
        function onGlobalTopicChange() {
            const globalTopicId = document.getElementById('globalTopicFilter').value;
            filterReports();
        }

        // 加载报告列表
        async function loadReports() {
            if (allReports.length > 0) {
                return;
            }
            try {
                const response = await fetch('/api/reports');
                const result = await response.json();

                if (result.success) {
                    allReports = result.data;
                    filterReports(); // 根据当前专题过滤显示报告
                }
            } catch (error) {
                console.error('加载报告列表失败:', error);
            }
        }

        // 加载评审记录
        async function loadReviews() {
            try {
                const response = await fetch('/api/reviews');
                const result = await response.json();

                if (result.success) {
                    allReviews = result.data;
                    // displayReviews(allReviews);
                } else {
                    alert('加载评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('加载评审记录失败: ' + error.message);
            }
        }

        // 过滤报告
        function filterReports() {
            const topicId = document.getElementById('globalTopicFilter').value;
            let filteredReports = allReports;

            if (allReports.length === 0) {
                loadReports().then(() => {
                    // 重新获取 allReports 数据后，再次执行过滤操作
                    filteredReports = allReports.filter(report => !topicId || report.topic_id === topicId);
                    displayReports(filteredReports);
                });
            }
            if (topicId) {
                filteredReports = filteredReports.filter(report => report.topic_id === topicId);
            }

            displayReports(filteredReports);
        }

        // 显示报告列表
        function displayReports(reports) {
            const container = document.getElementById('reportsList');

            if (reports.length === 0) {
                container.innerHTML = '<div class="text-center text-muted"><p>暂无报告。</p></div>';
                return;
            }

            container.innerHTML = reports.map(report => {
                const topic = allTopics.find(t => t.id === report.topic_id);
                const topicName = topic ? topic.name : '未分类';

                // 使用API返回的评审状态信息
                const hasReview = report.review_status === 'reviewed';
                const review = hasReview ? allReviews.find(r => r.report_id === report.id) : null;

                // 获取统计信息
                const stats = review?.result?.statistics || {};
                const complianceRate = stats.compliance_rate || 0;
                const totalCriteria = stats.total_criteria || 0;

                const isSelected = selectedReports.has(report.id);

                return `
                    <div class="review-card">
                        <div class="review-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" class="form-check-input me-3"
                                           ${isSelected ? 'checked' : ''}
                                           onchange="toggleReportSelection('${report.id}')">
                                    <div>
                                        <h5 class="mb-1">${report.name}</h5>
                                        <span class="badge bg-secondary status-badge">${topicName}</span>
                                        ${hasReview ? '<span class="badge bg-success status-badge ms-2">已评审</span>' : '<span class="badge bg-warning status-badge ms-2">未评审</span>'}
                                    </div>
                                </div>
                                <div>
                                    ${hasReview ? `
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReviewDetail('${review.id}')">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="startReviewForReport('${report.id}')">
                                            <i class="fas fa-redo"></i> 重新评审
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReview('${review.id}')">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    ` : `
                                        <button type="button" class="btn btn-sm btn-success" onclick="startReviewForReport('${report.id}')">
                                            <i class="fas fa-play"></i> 开始评审
                                        </button>
                                    `}
                                </div>
                            </div>
                        </div>
                        ${hasReview ? `
                            <div class="review-content">
                                <div class="stats-info">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>合规率:</strong> ${complianceRate}%
                                        </div>
                                        <div class="col-md-3">
                                            <strong>总审查细则:</strong> ${totalCriteria}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 开始评审（针对特定报告）
        async function startReviewForReport(reportId) {
            if (!reportId) {
                alert('报告ID无效');
                return;
            }

            // 检查是否已有评审记录
            const existingReview = allReviews.find(r => r.report_id === reportId);
            if (existingReview) {
                if (!confirm('该报告已有评审记录，确定要重新评审吗？')) {
                    return;
                }
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch(`/api/reviews/${reportId}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审完成');
                    loadReviews(); // 重新加载评审记录
                    displayReports(allReports); // 重新显示报告列表
                } else {
                    alert('评审失败: ' + result.error);
                }
            } catch (error) {
                alert('评审失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 查看评审详情
        async function viewReviewDetail(reviewId) {
            try {
                const response = await fetch(`/api/reviews/${reviewId}`);
                const result = await response.json();

                if (result.success) {
                    const review = result.data;
                    const report = allReports.find(r => r.id === review.report_id);
                    const topic = allTopics.find(t => t.id === review.topic_id);

                    displayReviewDetail(review, report, topic);
                    new bootstrap.Modal(document.getElementById('reviewDetailModal')).show();
                } else {
                    alert('获取评审详情失败: ' + result.error);
                }
            } catch (error) {
                alert('获取评审详情失败: ' + error.message);
            }
        }
        // 结论徽章样式辅助函数
        function getConclusionBadgeClass(conclusion) {
            switch(conclusion) {
                case '符合': return 'bg-success';
                case '基本符合': return 'bg-warning text-dark';
                case '不符合': return 'bg-danger';
                case '不适用': return 'bg-secondary';
                default: return 'bg-light text-dark';
            }
        }

        // 显示评审详情
        function displayReviewDetail(review, report, topic) {
            const container = document.getElementById('reviewDetailContent');
            const result = review.result;
            const stats = result.statistics || {};

            let content = `
                <div class="mb-4">
                    <h6>基本信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>报告名称:</strong> ${report ? report.name : '未知报告'}</p>
                            <p><strong>所属专题:</strong> ${topic ? topic.name : '未分类'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}</p>
                            <p><strong>评审状态:</strong> ${review.status}</p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h6>统计信息</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-primary">${stats.total_criteria || 0}</h5>
                                <small>总审查细则</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-success">${(stats.result_distribution?.['符合'] || 0) + (stats.result_distribution?.['基本符合'] || 0)}</h5>
                                <small>符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-danger">${stats.result_distribution?.['不符合'] || 0}</h5>
                                <small>不符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-info">${stats.compliance_rate || 0}%</h5>
                                <small>合规率</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加审查细则详情表格
            const criteriaData = result.review_results || result.criteria_analysis || result.criteria_results || [];
            if (criteriaData && criteriaData.length > 0) {
                content += `
                    <div class="mb-4">
                        <h6>审查细则详情</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 8%">编号</th>
                                        <th style="width: 12%">审查范畴</th>
                                        <th style="width: 30%">评审细则</th>
                                        <th style="width: 25%">审查情况</th>
                                        <th style="width: 25%">复核情况</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateCriteriaTable(criteriaData, report.id)}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            if (result.summary) {
                // 解析数据结构
                const summaryData = typeof result.summary === 'string' ? JSON.parse(result.summary) : result.summary;
                
                content += `
                    <div class="mb-4">
                        <h6 class="mb-3">总体评审意见</h6>
                        
                        <!-- 总体结论与合规率 -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="card border-light h-100">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-3 text-muted">评审结论</h6>
                                        <span class="badge ${getConclusionBadgeClass(summaryData.overall_conclusion)} fs-6">
                                            ${summaryData.overall_conclusion}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-light h-100">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-3 text-muted">合规率</h6>
                                        <div class="display-6 text-primary">${summaryData.compliance_rate}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主要问题 -->
                        <div class="card border-light mb-4">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-danger">主要问题</h6>
                                <ul class="list-group list-group-flush">
                                    ${summaryData.major_issues.map(issue => `
                                        <li class="list-group-item border-0 ps-0">
                                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                            ${issue}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>

                        <!-- 改进建议 -->
                        <div class="card border-light mb-4">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-success">改进建议</h6>
                                <ul class="list-group list-group-flush">
                                    ${summaryData.improvement_suggestions.map(suggestion => `
                                        <li class="list-group-item border-0 ps-0">
                                            <i class="fas fa-lightbulb text-success me-2"></i>
                                            ${suggestion}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>

                        <!-- 总结文本 -->
                        <div class="card border-light bg-light">
                            <div class="card-body">
                                <p class="card-text">${summaryData.summary_text.replace(/\n/g, '<br>')}</p>
                            </div>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = content;
        }

        // 生成审查细则表格
        function generateCriteriaTable(criteriaResults, reportId) {
            // 获取该报告的所有评审记录，用于对比首次评审和复核情况
            const reportReviews = allReviews.filter(r => r.report_id === reportId);
            reportReviews.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

            let tableRows = '';

            // 处理不同的数据结构 - 直接使用review_results格式
            let processedCriteria = [];

            // 检查数据结构类型
            if (criteriaResults && criteriaResults.length > 0) {
                const firstItem = criteriaResults[0];

                // 如果是review_results格式（当前数据格式）
                if (firstItem.sections && firstItem.overall_assessment) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_content || '',
                        category: getCriterionCategory(criterion.criterion_id),
                        overall_assessment: criterion.overall_assessment || '',
                        comprehensive_analysis: criterion.comprehensive_analysis || '',
                        key_findings: criterion.key_findings || [],
                        recommendations: criterion.recommendations || [],
                        sections: criterion.sections || []
                    }));
                }
                // 兼容其他格式
                else if (firstItem.section_results || firstItem.overall_result) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_content,
                        category: criterion.category || '',
                        overall_assessment: criterion.overall_result || '',
                        comprehensive_analysis: criterion.comprehensive_analysis || '',
                        key_findings: criterion.key_findings || [],
                        recommendations: criterion.recommendations || [],
                        sections: criterion.sections || []
                    }));
                }
                else if (firstItem.criterion_text || firstItem.result) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_text || criterion.criterion_content || '',
                        category: criterion.category || '',
                        overall_assessment: criterion.result || '',
                        comprehensive_analysis: criterion.analysis || criterion.explanation || '',
                        key_findings: [],
                        recommendations: [],
                        sections: []
                    }));
                }
            }

            processedCriteria.forEach((criterion, index) => {
                const criterionId = criterion.criterion_id;

                // 获取首次评审结果和最新评审结果
                let firstCriterion = null;
                let latestCriterion = null;

                if (reportReviews.length > 0) {
                    // 首次评审结果
                    const firstReview = reportReviews[0];
                    firstCriterion = findCriterionInReview(firstReview, criterionId);

                    // 最新评审结果（如果有多次评审）
                    if (reportReviews.length > 1) {
                        const latestReview = reportReviews[reportReviews.length - 1];
                        latestCriterion = findCriterionInReview(latestReview, criterionId);
                    } else {
                        // 只有一次评审时，复核情况为空
                    }
                }

                // 生成章节详情的唯一ID
                const sectionDetailId = `section-detail-${reportId}-${criterionId.replace('.', '-')}`;

                tableRows += `
                    <tr>
                        <td style="width: 8%">${criterion.criterion_id}</td>
                        <td style="width: 12%">${criterion.category}</td>
                        <td style="width: 30%" class="text-wrap">${criterion.criterion_content}</td>
                        <td style="width: 25%">
                            ${generateReviewResultCell(firstCriterion, sectionDetailId)}
                        </td>
                        <td style="width: 25%">
                            ${generateReviewResultCell(latestCriterion, null, true)}
                        </td>
                    </tr>
                    ${generateSectionDetailRow(criterion, sectionDetailId)}
                `;
            });

            return tableRows;
        }

        // 获取审查细则分类
        function getCriterionCategory(criterionId) {
            const categoryMap = {
                '1.': '报告编制',
                '2.': '项目来源',
                '3.': '建设内容',
                '4.': '项目必要性',
                '5.': '建设方案',
                '6.': '投资估算',
                '7.': '经济评价',
                '8.': '风险分析'
            };

            for (const [prefix, category] of Object.entries(categoryMap)) {
                if (criterionId.startsWith(prefix)) {
                    return category;
                }
            }
            return '其他';
        }

        // 构建评审结果显示内容
        function buildReviewResultDisplay(criterion) {
            return criterion.overall_assessment + (criterion.comprehensive_analysis ? ': ' + criterion.comprehensive_analysis : '');
        }

        // 获取结果状态的CSS类
        function getStatusClass(result) {
            if (result.includes('符合') && !result.includes('不符合')) {
                return 'text-success';
            } else if (result.includes('基本符合')) {
                return 'text-warning';
            } else if (result.includes('不符合')) {
                return 'text-danger';
            } else if (result.includes('不适用')) {
                return 'text-muted';
            }
            return '';
        }

        // 生成评审结果单元格
        function generateReviewResultCell(criterion, sectionDetailId = null, isRecheck = false) {
            if (criterion === null) {
                return '';
            }
            const assessment = criterion.overall_assessment || '';
            const analysis = criterion.comprehensive_analysis || '';
            const findings = criterion.key_findings || [];
            const recommendations = criterion.recommendations || [];

            let content = `
                <div class="review-result-cell">
                    <div class="mb-2">
                        <span class="badge ${getStatusBadgeClass(assessment)}">${assessment}</span>
                    </div>
            `;

            if (analysis) {
                content += `
                    <div class="mb-2">
                        <strong>全文分析：</strong>
                        <div class="text-muted small">${analysis}</div>
                    </div>
                `;
            }

            if (!isRecheck && recommendations.length > 0) {
                content += `
                    <div class="mb-2">
                        <strong>改进建议：</strong>
                        <ul class="small text-muted mb-0">
                            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (!isRecheck && sectionDetailId && criterion.sections && criterion.sections.length > 0) {
                content += `
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-info collapse-button" type="button"
                                data-bs-toggle="collapse" data-bs-target="#${sectionDetailId}"
                                onclick="toggleChevron(this)">
                            <i class="fas fa-chevron-down"></i> 查看各章节详细评审情况 (${criterion.sections.length}个相关章节)
                        </button>
                    </div>
                `;
            }

            content += '</div>';
            return content;
        }

        // 获取状态徽章的CSS类
        function getStatusBadgeClass(assessment) {
            if (assessment.includes('符合') && !assessment.includes('不符合')) {
                return 'bg-success';
            } else if (assessment.includes('基本符合')) {
                return 'bg-warning';
            } else if (assessment.includes('不符合')) {
                return 'bg-danger';
            } else if (assessment.includes('不适用')) {
                return 'bg-secondary';
            }
            return 'bg-light text-dark';
        }

        // 生成章节详情行
        function generateSectionDetailRow(criterion, sectionDetailId) {
            if (!criterion.sections || criterion.sections.length === 0) {
                return '';
            }

            let sectionContent = `
                <tr>
                    <td colspan="5" class="p-0">
                        <div class="collapse" id="${sectionDetailId}">
                            <div class="card card-body m-2">
                                <h6 class="mb-3">各章节详细评审情况</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 25%">章节名称</th>
                                                <th style="width: 15%">评审结果</th>
                                                <th style="width: 60%">说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
            `;

            criterion.sections.forEach(section => {
                sectionContent += `
                    <tr>
                        <td>${section.section_name}</td>
                        <td>
                            <span class="badge ${getStatusBadgeClass(section.result)}">${section.result}</span>
                        </td>
                        <td class="small">${section.explanation || ''}</td>
                    </tr>
                `;
            });

            sectionContent += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            `;

            return sectionContent;
        }

        // 在评审记录中查找指定的审查细则
        function findCriterionInReview(review, criterionId) {
            const result = review.result;

            // 检查不同的数据结构
            if (result.criteria_analysis) {
                return result.criteria_analysis.find(c => c.criterion_id === criterionId);
            }
            if (result.criteria_results) {
                return result.criteria_results.find(c => c.criterion_id === criterionId);
            }
            if (result.review_results) {
                return result.review_results.find(c => c.criterion_id === criterionId);
            }

            return null;
        }

        // 从审查细则对象中提取结果文本
        function extractCriterionResult(criterion) {
            if (criterion.overall_result) {
                return criterion.overall_result + (criterion.comprehensive_analysis ? ': ' + criterion.comprehensive_analysis : '');
            }
            if (criterion.result) {
                return criterion.result + (criterion.analysis || criterion.explanation ? ': ' + (criterion.analysis || criterion.explanation) : '');
            }
            if (criterion.analysis_result) {
                return criterion.analysis_result;
            }

            return '无结果';
        }

        // 切换报告选择状态
        function toggleReportSelection(reportId) {
            if (selectedReports.has(reportId)) {
                selectedReports.delete(reportId);
            } else {
                selectedReports.add(reportId);
            }
            updateSelectedCount();
            displayReviews(allReviews); // 重新渲染以更新选择状态
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = `已选择 ${selectedReports.size} 个报告`;
            }
        }

        // 全选报告
        function selectAllReports() {
            // 获取当前显示的报告卡片对应的报告ID
            const currentReports = document.querySelectorAll('.review-card input[type="checkbox"]');
            currentReports.forEach(checkbox => {
                const reportId = checkbox.getAttribute('onchange').match(/'([^']+)'/)[1];
                selectedReports.add(reportId);
            });
            displayReports(allReports); // 重新渲染以更新选择状态
        }

        // 清空选择
        function clearAllReports() {
            selectedReports.clear();
            displayReports(allReports); // 重新渲染以更新选择状态
        }

        // 生成多报告汇总分析
        async function generateMultiReportSummary() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行汇总分析');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行汇总分析吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch('/api/multi-report-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_ids: Array.from(selectedReports)
                    })
                });
                const result = await response.json();

                if (result.success) {
                    alert('汇总分析完成！');
                    // 可以在这里添加显示汇总结果的逻辑
                    console.log('汇总分析结果:', result.data);

                    // 清空选择
                    clearAllReports();
                } else {
                    alert('汇总分析失败: ' + result.error);
                }
            } catch (error) {
                alert('汇总分析失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 批量评审
        async function startBatchReview() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行评审');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行批量评审吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            let successCount = 0;
            let failCount = 0;
            const totalCount = selectedReports.size;

            for (const reportId of selectedReports) {
                try {
                    const response = await fetch(`/api/reviews/${reportId}`, {
                        method: 'POST'
                    });
                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error(`评审报告 ${reportId} 失败:`, result.error);
                    }
                } catch (error) {
                    failCount++;
                    console.error(`评审报告 ${reportId} 失败:`, error.message);
                }
            }

            document.querySelector('.loading').style.display = 'none';

            alert(`批量评审完成！\n成功: ${successCount} 个\n失败: ${failCount} 个\n总计: ${totalCount} 个`);

            // 清空选择并刷新列表
            clearAllReports();
            loadReviews();
            displayReports(allReports);
        }

        // 切换箭头图标
        function toggleChevron(button) {
            const icon = button.querySelector('i');
            setTimeout(() => {
                if (icon.classList.contains('fa-chevron-down')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }, 100);
        }

        // 删除评审记录
        async function deleteReview(reviewId) {
            if (!confirm('确定要删除这条评审记录吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/api/reviews/${reviewId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审记录删除成功');
                    loadReviews();
                    displayReports(allReports);
                } else {
                    alert('删除评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('删除评审记录失败: ' + error.message);
            }
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
