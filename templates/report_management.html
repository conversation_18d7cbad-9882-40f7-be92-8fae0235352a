<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告管理 - 可研报告评审助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .report-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .report-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .report-content {
            padding: 15px;
        }
        .file-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .topic-badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">报告评审</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reviews">评审管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>报告管理</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadReportModal">
                <i class="fas fa-upload"></i> 上传报告
            </button>
        </div>

        <!-- 过滤器 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <select class="form-select" id="topicFilter" onchange="filterReports()">
                    <option value="">所有专题</option>
                </select>
            </div>
        </div>

        <!-- 报告列表 -->
        <div id="reportsList">
            <!-- 报告卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 上传报告模态框 -->
    <div class="modal fade" id="uploadReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadReportForm">
                        <div class="mb-3">
                            <label for="reportFile" class="form-label">选择PDF文件 *</label>
                            <input type="file" class="form-control" id="reportFile" accept=".pdf" required onchange="autoFillReportName()">
                        </div>
                        <div class="mb-3">
                            <label for="reportName" class="form-label">报告名称 *</label>
                            <input type="text" class="form-control" id="reportName" required>
                        </div>
                        <div class="mb-3">
                            <label for="reportTopic" class="form-label">所属专题</label>
                            <select class="form-select" id="reportTopic">
                                <option value="">请选择专题</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="reportDescription" class="form-label">报告描述</label>
                            <textarea class="form-control" id="reportDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="uploadReport()">上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑报告模态框 -->
    <div class="modal fade" id="editReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editReportForm">
                        <input type="hidden" id="editReportId">
                        <div class="mb-3">
                            <label for="editReportName" class="form-label">报告名称 *</label>
                            <input type="text" class="form-control" id="editReportName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editReportTopic" class="form-label">所属专题</label>
                            <select class="form-select" id="editReportTopic">
                                <option value="">请选择专题</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editReportDescription" class="form-label">报告描述</label>
                            <textarea class="form-control" id="editReportDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateReport()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allReports = [];
        let allTopics = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTopics();
            loadReports();
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    allTopics = result.data;
                    populateTopicSelects();
                }
            } catch (error) {
                console.error('加载专题列表失败:', error);
            }
        }

        // 填充专题选择框
        function populateTopicSelects() {
            const selects = ['topicFilter', 'reportTopic', 'editReportTopic'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (selectId === 'topicFilter') {
                    select.innerHTML = '<option value="">所有专题</option>';
                } else {
                    select.innerHTML = '<option value="">请选择专题</option>';
                }

                allTopics.forEach(topic => {
                    const option = document.createElement('option');
                    option.value = topic.id;
                    option.textContent = topic.name;
                    select.appendChild(option);
                });
            });
        }

        // 加载报告列表
        async function loadReports() {
            try {
                const response = await fetch('/api/reports');
                const result = await response.json();

                if (result.success) {
                    allReports = result.data;
                    displayReports(allReports);
                } else {
                    alert('加载报告列表失败: ' + result.error);
                }
            } catch (error) {
                alert('加载报告列表失败: ' + error.message);
            }
        }

        // 过滤报告
        function filterReports() {
            const topicId = document.getElementById('topicFilter').value;
            let filteredReports = allReports;

            if (topicId) {
                filteredReports = allReports.filter(report => report.topic_id === topicId);
            }

            displayReports(filteredReports);
        }

        // 显示报告列表
        function displayReports(reports) {
            const container = document.getElementById('reportsList');

            if (reports.length === 0) {
                container.innerHTML = '<div class="text-center text-muted"><p>暂无报告，请点击"上传报告"添加第一个报告。</p></div>';
                return;
            }

            container.innerHTML = reports.map(report => {
                const topic = allTopics.find(t => t.id === report.topic_id);
                const topicName = topic ? topic.name : '未分类';
                const fileSize = (report.file_size / 1024 / 1024).toFixed(2);

                return `
                    <div class="report-card">
                        <div class="report-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">${report.name}</h5>
                                    <span class="badge bg-secondary topic-badge">${topicName}</span>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="reviewReport('${report.id}')">
                                        <i class="fas fa-search"></i> 评审
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editReport('${report.id}')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReport('${report.id}', '${report.name}')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="report-content">
                            <p class="mb-2">${report.description || '无描述'}</p>
                            <div class="file-info">
                                <div><strong>文件大小:</strong> ${fileSize} MB</div>
                                <div><strong>上传时间:</strong> ${new Date(report.created_at).toLocaleString()}</div>
                                <div><strong>文件路径:</strong> ${report.file_path}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 上传报告
        async function uploadReport() {
            const formData = new FormData();
            const fileInput = document.getElementById('reportFile');

            if (!fileInput.files[0]) {
                alert('请选择PDF文件');
                return;
            }

            formData.append('pdf_file', fileInput.files[0]);
            formData.append('name', document.getElementById('reportName').value);
            formData.append('topic_id', document.getElementById('reportTopic').value);
            formData.append('description', document.getElementById('reportDescription').value);

            try {
                const response = await fetch('/api/reports', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert('报告上传成功');
                    bootstrap.Modal.getInstance(document.getElementById('uploadReportModal')).hide();
                    document.getElementById('uploadReportForm').reset();
                    loadReports();
                } else {
                    alert('上传报告失败: ' + result.error);
                }
            } catch (error) {
                alert('上传报告失败: ' + error.message);
            }
        }

        // 编辑报告
        async function editReport(reportId) {
            const report = allReports.find(r => r.id === reportId);
            if (report) {
                document.getElementById('editReportId').value = report.id;
                document.getElementById('editReportName').value = report.name;
                document.getElementById('editReportTopic').value = report.topic_id || '';
                document.getElementById('editReportDescription').value = report.description || '';

                new bootstrap.Modal(document.getElementById('editReportModal')).show();
            }
        }

        // 更新报告
        async function updateReport() {
            const reportId = document.getElementById('editReportId').value;
            const formData = new FormData();
            formData.append('name', document.getElementById('editReportName').value);
            formData.append('topic_id', document.getElementById('editReportTopic').value);
            formData.append('description', document.getElementById('editReportDescription').value);

            try {
                const response = await fetch(`/api/reports/${reportId}`, {
                    method: 'PUT',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert('报告更新成功');
                    bootstrap.Modal.getInstance(document.getElementById('editReportModal')).hide();
                    loadReports();
                } else {
                    alert('更新报告失败: ' + result.error);
                }
            } catch (error) {
                alert('更新报告失败: ' + error.message);
            }
        }

        // 删除报告
        async function deleteReport(reportId, reportName) {
            if (!confirm(`确定要删除报告"${reportName}"吗？此操作不可恢复。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/reports/${reportId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    alert('报告删除成功');
                    loadReports();
                } else {
                    alert('删除报告失败: ' + result.error);
                }
            } catch (error) {
                alert('删除报告失败: ' + error.message);
            }
        }

        // 自动填充报告名称
        function autoFillReportName() {
            const fileInput = document.getElementById('reportFile');
            const nameInput = document.getElementById('reportName');

            if (fileInput.files[0]) {
                const fileName = fileInput.files[0].name;
                // 移除文件扩展名
                const nameWithoutExt = fileName.replace(/\.[^/.]+$/, "");
                nameInput.value = nameWithoutExt;
            }
        }

        // 评审报告
        function reviewReport(reportId) {
            // 跳转到评审页面，并传递报告ID
            window.location.href = `/reviews?report_id=${reportId}`;
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
